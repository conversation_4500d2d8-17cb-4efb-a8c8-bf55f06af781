/**
 * @OTA_RENDERER 多订单UI渲染器
 * 🏷️ 标签: @OTA_MULTI_ORDER_RENDERER
 * 📝 说明: 负责面板生成和DOM操作，管理多订单界面显示
 * ⚠️ 警告: 已注册，请勿重复开发
 * <AUTHOR>
 * @version 1.0.0
 */

// 防止重复加载
if (window.OTA && window.OTA.MultiOrderRenderer) {
    console.log('多订单UI渲染器已存在，跳过重复加载');
} else {

/**
 * 多订单UI渲染器类
 * 负责所有UI相关的操作和显示
 */
class MultiOrderRenderer {
    constructor(config = {}) {
        this.config = {
            panelId: config.panelId || 'multiOrderPanel',
            listSelector: config.listSelector || '.multi-order-list',
            ...config
        };
        
        this.logger = this.getLogger();
        this.isDragging = false;
        this.dragState = { currentX: 0, currentY: 0, initialX: 0, initialY: 0 };
    }

    /**
     * 获取日志服务
     * @returns {Object} 日志服务实例
     */
    getLogger() {
        return window.getLogger?.() || {
            log: console.log.bind(console),
            logError: console.error.bind(console)
        };
    }

    /**
     * 显示多订单面板
     * @param {Array} orders - 完整解析的订单对象数组
     */
    showMultiOrderPanel(orders) {
        console.log('🔄 showMultiOrderPanel开始执行', { orders: orders, ordersLength: orders?.length });
        this.logger?.log('🔄 开始显示多订单浮窗面板...', 'info');
        
        const multiOrderPanel = document.getElementById(this.config.panelId);
        console.log('🔍 multiOrderPanel元素检查:', {
            panel: multiOrderPanel,
            exists: !!multiOrderPanel,
            id: multiOrderPanel?.id,
            className: multiOrderPanel?.className
        });
        
        if (!multiOrderPanel) {
            console.error('❌ 多订单面板元素不存在');
            this.logger?.log('❌ 多订单面板元素不存在', 'error');
            return;
        }

        // 显示前状态检查
        this.logger?.log('🔍 面板显示前状态检查:', 'info', {
            ordersCount: orders?.length || 0,
            panelExists: !!multiOrderPanel,
            currentClasses: multiOrderPanel.className,
            currentDisplay: multiOrderPanel.style.display,
            isHidden: multiOrderPanel.classList.contains('hidden')
        });

        this.logger?.log(`📋 准备显示${orders.length}个订单`, 'info');

        // 更新面板内容
        this.logger?.log('🔧 更新面板内容...', 'info');
        console.log('🔧 即将调用updatePanelContent');
        try {
            this.updatePanelContent(orders);
            console.log('✅ updatePanelContent调用完成');
        } catch (updateError) {
            console.error('❌ updatePanelContent执行失败:', updateError);
            this.logger?.logError('updatePanelContent执行失败', updateError);
            throw updateError;
        }
        
        // 显示浮窗面板
        this.logger?.log('👁️ 显示浮窗面板...', 'info');
        console.log('🔧 移除hidden类并显示面板...');
        
        // 确保面板可见
        multiOrderPanel.classList.remove('hidden');
        multiOrderPanel.style.display = 'flex';
        
        console.log('✅ 面板已显示', { 
            className: multiOrderPanel.className,
            display: multiOrderPanel.style.display,
            visible: multiOrderPanel.offsetHeight > 0,
            zIndex: window.getComputedStyle(multiOrderPanel).zIndex
        });

        // 确保面板可见和居中
        this.ensurePanelVisible();

        // 启用浮窗增强功能
        console.log('🔧 启用拖拽功能...');
        this.addPanelDragFeature();
        console.log('✅ 浮窗增强功能启用完成');

        console.log('🎉 showMultiOrderPanel执行完全成功', {
            ordersCount: orders.length,
            finalPanelState: {
                className: multiOrderPanel.className,
                display: multiOrderPanel.style.display,
                offsetHeight: multiOrderPanel.offsetHeight,
                offsetWidth: multiOrderPanel.offsetWidth
            }
        });
        this.logger?.log(`✅ 多订单浮窗面板已显示，包含${orders.length}个完整订单`, 'success');
    }

    /**
     * 隐藏多订单面板
     */
    hideMultiOrderPanel() {
        const multiOrderPanel = document.getElementById(this.config.panelId);
        if (multiOrderPanel) {
            // 简化隐藏逻辑
            multiOrderPanel.classList.add('hidden');
            multiOrderPanel.style.display = 'none';
            
            console.log('✅ 多订单面板已隐藏', {
                className: multiOrderPanel.className,
                display: multiOrderPanel.style.display
            });
            
            this.logger?.log('🚪 多订单浮窗面板已关闭', 'info');
        }
    }

    /**
     * 更新面板内容
     * @param {Array} orders - 完整解析的订单对象数组
     */
    updatePanelContent(orders) {
        console.log('🔧 updatePanelContent开始执行', { orders: orders, ordersLength: orders?.length });
        
        const orderList = document.querySelector(`#${this.config.panelId} ${this.config.listSelector}`);
        console.log('🔍 orderList容器检查:', {
            orderList: orderList,
            exists: !!orderList,
            className: orderList?.className
        });
        
        if (!orderList) {
            console.error('❌ 多订单列表容器不存在');
            this.logger?.log('多订单列表容器不存在', 'warn');
            return;
        }

        try {
            // 生成订单项HTML
            console.log('🔧 开始生成订单项HTML...');
            const orderItemsHTML = orders.map((order, index) => {
                return this.generateOrderCardHTML(order, index);
            }).join('');
            console.log('✅ 订单项HTML生成完成', { orderItemsCount: orders.length });

            console.log('🔧 更新orderList.innerHTML...');
            orderList.innerHTML = orderItemsHTML;
            console.log('✅ orderList.innerHTML更新完成');

            // 绑定选择框事件
            console.log('🔧 绑定选择框事件...');
            this.bindOrderSelectionEvents(orderList);
            console.log('✅ 选择框事件绑定完成');

            // 更新统计信息
            console.log('🔧 更新统计信息...');
            this.updateOrderStats(orders.length);
            console.log('✅ 统计信息更新完成');
            
            console.log('🎉 updatePanelContent执行完全成功');
            
        } catch (error) {
            console.error('❌ updatePanelContent执行过程中出错:', error);
            this.logger?.logError('updatePanelContent执行失败', error);
            throw error;
        }
    }

    /**
     * 生成订单卡片HTML
     * @param {Object} order - 订单对象
     * @param {number} index - 订单索引
     * @returns {string} 订单卡片HTML
     */
    generateOrderCardHTML(order, index) {
        const orderId = `order-${index}`;
        
        // 生成订单摘要
        const summary = this.generateOrderSummary(order, index);
        
        // 检查是否为Paging订单
        const isPagingOrder = order.is_paging_order || order.carTypeId === 34;
        const pagingBadge = isPagingOrder ? '<span class="paging-badge">🏷️ 举牌</span>' : '';
        
        return `
            <div class="order-card ${isPagingOrder ? 'paging-order' : ''}" data-order-index="${index}" 
                 onclick="window.OTA.multiOrderManager.toggleOrderSelection(${index})" 
                 style="cursor: pointer;">
                <div class="order-card-header">
                    <div class="order-selector">
                        <input type="checkbox" id="${orderId}" checked class="order-checkbox">
                        <div class="order-title">
                            <span class="order-number">订单 ${index + 1}</span>
                            ${pagingBadge}
                        </div>
                    </div>
                    <div class="order-status">
                        <span class="status-badge status-parsed">已解析</span>
                    </div>
                </div>
                <div class="order-card-body" onclick="window.OTA.multiOrderManager.quickEditOrder(${index}); event.stopPropagation();">
                    ${summary}
                </div>
            </div>
        `;
    }

    /**
     * 生成订单摘要信息
     * @param {Object} order - 订单对象
     * @param {number} index - 订单索引
     * @returns {string} 摘要HTML
     */
    generateOrderSummary(order, index = 0) {
        const currency = order.currency || 'MYR';
        const price = order.otaPrice ? parseFloat(order.otaPrice).toFixed(2) : '0.00';

        return `
            <div class="order-summary compact-inline-layout">
                <div class="grid-item editable-field" data-field="customerName" onclick="window.OTA.multiOrderManager.editField(${index}, 'customerName')">
                    <span class="grid-value">${order.customerName || '未提供'}</span>
                </div>
                <div class="grid-item editable-field" data-field="customerContact" onclick="window.OTA.multiOrderManager.editField(${index}, 'customerContact')">
                    <span class="grid-value">${this.formatPhone(order.customerContact)}</span>
                </div>
                <div class="grid-item grid-item-route" data-field="route" onclick="window.OTA.multiOrderManager.editField(${index}, 'route')">
                    <div class="grid-value">${this.formatRoute(order)}</div>
                </div>
                <div class="grid-item editable-field" data-field="pickupDate" onclick="window.OTA.multiOrderManager.editField(${index}, 'pickupDate')">
                    <span class="grid-value">${order.pickupDate || '未指定'}</span>
                </div>
                <div class="grid-item editable-field" data-field="pickupTime" onclick="window.OTA.multiOrderManager.editField(${index}, 'pickupTime')">
                    <span class="grid-value">${order.pickupTime || '未指定'}</span>
                </div>
                <div class="grid-item editable-field" data-field="passengerCount" onclick="window.OTA.multiOrderManager.editField(${index}, 'passengerCount')">
                    <span class="grid-value">${order.passengerCount || 1}人</span>
                </div>
                <div class="grid-item editable-field" data-field="luggageCount" onclick="window.OTA.multiOrderManager.editField(${index}, 'luggageCount')">
                    <span class="grid-value">${order.luggageCount || 0}件</span>
                </div>
                <div class="grid-item editable-field" data-field="price" onclick="window.OTA.multiOrderManager.editField(${index}, 'price')">
                    <span class="grid-value">${currency}${price}</span>
                </div>
                <div class="grid-item editable-field" data-field="vehicleType" onclick="window.OTA.multiOrderManager.editField(${index}, 'vehicleType')">
                    <span class="grid-value">${this.getCarTypeName(order.carTypeId)}</span>
                </div>
                <div class="grid-item" data-field="language">
                    <span class="grid-value">${this.getLanguageDisplay(order)}</span>
                </div>
            </div>
        `;
    }

    /**
     * 格式化路线显示
     * @param {Object} order - 订单对象
     * @returns {string} 格式化的路线HTML
     */
    formatRoute(order) {
        const pickup = order.pickup || order.pickupLocation || '未指定';
        const dropoff = order.dropoff || order.dropoffLocation || '未指定';
        
        return `
            <div class="route-display">
                <div class="pickup-address">
                    <span class="address-label">上车:</span>
                    <span class="address-text">${pickup}</span>
                </div>
                <div class="dropoff-address">
                    <span class="address-label">下车:</span>
                    <span class="address-text">${dropoff}</span>
                </div>
            </div>
        `;
    }

    /**
     * 格式化电话显示
     * @param {string} phone - 电话号码
     * @returns {string} 格式化的电话
     */
    formatPhone(phone) {
        if (!phone) return '未提供';
        // 格式化电话显示：+6012***
        if (phone.startsWith('+60')) {
            return phone.substr(0, 5) + '***';
        }
        return phone.length > 6 ? phone.substr(0, 6) + '***' : phone;
    }

    /**
     * 获取车型名称
     * @param {number} carTypeId - 车型ID
     * @returns {string} 车型名称
     */
    getCarTypeName(carTypeId) {
        // 从API服务中获取车型映射
        const apiService = window.getApiService?.();
        if (apiService && apiService.staticData && apiService.staticData.carTypes) {
            const carType = apiService.staticData.carTypes.find(type => type.id === carTypeId);
            if (carType) {
                const name = carType.name;
                const simplified = name.split('(')[0].trim();
                return simplified;
            }
        }
        
        // 备用映射
        const carTypes = {
            5: '5座轿车',
            15: '7座MPV',
            20: '10座面包车',
            32: 'Alphard豪华车',
            34: '票务服务',
            37: '加长5座',
            38: '4座掀背'
        };
        return carTypes[carTypeId] || '标准车型';
    }

    /**
     * 获取语言显示
     * @param {Object} order - 订单对象
     * @returns {string} 语言显示文本
     */
    getLanguageDisplay(order) {
        if (!order.languagesIdArray || !Array.isArray(order.languagesIdArray)) {
            return '中文';
        }

        const languageMap = {
            2: '英文',
            3: '马来文',
            4: '中文',
            5: '举牌',
            6: '包车'
        };

        const languages = order.languagesIdArray.map(id => languageMap[id] || `语言${id}`);
        return languages.length > 0 ? languages.join(', ') : '中文';
    }

    /**
     * 绑定订单选择事件
     * @param {HTMLElement} orderList - 订单列表容器
     */
    bindOrderSelectionEvents(orderList) {
        const checkboxes = orderList.querySelectorAll('.order-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                e.stopPropagation(); // 防止触发卡片点击
                const card = checkbox.closest('.order-card');
                if (card) {
                    card.classList.toggle('selected', checkbox.checked);
                }
                this.updateSelectedCount();
            });
        });
    }

    /**
     * 更新订单统计信息
     * @param {number} orderCount - 订单数量
     */
    updateOrderStats(orderCount) {
        const countElement = document.getElementById('multiOrderCount');
        if (countElement) {
            countElement.textContent = `${orderCount} 个订单`;
        }
    }

    /**
     * 更新选中订单数量显示
     */
    updateSelectedCount() {
        const checkboxes = document.querySelectorAll('.order-checkbox:checked');
        const countElement = document.getElementById('selectedOrderCount');
        if (countElement) {
            countElement.textContent = `已选择 ${checkboxes.length} 个订单`;
        }
    }

    /**
     * 确保面板在视窗范围内可见
     */
    ensurePanelVisible() {
        const multiOrderPanel = document.getElementById(this.config.panelId);
        if (!multiOrderPanel) return;

        // 确保面板居中显示在视窗中
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        
        // 获取面板的实际尺寸
        const panelRect = multiOrderPanel.getBoundingClientRect();
        
        // 如果面板超出视窗范围，调整其大小
        let adjustments = {};
        
        if (panelRect.width > viewportWidth * 0.95) {
            adjustments.width = '95vw';
        }
        
        if (panelRect.height > viewportHeight * 0.9) {
            adjustments.height = '90vh';
        }
        
        // 应用调整
        Object.assign(multiOrderPanel.style, adjustments);
        
        // 滚动到面板位置（如果需要）
        multiOrderPanel.scrollIntoView({ 
            behavior: 'smooth', 
            block: 'center',
            inline: 'center'
        });
        
        this.logger?.log('📱 多订单面板位置已优化', 'info', {
            viewport: `${viewportWidth}x${viewportHeight}`,
            panel: `${panelRect.width}x${panelRect.height}`,
            adjustments
        });
    }

    /**
     * 添加面板拖拽功能
     */
    addPanelDragFeature() {
        const multiOrderPanel = document.getElementById(this.config.panelId);
        const header = multiOrderPanel?.querySelector('.multi-order-header');
        
        if (!multiOrderPanel || !header) return;
        
        // 检查是否已经添加了拖拽功能
        if (header.dataset.dragEnabled === 'true') {
            this.logger?.log('🔒 面板拖拽功能已存在，跳过重复绑定', 'info');
            return;
        }

        header.style.cursor = 'move';
        header.title = '拖拽面板来移动位置';

        const dragStart = (e) => {
            if (e.target.closest('button')) return;
            
            this.dragState.initialX = e.clientX - this.dragState.currentX;
            this.dragState.initialY = e.clientY - this.dragState.currentY;
            
            if (e.target === header || header.contains(e.target)) {
                this.isDragging = true;
                multiOrderPanel.style.transition = 'none';
            }
        };

        const dragEnd = () => {
            this.dragState.initialX = this.dragState.currentX;
            this.dragState.initialY = this.dragState.currentY;
            this.isDragging = false;
            multiOrderPanel.style.transition = 'all var(--transition-normal)';
        };

        const drag = (e) => {
            if (this.isDragging) {
                e.preventDefault();
                this.dragState.currentX = e.clientX - this.dragState.initialX;
                this.dragState.currentY = e.clientY - this.dragState.initialY;
                
                // 限制拖拽范围在视窗内
                const rect = multiOrderPanel.getBoundingClientRect();
                const maxX = window.innerWidth - rect.width;
                const maxY = window.innerHeight - rect.height;
                
                this.dragState.currentX = Math.max(0, Math.min(this.dragState.currentX, maxX));
                this.dragState.currentY = Math.max(0, Math.min(this.dragState.currentY, maxY));
                
                multiOrderPanel.style.transform = `translate(${this.dragState.currentX}px, ${this.dragState.currentY}px)`;
            }
        };

        header.addEventListener('mousedown', dragStart);
        document.addEventListener('mousemove', drag);
        document.addEventListener('mouseup', dragEnd);
        
        // 标记拖拽功能已启用
        header.dataset.dragEnabled = 'true';

        this.logger?.log('🖱️ 面板拖拽功能已启用', 'info');
    }
}

// 创建全局实例
const multiOrderRenderer = new MultiOrderRenderer();

// 导出UI渲染器
window.OTA = window.OTA || {};
window.OTA.MultiOrderRenderer = MultiOrderRenderer;
window.OTA.multiOrderRenderer = multiOrderRenderer;

// 注册到OTA注册中心
if (window.OTA && window.OTA.Registry) {
    window.OTA.Registry.registerService('multiOrderRenderer', multiOrderRenderer, '@OTA_MULTI_ORDER_RENDERER');
}

// 向后兼容
window.MultiOrderRenderer = MultiOrderRenderer;

console.log('✅ 多订单UI渲染器已加载');

// 结束防重复加载检查
}
