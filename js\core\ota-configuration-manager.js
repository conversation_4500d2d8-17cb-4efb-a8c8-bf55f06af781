/**
 * OTA配置管理器
 * 
 * 统一管理所有OTA通道的配置
 * 支持动态配置更新和通道特定配置
 */
class OTAConfigurationManager {
    constructor() {
        this.configurations = new Map();
        this.defaultConfiguration = {};
        this.initialized = false;
        
        // 配置变更监听器
        this.changeListeners = new Set();
        
        this.log('OTAConfigurationManager created');
    }

    /**
     * 初始化配置管理器
     */
    async initialize() {
        try {
            this.log('Initializing configuration manager...');
            
            // 加载默认配置
            await this.loadDefaultConfiguration();
            
            // 加载通道特定配置
            await this.loadChannelConfigurations();
            
            this.initialized = true;
            this.log('Configuration manager initialized');
            
        } catch (error) {
            console.error('Failed to initialize configuration manager:', error);
            throw error;
        }
    }

    /**
     * 获取通道配置
     * @param {string} channel - 通道名称
     * @returns {Object} 通道配置
     */
    getChannelConfiguration(channel) {
        if (!this.initialized) {
            console.warn('Configuration manager not initialized, returning empty config');
            return {};
        }

        const channelConfig = this.configurations.get(channel) || {};
        
        // 合并默认配置和通道特定配置
        return {
            ...this.defaultConfiguration,
            ...channelConfig
        };
    }

    /**
     * 设置通道配置
     * @param {string} channel - 通道名称
     * @param {Object} config - 配置对象
     */
    setChannelConfiguration(channel, config) {
        const oldConfig = this.configurations.get(channel) || {};
        const newConfig = { ...oldConfig, ...config };
        
        this.configurations.set(channel, newConfig);
        
        this.log(`Configuration updated for channel: ${channel}`);
        
        // 通知监听器
        this.notifyConfigurationChange(channel, newConfig, oldConfig);
    }

    /**
     * 获取默认配置
     * @returns {Object} 默认配置
     */
    getDefaultConfiguration() {
        return { ...this.defaultConfiguration };
    }

    /**
     * 更新默认配置
     * @param {Object} config - 新的默认配置
     */
    updateDefaultConfiguration(config) {
        const oldConfig = { ...this.defaultConfiguration };
        this.defaultConfiguration = { ...this.defaultConfiguration, ...config };
        
        this.log('Default configuration updated');
        
        // 通知所有通道配置变更
        for (const channel of this.configurations.keys()) {
            this.notifyConfigurationChange(channel, this.getChannelConfiguration(channel), oldConfig);
        }
    }

    /**
     * 添加配置变更监听器
     * @param {Function} listener - 监听器函数
     */
    addChangeListener(listener) {
        this.changeListeners.add(listener);
    }

    /**
     * 移除配置变更监听器
     * @param {Function} listener - 监听器函数
     */
    removeChangeListener(listener) {
        this.changeListeners.delete(listener);
    }

    /**
     * 获取所有通道配置
     * @returns {Object} 所有配置的映射
     */
    getAllConfigurations() {
        const result = {};
        
        for (const [channel, config] of this.configurations) {
            result[channel] = this.getChannelConfiguration(channel);
        }
        
        return result;
    }

    /**
     * 重置通道配置
     * @param {string} channel - 通道名称
     */
    resetChannelConfiguration(channel) {
        const oldConfig = this.configurations.get(channel) || {};
        this.configurations.delete(channel);
        
        this.log(`Configuration reset for channel: ${channel}`);
        
        // 通知配置重置
        this.notifyConfigurationChange(channel, this.getDefaultConfiguration(), oldConfig);
    }

    /**
     * 导出配置
     * @returns {Object} 可序列化的配置对象
     */
    exportConfiguration() {
        return {
            defaultConfiguration: this.defaultConfiguration,
            channelConfigurations: Object.fromEntries(this.configurations),
            timestamp: Date.now(),
            version: '1.0.0'
        };
    }

    /**
     * 导入配置
     * @param {Object} configData - 配置数据
     */
    importConfiguration(configData) {
        if (!configData || typeof configData !== 'object') {
            throw new Error('Invalid configuration data');
        }

        // 备份当前配置
        const backup = this.exportConfiguration();

        try {
            // 导入默认配置
            if (configData.defaultConfiguration) {
                this.defaultConfiguration = { ...configData.defaultConfiguration };
            }

            // 导入通道配置
            if (configData.channelConfigurations) {
                this.configurations.clear();
                for (const [channel, config] of Object.entries(configData.channelConfigurations)) {
                    this.configurations.set(channel, config);
                }
            }

            this.log('Configuration imported successfully');

        } catch (error) {
            // 恢复备份配置
            this.importConfiguration(backup);
            throw new Error(`Failed to import configuration: ${error.message}`);
        }
    }

    /**
     * 加载默认配置
     * @private
     */
    async loadDefaultConfiguration() {
        // 默认的基础配置
        this.defaultConfiguration = {
            // 通用字段配置
            fields: {
                customerName: { required: true, type: 'string', maxLength: 50 },
                contactPhone: { required: true, type: 'string', pattern: /^1[3-9]\d{9}$/ },
                contactEmail: { required: false, type: 'email' },
                idNumber: { required: false, type: 'string' },
                passportNumber: { required: false, type: 'string' }
            },
            
            // 验证配置
            validation: {
                enableStrictMode: false,
                enableBusinessRules: true,
                enableFormatCheck: true
            },
            
            // 处理配置
            processing: {
                enableDataEncryption: false,
                enableAuditLog: true,
                enableErrorRecovery: true
            },
            
            // API配置
            api: {
                timeout: 30000,
                retryCount: 3,
                enableCache: true
            },
            
            // UI配置
            ui: {
                showProcessingIndicator: true,
                enableRealTimeValidation: true,
                theme: 'default'
            }
        };

        this.log('Default configuration loaded');
    }

    /**
     * 加载通道特定配置
     * @private
     */
    async loadChannelConfigurations() {
        // Fliggy通道配置
        this.configurations.set('fliggy', {
            api: {
                endpoint: 'https://fliggy-api.taobao.com',
                timeout: 45000,
                enableSpecialHandling: true
            },
            validation: {
                enableStrictMode: true,
                enableComplianceCheck: true
            },
            processing: {
                enableDataEncryption: true,
                enableSpecialProcessing: true
            },
            fields: {
                customerName: { required: true, type: 'string', maxLength: 20 },
                contactPhone: { required: true, type: 'string', pattern: /^1[3-9]\d{9}$/ },
                idNumber: { required: true, type: 'string', pattern: /^[1-9]\d{17}$/ }
            }
        });

        // Booking.com通道配置
        this.configurations.set('booking', {
            api: {
                endpoint: 'https://api.booking.com',
                timeout: 25000
            },
            validation: {
                enableStrictMode: false
            },
            fields: {
                customerName: { required: true, type: 'string', maxLength: 100 },
                contactEmail: { required: true, type: 'email' },
                passportNumber: { required: false, type: 'string' }
            }
        });

        // Expedia通道配置
        this.configurations.set('expedia', {
            api: {
                endpoint: 'https://api.expedia.com',
                timeout: 35000
            },
            processing: {
                enableDataEncryption: false,
                enableSpecialFormatting: true
            }
        });

        this.log('Channel configurations loaded');
    }

    /**
     * 通知配置变更
     * @param {string} channel - 通道名称
     * @param {Object} newConfig - 新配置
     * @param {Object} oldConfig - 旧配置
     * @private
     */
    notifyConfigurationChange(channel, newConfig, oldConfig) {
        const changeEvent = {
            channel,
            newConfig: { ...newConfig },
            oldConfig: { ...oldConfig },
            timestamp: Date.now()
        };

        for (const listener of this.changeListeners) {
            try {
                listener(changeEvent);
            } catch (error) {
                console.error('Error in configuration change listener:', error);
            }
        }
    }

    /**
     * 清理资源
     */
    cleanup() {
        this.configurations.clear();
        this.changeListeners.clear();
        this.defaultConfiguration = {};
        this.initialized = false;
        
        this.log('Configuration manager cleaned up');
    }

    /**
     * 记录日志
     * @param {...any} args - 日志参数
     * @private
     */
    log(...args) {
        console.log('[OTAConfigurationManager]', ...args);
    }
}

// 导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = OTAConfigurationManager;
} else if (typeof window !== 'undefined') {
    window.OTAConfigurationManager = OTAConfigurationManager;
}
