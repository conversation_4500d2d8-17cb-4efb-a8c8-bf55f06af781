<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fliggy OTA配置测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-title {
            color: #333;
            border-bottom: 2px solid #007cba;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .test-input {
            margin-bottom: 15px;
        }
        .test-input label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .test-input textarea, .test-input input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .test-input textarea {
            height: 100px;
            resize: vertical;
        }
        .test-button {
            background-color: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        .test-button:hover {
            background-color: #005a87;
        }
        .result-container {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin-top: 20px;
        }
        .result-success {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .result-error {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        .result-item {
            margin-bottom: 10px;
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        .result-label {
            font-weight: bold;
            color: #495057;
        }
        .result-value {
            color: #212529;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">🛩️ Fliggy OTA配置测试</h1>
        
        <div class="test-input">
            <label for="orderContent">订单内容 (包含"订单编号" + 19位数字):</label>
            <textarea id="orderContent" placeholder="请输入包含Fliggy订单特征的文本内容，例如：订单编号1234567890123456789">尊敬的客户，您的马来西亚接机服务已确认。订单编号1234567890123456789，请保留此信息。
接机地点：吉隆坡国际机场
乘客人数：5人
行李件数：3件
车型要求：7座商务车</textarea>
        </div>

        <div class="test-input">
            <label for="basePrice">基础价格 (MYR):</label>
            <input type="number" id="basePrice" value="150" placeholder="输入基础价格">
        </div>

        <div class="test-input">
            <label for="passengerCount">乘客人数:</label>
            <input type="number" id="passengerCount" value="5" placeholder="输入乘客人数">
        </div>

        <button class="test-button" onclick="testFliggyDetection()">🔍 测试Fliggy渠道检测</button>
        <button class="test-button" onclick="testFliggyPricing()">💰 测试Fliggy价格计算</button>
        <button class="test-button" onclick="testFliggyProcessing()">⚙️ 测试Fliggy综合处理</button>

        <div id="results"></div>
    </div>

    <!-- 引入OTA系统依赖 -->
    <script src="js/logger.js"></script>
    <script src="js/ota-system/ota-channel-detector.js"></script>
    <script src="js/ota-system/ota-customization-engine.js"></script>

    <script>
        // 测试Fliggy渠道检测
        function testFliggyDetection() {
            const orderContent = document.getElementById('orderContent').value;
            const resultsDiv = document.getElementById('results');
            
            try {
                // 获取OTA检测器实例
                const detector = window.OTA && window.OTA.channelDetector;
                if (!detector) {
                    throw new Error('OTA渠道检测器未加载');
                }

                // 执行检测
                const result = detector.detectChannel(orderContent);
                
                // 显示结果
                displayResults('Fliggy渠道检测结果', result, 'detection');
                
            } catch (error) {
                displayError('检测失败', error.message);
            }
        }

        // 测试Fliggy价格计算
        function testFliggyPricing() {
            const orderContent = document.getElementById('orderContent').value;
            const basePrice = parseFloat(document.getElementById('basePrice').value);
            const passengerCount = parseInt(document.getElementById('passengerCount').value);
            const resultsDiv = document.getElementById('results');
            
            try {
                // 获取定制化引擎实例
                const engine = window.OTA && window.OTA.customizationEngine;
                if (!engine) {
                    throw new Error('OTA定制化引擎未加载');
                }

                // 构造订单数据
                const orderData = {
                    orderContent: orderContent,
                    passenger_count: passengerCount,
                    notes: orderContent
                };

                // 执行价格计算
                const result = engine.calculateChannelPrice('Fliggy', basePrice, orderData);
                
                // 显示结果
                displayResults('Fliggy价格计算结果', result, 'pricing');
                
            } catch (error) {
                displayError('价格计算失败', error.message);
            }
        }

        // 测试Fliggy综合处理
        function testFliggyProcessing() {
            const orderContent = document.getElementById('orderContent').value;
            const basePrice = parseFloat(document.getElementById('basePrice').value);
            const passengerCount = parseInt(document.getElementById('passengerCount').value);
            
            try {
                // 获取定制化引擎实例
                const engine = window.OTA && window.OTA.customizationEngine;
                if (!engine) {
                    throw new Error('OTA定制化引擎未加载');
                }

                // 构造订单数据
                const orderData = {
                    orderContent: orderContent,
                    passenger_count: passengerCount,
                    luggage_count: 3,
                    customer_name: '测试客户',
                    pickup_time: '14:30',
                    notes: orderContent
                };

                // 执行综合处理
                const result = engine.processOrder('Fliggy', orderData, basePrice);
                
                // 显示结果
                displayResults('Fliggy综合处理结果', result, 'processing');
                
            } catch (error) {
                displayError('综合处理失败', error.message);
            }
        }

        // 显示测试结果
        function displayResults(title, result, type) {
            const resultsDiv = document.getElementById('results');
            const isSuccess = result.success !== false && result.confidence > 0;
            
            let html = `
                <div class="result-container ${isSuccess ? 'result-success' : 'result-error'}">
                    <h3>${title}</h3>
            `;

            if (type === 'detection') {
                html += `
                    <div class="result-item">
                        <span class="result-label">检测到的渠道:</span>
                        <span class="result-value">${result.detectedChannel || '未检测到'}</span>
                    </div>
                    <div class="result-item">
                        <span class="result-label">检测置信度:</span>
                        <span class="result-value">${(result.confidence * 100).toFixed(1)}%</span>
                    </div>
                    <div class="result-item">
                        <span class="result-label">检测方法:</span>
                        <span class="result-value">${result.method || '无'}</span>
                    </div>
                    <div class="result-item">
                        <span class="result-label">详细信息:</span>
                        <span class="result-value">${JSON.stringify(result.details, null, 2)}</span>
                    </div>
                `;
            } else if (type === 'pricing') {
                html += `
                    <div class="result-item">
                        <span class="result-label">原始价格:</span>
                        <span class="result-value">MYR ${result.originalPrice}</span>
                    </div>
                    <div class="result-item">
                        <span class="result-label">最终价格:</span>
                        <span class="result-value">MYR ${result.finalPrice}</span>
                    </div>
                    <div class="result-item">
                        <span class="result-label">价格调整:</span>
                        <span class="result-value">${JSON.stringify(result.adjustments, null, 2)}</span>
                    </div>
                `;
            } else if (type === 'processing') {
                html += `
                    <div class="result-item">
                        <span class="result-label">处理状态:</span>
                        <span class="result-value">${result.success ? '成功' : '失败'}</span>
                    </div>
                    <div class="result-item">
                        <span class="result-label">渠道:</span>
                        <span class="result-value">${result.channel}</span>
                    </div>
                    <div class="result-item">
                        <span class="result-label">推荐车型:</span>
                        <span class="result-value">${result.vehicleRecommendation ? result.vehicleRecommendation.mappedType : '无'}</span>
                    </div>
                    <div class="result-item">
                        <span class="result-label">价格结果:</span>
                        <span class="result-value">${result.priceResult ? 'MYR ' + result.priceResult.finalPrice : '未计算'}</span>
                    </div>
                `;
            }

            html += '</div>';
            resultsDiv.innerHTML = html;
        }

        // 显示错误信息
        function displayError(title, message) {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = `
                <div class="result-container result-error">
                    <h3>${title}</h3>
                    <div class="result-item">
                        <span class="result-label">错误信息:</span>
                        <span class="result-value">${message}</span>
                    </div>
                </div>
            `;
        }

        // 页面加载完成后进行初始化检查
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔍 检查OTA系统加载状态...');
            
            setTimeout(() => {
                const otaLoaded = window.OTA && window.OTA.channelDetector && window.OTA.customizationEngine;
                if (otaLoaded) {
                    console.log('✅ OTA系统加载成功');
                } else {
                    console.warn('⚠️ OTA系统加载不完整');
                    console.log('OTA对象:', window.OTA);
                }
            }, 1000);
        });
    </script>
</body>
</html>
