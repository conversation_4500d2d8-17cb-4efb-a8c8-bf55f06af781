/**
 * @OTA_COORDINATOR 多订单协调器
 * 🏷️ 标签: @OTA_MULTI_ORDER_COORDINATOR
 * 📝 说明: 核心协调器，管理多订单系统的各个模块
 * <AUTHOR>
 * @version 1.0.0
 */

// 防止重复加载
if (window.OTA && window.OTA.MultiOrderCoordinator) {
    console.log('多订单协调器已存在，跳过重复加载');
} else {

/**
 * 多订单协调器类
 * 作为主控制器协调各个子模块
 */
class MultiOrderCoordinator {
    constructor(config = {}) {
        // 配置参数
        this.config = {
            minInputLength: 50,
            debounceDelay: 1200,
            maxOrdersPerBatch: 5,
            batchDelay: 800,
            confidenceThreshold: 0.7,
            ...config
        };

        // 核心状态
        this.state = {
            isMultiOrderMode: false,
            currentInput: '',
            detectionResult: null,
            processedOrders: [],
            selectedOrders: new Set(),
            lastAnalysisTime: null
        };

        // 模块引用
        this.modules = {
            detector: null,
            processor: null,
            renderer: null,
            batchProcessor: null,
            stateManager: null
        };

        // 事件管理
        this.eventListeners = new Map();
        this.debounceTimer = null;

        this.logger = this.getLogger();
        this.initializeModules();
        this.setupEventBridge();
    }

    /**
     * 初始化所有模块
     */
    initializeModules() {
        try {
            // 获取检测器
            this.modules.detector = this.getService('multiOrderDetector') || 
                                   window.OTA?.multiOrderDetector || 
                                   new window.OTA.MultiOrderDetector(this.config);

            // 获取处理器
            this.modules.processor = this.getService('multiOrderProcessor') || 
                                    window.OTA?.multiOrderProcessor || 
                                    new window.OTA.MultiOrderProcessor(this.config);

            // 获取渲染器
            this.modules.renderer = this.getService('multiOrderRenderer') || 
                                   window.OTA?.multiOrderRenderer || 
                                   new window.OTA.MultiOrderRenderer(this.config);

            // 获取批量处理器
            this.modules.batchProcessor = this.getService('batchProcessor') || 
                                         window.OTA?.batchProcessor ||
                                         new window.OTA.BatchProcessor(this.config);

            // 获取状态管理器
            this.modules.stateManager = this.getService('multiOrderStateManager') || 
                                       window.OTA?.multiOrderStateManager ||
                                       new window.OTA.MultiOrderStateManager(this.config);

            this.logger.log('多订单协调器模块初始化完成', 'info');

        } catch (error) {
            this.logger.log('模块初始化失败', 'error', { error: error.message });
            this.initializeFallbackMethods();
        }
    }

    /**
     * 初始化降级方法
     */
    initializeFallbackMethods() {
        this.logger.log('使用降级方法初始化协调器', 'warning');
        
        // 如果模块加载失败，提供基本功能
        if (!this.modules.detector) {
            this.modules.detector = {
                detectMultiOrder: async (text) => ({ isMultiOrder: false, orders: [] })
            };
        }
        
        if (!this.modules.renderer) {
            this.modules.renderer = {
                renderMultiOrderPanel: () => {},
                hideMultiOrderPanel: () => {}
            };
        }
    }

    /**
     * 主要的多订单分析方法 - 已简化，避免重复检测
     * 现在只处理已经通过Gemini检测到的多订单结果
     * @param {string} text - 输入文本
     * @param {object} options - 分析选项
     * @returns {Promise<object>} 分析结果
     */
    async analyzeInput(text, options = {}) {
        try {
            // 更新状态
            this.state.currentInput = text;
            this.state.lastAnalysisTime = Date.now();

            // 🔧 优化：不再执行独立检测，避免重复的Gemini API调用
            // 现在统一通过realtime-analysis-manager的Gemini分析结果触发
            console.log('📋 多订单协调器：跳过独立检测，等待Gemini分析结果');

            // 如果options中已经包含检测结果，直接处理
            if (options.detectionResult && options.detectionResult.isMultiOrder) {
                this.state.detectionResult = options.detectionResult;
                return await this.handleMultiOrderDetected(options.detectionResult, text);
            }

            // 否则返回单订单处理结果
            return await this.handleSingleOrder(text);

        } catch (error) {
            this.logger.log('输入分析失败', 'error', { error: error.message });
            return {
                success: false,
                error: error.message,
                isMultiOrder: false
            };
        }
    }

    /**
     * 处理检测到多订单的情况
     * @param {object} detectionResult - 检测结果
     * @param {string} originalText - 原始文本
     * @returns {Promise<object>} 处理结果
     */
    async handleMultiOrderDetected(detectionResult, originalText) {
        try {
            this.logger.log('检测到多订单', 'info', {
                订单数量: detectionResult.orders.length,
                置信度: detectionResult.confidence
            });

            // 启用多订单模式
            this.enableMultiOrderMode();

            // 处理订单数据
            const processedOrders = await this.modules.processor.processOrders(detectionResult.orders, {
                originalText: originalText,
                detectionMethod: detectionResult.method
            });

            // 更新状态
            this.state.processedOrders = processedOrders;
            this.state.selectedOrders.clear();

            // 渲染界面
            await this.modules.renderer.renderMultiOrderPanel(processedOrders, {
                onOrderSelect: (index) => this.toggleOrderSelection(index),
                onBatchProcess: () => this.processBatchOrders(),
                onModeToggle: () => this.toggleMultiOrderMode()
            });

            return {
                success: true,
                isMultiOrder: true,
                orderCount: processedOrders.length,
                orders: processedOrders,
                detectionResult: detectionResult
            };

        } catch (error) {
            this.logger.log('多订单处理失败', 'error', { error: error.message });
            return {
                success: false,
                error: error.message,
                isMultiOrder: true
            };
        }
    }

    /**
     * 处理单订单情况
     * @param {string} text - 输入文本
     * @returns {Promise<object>} 处理结果
     */
    async handleSingleOrder(text) {
        // 禁用多订单模式
        this.disableMultiOrderMode();

        return {
            success: true,
            isMultiOrder: false,
            orderCount: 1,
            shouldProcessAsSingle: true,
            originalText: text
        };
    }

    /**
     * 启用多订单模式
     */
    enableMultiOrderMode() {
        if (!this.state.isMultiOrderMode) {
            this.state.isMultiOrderMode = true;
            this.modules.stateManager?.updateState({ isMultiOrderMode: true });
            this.dispatchEvent('multiOrderModeEnabled', { timestamp: Date.now() });
            
            // 向后兼容：发出旧版事件
            this.dispatchEvent('multiOrderDetected', { 
                isMultiOrder: true, 
                timestamp: Date.now() 
            });
            
            this.logger.log('多订单模式已启用', 'info');
        }
    }

    /**
     * 禁用多订单模式
     */
    disableMultiOrderMode() {
        if (this.state.isMultiOrderMode) {
            this.state.isMultiOrderMode = false;
            this.state.processedOrders = [];
            this.state.selectedOrders.clear();
            this.modules.stateManager?.updateState({ isMultiOrderMode: false });
            this.modules.renderer?.hideMultiOrderPanel();
            this.dispatchEvent('multiOrderModeDisabled', { timestamp: Date.now() });
            this.logger.log('多订单模式已禁用', 'info');
        }
    }

    /**
     * 切换订单选择状态
     * @param {number} index - 订单索引
     */
    toggleOrderSelection(index) {
        if (this.state.selectedOrders.has(index)) {
            this.state.selectedOrders.delete(index);
        } else {
            this.state.selectedOrders.add(index);
        }

        // 更新UI显示
        if (this.modules.renderer && this.modules.renderer.updateOrderSelection) {
            this.modules.renderer.updateOrderSelection(index, this.state.selectedOrders.has(index));
        }
        
        this.dispatchEvent('orderSelectionChanged', {
            index: index,
            selected: this.state.selectedOrders.has(index),
            totalSelected: this.state.selectedOrders.size
        });
    }

    /**
     * 处理批量订单
     * @returns {Promise<object>} 处理结果
     */
    async processBatchOrders() {
        try {
            const selectedIndexes = Array.from(this.state.selectedOrders);
            const selectedOrders = selectedIndexes.map(index => this.state.processedOrders[index]);

            if (selectedOrders.length === 0) {
                throw new Error('没有选择任何订单');
            }

            this.logger.log('开始批量处理订单', 'info', { 
                selectedCount: selectedOrders.length 
            });

            // 使用批量处理器
            const result = await this.modules.batchProcessor.processBatch(selectedOrders, {
                onProgress: (progress) => this.updateBatchProgress(progress),
                onOrderComplete: (orderIndex, result) => this.handleOrderComplete(orderIndex, result),
                onError: (orderIndex, error) => this.handleOrderError(orderIndex, error)
            });

            this.dispatchEvent('batchProcessingComplete', result);
            return result;

        } catch (error) {
            this.logger.log('批量处理失败', 'error', { error: error.message });
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 更新批量处理进度
     * @param {object} progress - 进度信息
     */
    updateBatchProgress(progress) {
        this.modules.renderer?.updateBatchProgress?.(progress);
        this.dispatchEvent('batchProgressUpdated', progress);
    }

    /**
     * 处理单个订单完成事件
     * @param {number} orderIndex - 订单索引
     * @param {object} result - 处理结果
     */
    handleOrderComplete(orderIndex, result) {
        this.logger.log(`订单 ${orderIndex + 1} 处理完成`, 'info');
        this.modules.renderer?.markOrderComplete?.(orderIndex, result);
    }

    /**
     * 处理单个订单错误事件
     * @param {number} orderIndex - 订单索引
     * @param {object} error - 错误信息
     */
    handleOrderError(orderIndex, error) {
        this.logger.log(`订单 ${orderIndex + 1} 处理失败`, 'error', { error: error.message });
        this.modules.renderer?.markOrderError?.(orderIndex, error);
    }

    /**
     * 获取当前状态
     * @returns {object} 当前状态
     */
    getState() {
        return {
            ...this.state,
            moduleStatus: {
                detector: !!this.modules.detector,
                processor: !!this.modules.processor,
                renderer: !!this.modules.renderer,
                batchProcessor: !!this.modules.batchProcessor,
                stateManager: !!this.modules.stateManager
            }
        };
    }

    /**
     * 重置协调器状态
     */
    reset() {
        this.disableMultiOrderMode();
        this.state.currentInput = '';
        this.state.detectionResult = null;
        this.state.lastAnalysisTime = null;
        
        if (this.debounceTimer) {
            clearTimeout(this.debounceTimer);
            this.debounceTimer = null;
        }

        this.logger.log('协调器状态已重置', 'info');
    }

    /**
     * 分发事件
     * @param {string} eventName - 事件名称
     * @param {object} data - 事件数据
     */
    dispatchEvent(eventName, data) {
        const event = new CustomEvent(eventName, { detail: data });
        window.dispatchEvent(event);

        // 调用注册的监听器
        const listeners = this.eventListeners.get(eventName) || [];
        listeners.forEach(listener => {
            try {
                listener(data);
            } catch (error) {
                this.logger.log(`事件监听器执行失败: ${eventName}`, 'error', { error: error.message });
            }
        });
    }

    /**
     * 添加事件监听器
     * @param {string} eventName - 事件名称
     * @param {function} listener - 监听器函数
     */
    addEventListener(eventName, listener) {
        if (!this.eventListeners.has(eventName)) {
            this.eventListeners.set(eventName, []);
        }
        this.eventListeners.get(eventName).push(listener);
    }

    /**
     * 获取服务实例
     * @param {string} serviceName - 服务名称
     * @returns {object|null} 服务实例
     */
    getService(serviceName) {
        if (window.OTA && window.OTA.Registry) {
            return window.OTA.Registry.getService(serviceName);
        }
        return null;
    }

    /**
     * 设置事件桥接器
     * 确保新系统事件与现有系统兼容
     */
    setupEventBridge() {
        // 监听全局多订单相关事件，转发给内部模块
        window.addEventListener('multiOrderModeChanged', (event) => {
            if (event.detail?.enabled) {
                this.enableMultiOrderMode();
            } else {
                this.disableMultiOrderMode();
            }
        });

        // 在状态变更时通知OTA集成系统
        this.addEventListener('multiOrderModeEnabled', () => {
            if (window.OTA?.multiOrderIntegration) {
                window.OTA.multiOrderIntegration.onMultiOrderModeEnabled?.();
            }
        });

        this.addEventListener('multiOrderModeDisabled', () => {
            if (window.OTA?.multiOrderIntegration) {
                window.OTA.multiOrderIntegration.onMultiOrderModeDisabled?.();
            }
        });
    }

    /**
     * 获取Logger实例
     * @returns {object} Logger实例
     */
    getLogger() {
        if (typeof getLogger === 'function') {
            return getLogger();
        }
        return {
            log: (message, level, data) => {
                console.log(`[${level?.toUpperCase() || 'INFO'}] ${message}`, data || '');
            }
        };
    }
}

// 创建全局实例
const multiOrderCoordinator = new MultiOrderCoordinator();

// 暴露到OTA命名空间
window.OTA = window.OTA || {};
window.OTA.MultiOrderCoordinator = MultiOrderCoordinator;
window.OTA.multiOrderCoordinator = multiOrderCoordinator;

// 注册到OTA注册中心
if (window.OTA && window.OTA.Registry) {
    window.OTA.Registry.registerService('multiOrderCoordinator', multiOrderCoordinator, '@OTA_MULTI_ORDER_COORDINATOR');
}

}