#!/usr/bin/env node

/**
 * 网站显示问题诊断脚本
 * 检查可能导致网站显示异常的问题
 */

const https = require('https');
const fs = require('fs');
const path = require('path');

class WebsiteDisplayDiagnostic {
    constructor() {
        this.url = 'https://createjobgmh.netlify.app/';
        this.projectRoot = path.dirname(__dirname);
        this.issues = [];
        this.suggestions = [];
    }

    log(message, type = 'info') {
        const timestamp = new Date().toISOString();
        const prefix = type === 'error' ? '🔴' : type === 'warning' ? '🟡' : type === 'success' ? '🟢' : 'ℹ️';
        console.log(`${prefix} [${timestamp}] ${message}`);
    }

    async checkWebsiteResponse() {
        this.log('检查网站响应状态...', 'info');
        
        return new Promise((resolve) => {
            const req = https.get(this.url, (res) => {
                this.log(`HTTP 状态码: ${res.statusCode}`, res.statusCode === 200 ? 'success' : 'error');
                this.log(`内容类型: ${res.headers['content-type']}`, 'info');
                this.log(`内容长度: ${res.headers['content-length']} bytes`, 'info');
                
                if (res.statusCode !== 200) {
                    this.issues.push(`HTTP 错误: ${res.statusCode}`);
                    this.suggestions.push('检查部署状态和域名配置');
                }
                
                // 检查安全头
                const securityHeaders = [
                    'content-security-policy',
                    'x-frame-options',
                    'x-content-type-options'
                ];
                
                securityHeaders.forEach(header => {
                    if (res.headers[header]) {
                        this.log(`✓ 安全头 ${header} 已配置`, 'success');
                    } else {
                        this.log(`⚠ 安全头 ${header} 缺失`, 'warning');
                    }
                });
                
                let body = '';
                res.on('data', chunk => body += chunk);
                res.on('end', () => {
                    this.analyzeContent(body);
                    resolve();
                });
            });
            
            req.on('error', (error) => {
                this.log(`网络错误: ${error.message}`, 'error');
                this.issues.push(`网络连接问题: ${error.message}`);
                resolve();
            });
            
            req.setTimeout(10000, () => {
                this.log('请求超时', 'error');
                this.issues.push('网站响应超时');
                req.abort();
                resolve();
            });
        });
    }

    analyzeContent(html) {
        this.log('分析HTML内容...', 'info');
        
        // 检查基本HTML结构
        if (!html.includes('<!DOCTYPE html>')) {
            this.issues.push('缺少DOCTYPE声明');
            this.suggestions.push('确保HTML文件以正确的DOCTYPE开头');
        }
        
        if (!html.includes('<title>')) {
            this.issues.push('缺少页面标题');
            this.suggestions.push('添加<title>标签');
        } else {
            const titleMatch = html.match(/<title[^>]*>(.*?)<\/title>/i);
            if (titleMatch) {
                this.log(`页面标题: ${titleMatch[1]}`, 'success');
            }
        }
        
        // 检查CSS链接
        const cssLinks = html.match(/<link[^>]+rel=["']stylesheet["'][^>]*>/gi) || [];
        this.log(`发现 ${cssLinks.length} 个CSS链接`, 'info');
        
        if (cssLinks.length === 0) {
            this.issues.push('未找到CSS样式表链接');
            this.suggestions.push('检查CSS文件路径和链接');
        }
        
        // 检查JavaScript链接
        const jsScripts = html.match(/<script[^>]+src=[^>]*>/gi) || [];
        this.log(`发现 ${jsScripts.length} 个JavaScript文件`, 'info');
        
        // 检查核心元素
        const coreElements = [
            { name: 'app容器', pattern: /id=["']app["']/i },
            { name: '登录面板', pattern: /id=["']loginPanel["']/i },
            { name: '主要内容', pattern: /class=["'][^"']*main-content[^"']*["']/i }
        ];
        
        coreElements.forEach(element => {
            if (element.pattern.test(html)) {
                this.log(`✓ 找到${element.name}`, 'success');
            } else {
                this.issues.push(`缺少${element.name}`);
                this.suggestions.push(`检查${element.name}的HTML结构`);
            }
        });
        
        // 检查可能的错误信息
        const errorPatterns = [
            /error/i,
            /404/i,
            /not found/i,
            /internal server error/i
        ];
        
        errorPatterns.forEach(pattern => {
            if (pattern.test(html)) {
                this.issues.push(`HTML中可能包含错误信息: ${pattern.source}`);
                this.suggestions.push('检查HTML内容中的错误信息');
            }
        });
    }

    checkLocalFiles() {
        this.log('检查本地文件完整性...', 'info');
        
        const criticalFiles = [
            'index.html',
            'css/main.css',
            'js/app-state.js',
            'js/gemini-service.js',
            'js/ui-manager.js'
        ];
        
        criticalFiles.forEach(file => {
            const filePath = path.join(this.projectRoot, file);
            if (fs.existsSync(filePath)) {
                const stats = fs.statSync(filePath);
                this.log(`✓ ${file} (${stats.size} bytes)`, 'success');
                
                if (stats.size === 0) {
                    this.issues.push(`文件为空: ${file}`);
                    this.suggestions.push(`检查 ${file} 文件内容`);
                }
            } else {
                this.issues.push(`文件缺失: ${file}`);
                this.suggestions.push(`确保 ${file} 文件存在`);
            }
        });
    }

    generateDiagnosticSuggestions() {
        this.log('生成诊断建议...', 'info');
        
        // 常见的显示问题和解决方案
        const commonSolutions = [
            '清除浏览器缓存和Cookie',
            '尝试使用无痕/隐私模式访问',
            '检查浏览器开发者工具中的控制台错误',
            '检查网络选项卡中是否有资源加载失败',
            '确认JavaScript已启用',
            '尝试不同的浏览器',
            '检查是否有广告拦截器影响',
            '确认网络连接正常'
        ];
        
        this.suggestions.push(...commonSolutions);
    }

    async run() {
        this.log('开始网站显示问题诊断...', 'info');
        
        await this.checkWebsiteResponse();
        this.checkLocalFiles();
        this.generateDiagnosticSuggestions();
        
        // 生成诊断报告
        const report = {
            timestamp: new Date().toISOString(),
            url: this.url,
            issues: this.issues,
            suggestions: this.suggestions,
            status: this.issues.length === 0 ? 'HEALTHY' : 'NEEDS_ATTENTION'
        };
        
        const reportPath = path.join(this.projectRoot, 'website-display-diagnostic.json');
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        
        // 输出结果
        this.log('诊断完成', 'info');
        this.log(`发现问题: ${this.issues.length}`, this.issues.length > 0 ? 'warning' : 'success');
        this.log(`建议操作: ${this.suggestions.length}`, 'info');
        
        if (this.issues.length > 0) {
            this.log('发现的问题:', 'warning');
            this.issues.forEach(issue => this.log(`  - ${issue}`, 'warning'));
        }
        
        this.log('建议尝试的解决方案:', 'info');
        this.suggestions.slice(0, 5).forEach(suggestion => {
            this.log(`  - ${suggestion}`, 'info');
        });
        
        this.log(`完整诊断报告: ${reportPath}`, 'info');
        
        return report.status === 'HEALTHY';
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    const diagnostic = new WebsiteDisplayDiagnostic();
    diagnostic.run().then(success => {
        process.exit(success ? 0 : 1);
    });
}

module.exports = WebsiteDisplayDiagnostic;
