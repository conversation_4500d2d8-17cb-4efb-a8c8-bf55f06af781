/**
 * 统一语言检测模块
 * 负责所有语言检测相关的功能，提供统一的API接口
 * 
 * 功能特点：
 * - 简单的二元检测逻辑（英文/中文）
 * - 统一的事件绑定和管理
 * - 可靠的初始化时机控制
 * - 与现有稳定化器集成
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    /**
     * 统一语言检测器类
     */
    class UnifiedLanguageDetector {
        constructor() {
            this.initialized = false;
            this.boundFields = new Set();
            this.eventListeners = new Map();

            // 语言检测配置
            this.config = {
                // 中文字符检测正则表达式
                chineseRegex: /[\u4e00-\u9fff\u3400-\u4dbf\uf900-\ufaff]/,
                // 默认语言ID
                defaultLanguageId: 2, // 英文
                // 中文语言ID
                chineseLanguageId: 4, // 中文
                // 需要监听的字段选择器（不包含orderInput，避免与Gemini自动触发冲突）
                targetFields: [
                    '#customerName',     // 客户姓名
                    '#pickup',          // 上车地点
                    '#dropoff',         // 目的地
                    '#extraRequirement', // 额外要求
                    '#remark',          // 备注
                    '#flightInfo'       // 航班信息
                ],
                // 防抖延迟（毫秒）
                debounceDelay: 300
            };

            // 防抖定时器
            this.debounceTimers = new Map();
        }

        /**
         * 安全的日志记录方法
         */
        log(message, level = 'info', data = null) {
            try {
                if (typeof getLogger === 'function') {
                    if (level === 'error') {
                        getLogger().logError(message, data);
                    } else {
                        getLogger().log(message, level, data);
                    }
                } else {
                    // 降级到console
                    const prefix = level === 'error' ? '❌' : level === 'success' ? '✅' : 'ℹ️';
                    console.log(`${prefix} [UnifiedLanguageDetector] ${message}`, data || '');
                }
            } catch (error) {
                console.log(`[UnifiedLanguageDetector] ${message}`, data || '');
            }
        }

        /**
         * 初始化语言检测器
         */
        async init() {
            if (this.initialized) {
                this.log('语言检测器已经初始化', 'info');
                return true;
            }

            try {
                this.log('🌐 初始化统一语言检测器...', 'info');

                // 等待DOM就绪
                await this.waitForDOM();

                // 绑定所有目标字段的事件
                await this.bindAllFieldEvents();

                // 设置默认语言
                await this.setDefaultLanguage();

                this.initialized = true;
                this.log('✅ 统一语言检测器初始化完成', 'success');

                // 注册到全局OTA命名空间
                window.OTA.unifiedLanguageDetector = this;

                return true;
            } catch (error) {
                this.log('语言检测器初始化失败', 'error', error);
                return false;
            }
        }

        /**
         * 等待DOM就绪
         */
        async waitForDOM() {
            return new Promise((resolve) => {
                if (document.readyState === 'complete') {
                    resolve();
                } else {
                    const handler = () => {
                        document.removeEventListener('DOMContentLoaded', handler);
                        window.removeEventListener('load', handler);
                        resolve();
                    };
                    document.addEventListener('DOMContentLoaded', handler);
                    window.addEventListener('load', handler);
                }
            });
        }

        /**
         * 绑定所有目标字段的事件
         */
        async bindAllFieldEvents() {
            let boundCount = 0;

            for (const selector of this.config.targetFields) {
                const element = document.querySelector(selector);
                if (element) {
                    this.bindFieldEvents(element, selector);
                    boundCount++;
                } else {
                    this.log(`⚠️ 字段不存在，跳过绑定: ${selector}`, 'warning');
                }
            }

            this.log(`📝 已绑定 ${boundCount} 个字段的语言检测事件`, 'info');
        }

        /**
         * 为单个字段绑定事件
         */
        bindFieldEvents(element, selector) {
            if (this.boundFields.has(selector)) {
                this.log(`字段 ${selector} 已经绑定过事件，跳过`, 'info');
                return;
            }

            // 创建事件处理器
            const inputHandler = (event) => this.handleInput(event, selector);
            const pasteHandler = (event) => this.handlePaste(event, selector);
            const blurHandler = (event) => this.handleBlur(event, selector);

            // 绑定事件
            element.addEventListener('input', inputHandler);
            element.addEventListener('paste', pasteHandler);
            element.addEventListener('blur', blurHandler);

            // 保存事件监听器引用，以便后续清理
            this.eventListeners.set(selector, {
                element,
                handlers: { inputHandler, pasteHandler, blurHandler }
            });

            this.boundFields.add(selector);
            this.log(`✅ 已绑定字段事件: ${selector}`, 'info');
        }

        /**
         * 处理输入事件
         */
        handleInput(event, selector) {
            const text = event.target.value;
            this.detectLanguageWithDebounce(text, selector);
        }

        /**
         * 处理粘贴事件
         */
        handlePaste(event, selector) {
            // 粘贴后稍微延迟处理，确保内容已更新
            setTimeout(() => {
                const text = event.target.value;
                this.detectLanguageWithDebounce(text, selector);
            }, 50);
        }

        /**
         * 处理失焦事件
         */
        handleBlur(event, selector) {
            const text = event.target.value;
            // 失焦时立即检测，不使用防抖
            this.detectAndApply(text, selector);
        }

        /**
         * 带防抖的语言检测
         */
        detectLanguageWithDebounce(text, sourceField) {
            // 清除之前的定时器
            if (this.debounceTimers.has(sourceField)) {
                clearTimeout(this.debounceTimers.get(sourceField));
            }

            // 设置新的定时器
            const timer = setTimeout(() => {
                this.detectAndApply(text, sourceField);
                this.debounceTimers.delete(sourceField);
            }, this.config.debounceDelay);

            this.debounceTimers.set(sourceField, timer);
        }

        /**
         * 检测语言并应用设置
         * @param {string} text - 输入文本
         * @param {string} sourceField - 来源字段
         */
        async detectAndApply(text, sourceField = 'unknown') {
            try {
                if (!text || text.trim().length === 0) {
                    // 空文本时设置默认语言
                    await this.applyLanguageSelection([this.config.defaultLanguageId], sourceField);
                    return true;
                }

                // 检测是否包含中文字符
                const hasChinese = this.config.chineseRegex.test(text);
                const languageIds = hasChinese ? [this.config.chineseLanguageId] : [this.config.defaultLanguageId];
                const languageName = hasChinese ? '中文' : '英文';

                // 应用语言选择
                const success = await this.applyLanguageSelection(languageIds, sourceField);

                if (success) {
                    this.log(
                        `🌐 检测到${languageName}内容，已自动设置${languageName}语言要求`,
                        'info',
                        {
                            sourceField,
                            textLength: text.length,
                            hasChinese,
                            languageIds
                        }
                    );
                }

                return success;
            } catch (error) {
                this.log('语言检测和应用失败', 'error', { error, sourceField, textLength: text?.length });
                return false;
            }
        }

        /**
         * 应用语言选择 - 简化为只使用直接DOM操作
         */
        async applyLanguageSelection(languageIds, sourceField) {
            try {
                // 直接操作DOM - 最可靠的方法
                return this.setLanguageSelectionDirectly(languageIds);

            } catch (error) {
                this.log('应用语言选择失败', 'error', { error, languageIds, sourceField });
                return false;
            }
        }

        /**
         * 直接操作DOM设置语言选择
         */
        setLanguageSelectionDirectly(languageIds) {
            try {
                const checkboxes = document.querySelectorAll('input[name="languagesIdArray"]');
                if (checkboxes.length === 0) {
                    this.log('未找到语言复选框，无法设置语言选择', 'warning');
                    return false;
                }

                // 清除所有选择
                checkboxes.forEach(checkbox => checkbox.checked = false);

                // 设置指定的语言
                languageIds.forEach(id => {
                    const checkbox = document.getElementById(`lang_${id}`);
                    if (checkbox) {
                        checkbox.checked = true;
                    }
                });

                // 触发change事件
                checkboxes.forEach(checkbox => {
                    if (checkbox.checked) {
                        checkbox.dispatchEvent(new Event('change', { bubbles: true }));
                    }
                });

                return true;
            } catch (error) {
                this.log('直接设置语言选择失败', 'error', error);
                return false;
            }
        }

        /**
         * 设置默认语言
         */
        async setDefaultLanguage() {
            try {
                await this.applyLanguageSelection([this.config.defaultLanguageId], 'initialization');
                this.log('✅ 已设置默认语言（英文）', 'info');
            } catch (error) {
                this.log('设置默认语言失败', 'error', error);
            }
        }

        /**
         * 清理所有事件监听器
         */
        cleanup() {
            // 清理防抖定时器
            this.debounceTimers.forEach(timer => clearTimeout(timer));
            this.debounceTimers.clear();

            // 清理事件监听器
            this.eventListeners.forEach(({ element, handlers }, selector) => {
                element.removeEventListener('input', handlers.inputHandler);
                element.removeEventListener('paste', handlers.pasteHandler);
                element.removeEventListener('blur', handlers.blurHandler);
            });

            this.eventListeners.clear();
            this.boundFields.clear();
            this.initialized = false;

            this.log('🧹 语言检测器已清理', 'info');
        }

        /**
         * 重新初始化
         */
        async reinit() {
            this.cleanup();
            return await this.init();
        }
    }

    // 创建全局实例
    window.OTA.UnifiedLanguageDetector = UnifiedLanguageDetector;

    // 自动初始化（在DOM就绪后）
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', async () => {
            const detector = new UnifiedLanguageDetector();
            await detector.init();
        });
    } else {
        // DOM已经就绪，立即初始化
        setTimeout(async () => {
            const detector = new UnifiedLanguageDetector();
            await detector.init();
        }, 100);
    }

})();
