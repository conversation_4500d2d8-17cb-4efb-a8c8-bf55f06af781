/**
 * OTA事件桥接器
 * 
 * 连接OTA系统与现有事件架构
 * 提供事件转发、过滤和协调功能
 */
class OTAEventBridge {
    constructor(otaManager) {
        this.otaManager = otaManager;
        this.eventHandlers = new Map();
        this.eventFilters = new Map();
        this.eventQueue = [];
        this.isProcessing = false;
        
        // 获取全局事件协调器
        this.globalEventCoordinator = this.getGlobalEventCoordinator();
        
        this.log('OTAEventBridge created');
    }

    /**
     * 设置事件处理器
     */
    setupEventHandlers() {
        this.log('Setting up event handlers...');
        
        // 监听通道检测事件
        this.registerHandler('channel-detected', this.handleChannelDetected.bind(this));
        
        // 监听配置变更事件
        this.registerHandler('configuration-updated', this.handleConfigurationUpdated.bind(this));
        
        // 监听策略相关事件
        this.registerHandler('strategy-activated', this.handleStrategyActivated.bind(this));
        this.registerHandler('strategy-deactivated', this.handleStrategyDeactivated.bind(this));
        this.registerHandler('strategy-registered', this.handleStrategyRegistered.bind(this));
        
        // 监听处理错误事件
        this.registerHandler('processing-error', this.handleProcessingError.bind(this));
        
        // 连接到全局事件系统
        this.connectToGlobalEvents();
        
        this.log('Event handlers set up successfully');
    }

    /**
     * 注册事件处理器
     * @param {string} eventName - 事件名称
     * @param {Function} handler - 处理函数
     */
    registerHandler(eventName, handler) {
        if (!this.eventHandlers.has(eventName)) {
            this.eventHandlers.set(eventName, new Set());
        }
        this.eventHandlers.get(eventName).add(handler);
    }

    /**
     * 注销事件处理器
     * @param {string} eventName - 事件名称
     * @param {Function} handler - 处理函数
     */
    unregisterHandler(eventName, handler) {
        if (this.eventHandlers.has(eventName)) {
            this.eventHandlers.get(eventName).delete(handler);
        }
    }

    /**
     * 发出事件
     * @param {string} eventName - 事件名称
     * @param {Object} data - 事件数据
     */
    emit(eventName, data = {}) {
        const event = {
            name: eventName,
            data,
            timestamp: Date.now(),
            source: 'ota-manager'
        };

        // 添加到事件队列
        this.eventQueue.push(event);
        
        // 处理事件队列
        this.processEventQueue();
    }

    /**
     * 添加事件过滤器
     * @param {string} eventName - 事件名称
     * @param {Function} filter - 过滤函数
     */
    addEventFilter(eventName, filter) {
        if (!this.eventFilters.has(eventName)) {
            this.eventFilters.set(eventName, new Set());
        }
        this.eventFilters.get(eventName).add(filter);
    }

    /**
     * 处理通道检测事件
     * @param {Object} data - 事件数据
     */
    handleChannelDetected(data) {
        this.log('Channel detected event received:', data);
        
        // 转发给OTA管理器
        if (this.otaManager && this.otaManager.handleChannelDetection) {
            this.otaManager.handleChannelDetection(data);
        }
        
        // 转发到全局事件系统
        this.forwardToGlobalEvents('ota-channel-detected', data);
    }

    /**
     * 处理配置更新事件
     * @param {Object} data - 事件数据
     */
    handleConfigurationUpdated(data) {
        this.log('Configuration updated event received:', data);
        
        // 转发到全局事件系统
        this.forwardToGlobalEvents('ota-configuration-updated', data);
        
        // 通知其他管理器
        this.notifyOtherManagers('configuration-changed', data);
    }

    /**
     * 处理策略激活事件
     * @param {Object} data - 事件数据
     */
    handleStrategyActivated(data) {
        this.log('Strategy activated event received:', data);
        
        // 更新UI状态
        this.updateUIState('strategy-active', data);
        
        // 转发到全局事件系统
        this.forwardToGlobalEvents('ota-strategy-activated', data);
    }

    /**
     * 处理策略停用事件
     * @param {Object} data - 事件数据
     */
    handleStrategyDeactivated(data) {
        this.log('Strategy deactivated event received:', data);
        
        // 更新UI状态
        this.updateUIState('strategy-inactive', data);
        
        // 转发到全局事件系统
        this.forwardToGlobalEvents('ota-strategy-deactivated', data);
    }

    /**
     * 处理策略注册事件
     * @param {Object} data - 事件数据
     */
    handleStrategyRegistered(data) {
        this.log('Strategy registered event received:', data);
        
        // 转发到全局事件系统
        this.forwardToGlobalEvents('ota-strategy-registered', data);
    }

    /**
     * 处理处理错误事件
     * @param {Object} data - 事件数据
     */
    handleProcessingError(data) {
        this.log('Processing error event received:', data);
        
        // 记录错误
        this.logError(data);
        
        // 转发到全局事件系统
        this.forwardToGlobalEvents('ota-processing-error', data);
        
        // 通知错误处理系统
        this.notifyErrorHandler(data);
    }

    /**
     * 连接到全局事件系统
     */
    connectToGlobalEvents() {
        if (this.globalEventCoordinator) {
            // 监听可能影响OTA的全局事件
            this.globalEventCoordinator.on('form-data-changed', this.handleFormDataChanged.bind(this));
            this.globalEventCoordinator.on('ui-state-changed', this.handleUIStateChanged.bind(this));
            this.globalEventCoordinator.on('application-shutdown', this.handleApplicationShutdown.bind(this));
            
            this.log('Connected to global event system');
        } else {
            this.warn('Global event coordinator not available');
        }
    }

    /**
     * 转发事件到全局事件系统
     * @param {string} eventName - 事件名称
     * @param {Object} data - 事件数据
     */
    forwardToGlobalEvents(eventName, data) {
        if (this.globalEventCoordinator) {
            this.globalEventCoordinator.emit(eventName, {
                ...data,
                forwarded: true,
                originalSource: 'ota-manager'
            });
        }
    }

    /**
     * 处理表单数据变更事件
     * @param {Object} data - 事件数据
     */
    handleFormDataChanged(data) {
        // 检查是否需要重新检测OTA通道
        if (this.shouldRetriggerDetection(data)) {
            this.emit('retrigger-channel-detection', data);
        }
    }

    /**
     * 处理UI状态变更事件
     * @param {Object} data - 事件数据
     */
    handleUIStateChanged(data) {
        // 根据UI状态调整OTA处理行为
        if (data.state === 'form-reset') {
            this.emit('reset-ota-state');
        }
    }

    /**
     * 处理应用关闭事件
     * @param {Object} data - 事件数据
     */
    handleApplicationShutdown(data) {
        this.log('Application shutdown detected, cleaning up...');
        this.cleanup();
    }

    /**
     * 处理事件队列
     */
    processEventQueue() {
        if (this.isProcessing || this.eventQueue.length === 0) {
            return;
        }

        this.isProcessing = true;

        // 批量处理事件
        const batchSize = 10;
        const batch = this.eventQueue.splice(0, batchSize);

        for (const event of batch) {
            this.processEvent(event);
        }

        this.isProcessing = false;

        // 如果还有事件，继续处理
        if (this.eventQueue.length > 0) {
            setTimeout(() => this.processEventQueue(), 0);
        }
    }

    /**
     * 处理单个事件
     * @param {Object} event - 事件对象
     */
    processEvent(event) {
        const { name, data } = event;

        try {
            // 应用过滤器
            if (!this.applyEventFilters(name, data)) {
                return; // 事件被过滤
            }

            // 执行处理器
            const handlers = this.eventHandlers.get(name);
            if (handlers) {
                for (const handler of handlers) {
                    try {
                        handler(data);
                    } catch (error) {
                        this.error(`Error in event handler for ${name}:`, error);
                    }
                }
            }

        } catch (error) {
            this.error(`Error processing event ${name}:`, error);
        }
    }

    /**
     * 应用事件过滤器
     * @param {string} eventName - 事件名称
     * @param {Object} data - 事件数据
     * @returns {boolean} 是否通过过滤
     */
    applyEventFilters(eventName, data) {
        const filters = this.eventFilters.get(eventName);
        if (!filters) return true;

        for (const filter of filters) {
            try {
                if (!filter(data)) {
                    return false; // 被过滤器阻止
                }
            } catch (error) {
                this.error(`Error in event filter for ${eventName}:`, error);
                return false;
            }
        }

        return true;
    }

    /**
     * 获取全局事件协调器
     * @returns {Object|null} 全局事件协调器
     */
    getGlobalEventCoordinator() {
        // 尝试从多个位置获取全局事件协调器
        if (typeof window !== 'undefined') {
            return window.GlobalEventCoordinator || 
                   window.EventCoordinator || 
                   window.APP?.eventCoordinator ||
                   null;
        }
        return null;
    }

    /**
     * 通知其他管理器
     * @param {string} eventName - 事件名称
     * @param {Object} data - 事件数据
     */
    notifyOtherManagers(eventName, data) {
        // 通过依赖注入容器获取其他管理器
        try {
            const dependencyContainer = window.DependencyContainer;
            if (dependencyContainer) {
                // 通知UI管理器
                const uiManager = dependencyContainer.get('UIManager');
                if (uiManager && uiManager.handleOTAEvent) {
                    uiManager.handleOTAEvent(eventName, data);
                }

                // 通知表单管理器
                const formManager = dependencyContainer.get('FormManager');
                if (formManager && formManager.handleOTAEvent) {
                    formManager.handleOTAEvent(eventName, data);
                }
            }
        } catch (error) {
            this.warn('Failed to notify other managers:', error);
        }
    }

    /**
     * 更新UI状态
     * @param {string} state - 状态名称
     * @param {Object} data - 状态数据
     */
    updateUIState(state, data) {
        this.forwardToGlobalEvents('ui-state-update-request', {
            state,
            data,
            source: 'ota-manager'
        });
    }

    /**
     * 记录错误
     * @param {Object} errorData - 错误数据
     */
    logError(errorData) {
        // 记录到错误日志系统
        if (typeof window !== 'undefined' && window.ErrorLogger) {
            window.ErrorLogger.log('ota-processing-error', errorData);
        } else {
            console.error('[OTA Error]', errorData);
        }
    }

    /**
     * 通知错误处理系统
     * @param {Object} errorData - 错误数据
     */
    notifyErrorHandler(errorData) {
        try {
            const dependencyContainer = window.DependencyContainer;
            if (dependencyContainer) {
                const errorHandler = dependencyContainer.get('ErrorHandler');
                if (errorHandler && errorHandler.handleOTAError) {
                    errorHandler.handleOTAError(errorData);
                }
            }
        } catch (error) {
            this.warn('Failed to notify error handler:', error);
        }
    }

    /**
     * 判断是否需要重新触发检测
     * @param {Object} data - 数据
     * @returns {boolean} 是否需要重新检测
     */
    shouldRetriggerDetection(data) {
        // 简单的判断逻辑，可以根据需要扩展
        const triggerFields = ['customerName', 'contactPhone', 'domain'];
        
        return triggerFields.some(field => 
            data.changedFields && data.changedFields.includes(field)
        );
    }

    /**
     * 清理资源
     */
    cleanup() {
        this.log('Cleaning up event bridge...');
        
        // 清理事件处理器
        this.eventHandlers.clear();
        this.eventFilters.clear();
        
        // 清理事件队列
        this.eventQueue = [];
        
        // 断开全局事件连接
        if (this.globalEventCoordinator) {
            // 移除监听器
            this.globalEventCoordinator.off('form-data-changed', this.handleFormDataChanged);
            this.globalEventCoordinator.off('ui-state-changed', this.handleUIStateChanged);
            this.globalEventCoordinator.off('application-shutdown', this.handleApplicationShutdown);
        }
        
        this.log('Event bridge cleaned up');
    }

    /**
     * 记录日志
     * @param {...any} args - 日志参数
     */
    log(...args) {
        console.log('[OTAEventBridge]', ...args);
    }

    /**
     * 记录警告
     * @param {...any} args - 警告参数
     */
    warn(...args) {
        console.warn('[OTAEventBridge]', ...args);
    }

    /**
     * 记录错误
     * @param {...any} args - 错误参数
     */
    error(...args) {
        console.error('[OTAEventBridge]', ...args);
    }
}

// 导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = OTAEventBridge;
} else if (typeof window !== 'undefined') {
    window.OTAEventBridge = OTAEventBridge;
}
