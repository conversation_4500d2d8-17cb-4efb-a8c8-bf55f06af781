/**
 * @OTA_PROMPT_TEMPLATES OTA渠道专属Gemini提示词模板
 * 🏷️ 标签: @OTA_CHANNEL_PROMPTS
 * 📝 说明: 为不同OTA渠道定制的Gemini AI提示词模板
 * <AUTHOR>
 * @version 1.0.0
 */

(function() {
    'use strict';

    // 确保OTA命名空间存在
    window.OTA = window.OTA || {};

    /**
     * OTA渠道专属提示词模板配置
     */
    const OTAPromptTemplates = {
        
        // Chong Dealer 渠道专属模板
        'Chong Dealer': {
            basePrompt: `
**Chong Dealer渠道专属解析规则：**

**参考号识别优先级：**
- CD开头的参考号为Chong Dealer专属格式
- 通常为8-12位字符组合（如：CD123456AB）
- 优先提取并验证CD格式的参考号

**数据解析特点：**
- 客户信息通常简洁明了，注重效率
- 团号信息经常出现，格式为"团号：XXXXX"
- 价格倾向于使用MYR货币
- 车型选择偏向经济实用

**字段处理要求：**
- 'ota_reference_number': 优先识别CD开头的参考号
- 'customer_name': 清理多余空格，保持简洁
- 'pickup_time': 保持原始时间，不进行计算
- 'sub_category_id': 根据标准规则判断(接机=2, 送机=3, 包车=4)
- 'car_type_id': 优先推荐经济型车辆(5座=5, 7座=15)`,

            multiOrderPrompt: `
**Chong Dealer渠道多订单处理：**
- 经常包含批量团体订单
- 团号格式识别：CD+数字组合
- 注意区分不同日期的同团号订单
- 批量订单通常有统一的联系人信息`,

            validationRules: [
                'CD参考号格式验证',
                '团号信息完整性检查',
                '经济型车辆优先推荐',
                'MYR货币单位标准化'
            ]
        },

        // 携程(Ctrip)渠道专属模板  
        'Ctrip': {
            basePrompt: `
**携程(Ctrip)渠道专属解析规则：**

**参考号识别优先级：**
- 多样化参考号格式，可能包含CT前缀
- 数字字母组合，通常较长
- 可能包含"携程"、"Ctrip"等标识

**数据解析特点：**
- 客户信息详细完整，要求规范化处理
- 经常需要中文服务(languages_id_array: [4])
- 价格支持CNY/MYR双币种显示
- 注重行李容量和舒适性
- 偏好7座以上车型

**字段处理要求：**
- 'ota_reference_number': 识别各种携程参考号格式
- 'customer_name': 支持中文姓名，保持完整格式
- 'languages_id_array': 默认包含中文(4)，可含英文(2)
- 'sub_category_id': 标准判断规则
- 'car_type_id': 舒适性优先，推荐7座MPV(15)或豪华车型
- 'driving_region_id': 准确识别行驶区域`,

            multiOrderPrompt: `
**携程渠道多订单处理：**
- 订单信息通常非常详细和规范
- 可能包含往返行程组合
- 注重服务质量和客户体验
- 经常有特殊要求和备注信息`,

            validationRules: [
                '中文姓名格式验证',
                '双币种价格显示',
                '舒适型车辆优先推荐',
                '中文服务默认启用',
                '详细行程信息完整性'
            ]
        },

        // Klook渠道专属模板
        'Klook West Malaysia': {
            basePrompt: `
**Klook渠道专属解析规则：**

**参考号识别优先级：**
- KL开头或Klook特有格式
- 国际化标准参考号
- 可能包含"Klook"标识

**数据解析特点：**
- 国际化客户，英文为主
- 注重准时性和服务质量 
- 支持多币种，MYR为主
- 平衡经济与舒适的车型选择
- 团体预订较常见

**字段处理要求：**
- 'ota_reference_number': 识别KL前缀和Klook格式
- 'customer_name': 支持国际化姓名格式
- 'languages_id_array': 默认英文(2)，可含其他语言
- 'sub_category_id': 标准判断规则
- 'car_type_id': 平衡选择，根据人数合理推荐
- 'pickup_time': 注重准时性，保持精确时间`,

            multiOrderPrompt: `
**Klook渠道多订单处理：**
- 国际化团体预订较多
- 注重服务标准化和质量
- 可能包含多语言混合信息
- 时间精确度要求高`,

            validationRules: [
                '国际化姓名格式支持',
                '英文为主语言设置',
                '准时性要求验证',
                '服务质量标准检查',
                '多币种支持验证'
            ]
        },

        // KKday渠道专属模板
        'Kkday': {
            basePrompt: `
**KKday渠道专属解析规则：**

**参考号识别优先级：**
- KK开头的参考号格式
- 台湾地区特色的预订模式
- 可能包含"KKday"标识

**数据解析特点：**
- 台湾客户群体，繁体中文常见
- 注重性价比和服务体验
- 预订信息相对简洁
- 偏好实用型车辆选择

**字段处理要求：**
- 'ota_reference_number': 识别KK前缀格式
- 'customer_name': 支持繁体中文姓名
- 'languages_id_array': 支持中文(4)和英文(2)
- 'car_type_id': 性价比导向，合理推荐
- 'sub_category_id': 标准判断规则`,

            multiOrderPrompt: `
**KKday渠道多订单处理：**
- 台湾团体游客较多
- 注重性价比和实用性
- 预订信息相对简洁明了
- 可能包含繁体中文信息`,

            validationRules: [
                'KK参考号格式验证',
                '繁体中文支持',
                '性价比车型推荐',
                '简洁信息处理',
                '台湾客户服务标准'
            ]
        },

        // 通用/默认模板
        'default': {
            basePrompt: `
**通用OTA渠道解析规则：**

**标准处理流程：**
- 使用标准的订单解析逻辑
- 支持多语言和多渠道格式
- 采用通用的价格计算方式
- 标准车型推荐算法

**字段处理要求：**
- 'ota_reference_number': 识别各种常见格式
- 'customer_name': 标准姓名清理和格式化
- 'languages_id_array': 根据内容智能判断
- 'sub_category_id': 标准服务类型判断
- 'car_type_id': 根据人数标准推荐
- 'pickup_time': 保持原始时间`,

            multiOrderPrompt: `
**通用多订单处理：**
- 标准多订单分割逻辑
- 通用格式识别
- 标准验证规则
- 基础错误处理`,

            validationRules: [
                '标准格式验证',
                '基础完整性检查',
                '通用车型推荐',
                '标准价格计算',
                '基础错误处理'
            ]
        }
    };

    /**
     * 获取渠道专属的完整提示词
     * @param {string} channelName - 渠道名称
     * @param {string} baseSystemPrompt - 基础系统提示词
     * @param {boolean} isMultiOrder - 是否为多订单模式
     * @returns {string} 完整的提示词
     */
    function generateChannelPrompt(channelName, baseSystemPrompt = '', isMultiOrder = false) {
        const template = OTAPromptTemplates[channelName] || OTAPromptTemplates['default'];
        
        let fullPrompt = baseSystemPrompt;
        
        // 添加渠道专属基础提示词
        fullPrompt += '\n\n' + template.basePrompt;
        
        // 如果是多订单模式，添加多订单专属提示词
        if (isMultiOrder && template.multiOrderPrompt) {
            fullPrompt += '\n\n' + template.multiOrderPrompt;
        }
        
        // 添加验证规则说明
        if (template.validationRules && template.validationRules.length > 0) {
            fullPrompt += '\n\n**验证规则要求：**\n';
            template.validationRules.forEach((rule, index) => {
                fullPrompt += `${index + 1}. ${rule}\n`;
            });
        }
        
        // 添加渠道标识
        fullPrompt += `\n\n**当前处理渠道：${channelName}**\n请严格按照该渠道的处理规则进行解析。`;
        
        return fullPrompt;
    }

    /**
     * 获取渠道支持的语言配置
     * @param {string} channelName - 渠道名称
     * @returns {array} 支持的语言ID数组
     */
    function getChannelLanguages(channelName) {
        const languageConfig = {
            'Chong Dealer': [4, 2], // 中文、英文
            'Ctrip': [4], // 中文为主
            'Klook West Malaysia': [2, 4], // 英文为主，支持中文
            'Kkday': [4, 2], // 中文、英文
            'default': [2, 4] // 英文、中文
        };
        
        return languageConfig[channelName] || languageConfig['default'];
    }

    /**
     * 获取渠道的车型偏好配置
     * @param {string} channelName - 渠道名称
     * @returns {object} 车型偏好配置
     */
    function getChannelVehiclePreference(channelName) {
        const preferences = {
            'Chong Dealer': {
                priority: 'economy', // 经济性优先
                default: 5, // 5座轿车
                upgrade_threshold: 4 // 4人以上才升级
            },
            'Ctrip': {
                priority: 'comfort', // 舒适性优先
                default: 15, // 7座MPV
                upgrade_threshold: 3 // 3人以上就升级
            },
            'Klook West Malaysia': {
                priority: 'balanced', // 平衡考虑
                default: 5,
                upgrade_threshold: 4
            },
            'Kkday': {
                priority: 'value', // 性价比优先
                default: 5,
                upgrade_threshold: 4
            },
            'default': {
                priority: 'standard',
                default: 5,
                upgrade_threshold: 4
            }
        };
        
        return preferences[channelName] || preferences['default'];
    }

    // 暴露到OTA命名空间
    window.OTA.PromptTemplates = OTAPromptTemplates;
    window.OTA.generateChannelPrompt = generateChannelPrompt;
    window.OTA.getChannelLanguages = getChannelLanguages;
    window.OTA.getChannelVehiclePreference = getChannelVehiclePreference;

    // 注册到OTA注册中心
    if (window.OTA && window.OTA.Registry) {
        window.OTA.Registry.registerService('promptTemplates', OTAPromptTemplates, '@OTA_PROMPT_TEMPLATES');
        window.OTA.Registry.registerFactory('generateChannelPrompt', generateChannelPrompt, '@OTA_PROMPT_GENERATOR');
    }

})();