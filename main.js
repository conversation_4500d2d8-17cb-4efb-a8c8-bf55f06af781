/**
 * OTA订单处理系统 - 主入口文件 (重构版)
 * 使用新的启动协调器统一管理初始化流程
 * 解决架构混乱问题
 */

// 等待所有模块加载完成后启动应用
document.addEventListener('DOMContentLoaded', async function() {
    console.log('🚀 开始启动OTA订单处理系统...');

    try {
        // 检查核心模块是否已加载
        if (!window.OTA || !window.OTA.container || !window.OTA.ApplicationBootstrap) {
            throw new Error('核心架构模块未正确加载，请检查script标签顺序');
        }

        // 创建启动协调器实例
        const bootstrap = new window.OTA.ApplicationBootstrap();

        // 启动应用
        const result = await bootstrap.start();

        if (result.success) {
            console.log(`✅ OTA系统启动成功，耗时: ${result.duration.toFixed(2)}ms`);

            // 暴露全局应用实例（用于调试和向后兼容）
            window.app = {
                bootstrap,
                container: window.OTA.container,
                serviceLocator: window.OTA.serviceLocator,
                getService: window.OTA.getService,
                version: '2.0.0-refactored',
                startTime: Date.now()
            };

            // 暴露到OTA命名空间
            window.OTA.app = window.app;

        } else {
            throw new Error(`系统启动失败: ${result.error}`);
        }

    } catch (error) {
        console.error('❌ 系统启动失败:', error);

        // 显示错误信息给用户
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
            background: #ff4444; color: white; padding: 20px; border-radius: 8px;
            font-family: Arial, sans-serif; z-index: 10000; max-width: 500px;
        `;
        errorDiv.innerHTML = `
            <h3>系统启动失败</h3>
            <p>${error.message}</p>
            <button onclick="location.reload()" style="background: white; color: #ff4444; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                重新加载
            </button>
        `;
        document.body.appendChild(errorDiv);
    }
});

// 🧹 已清理：移除了传统应用程序类（LegacyOTAApplication）
// 现在统一使用新的启动协调器（ApplicationBootstrap）进行初始化
// 这简化了代码结构，避免了重复的初始化逻辑
// 🧹 已清理：移除了传统应用程序类的方法
// 这些功能现在由ApplicationBootstrap统一处理
// 🧹 已清理：移除了传统应用程序类的模块初始化方法
// 现在由ApplicationBootstrap统一处理模块初始化
// 🧹 已清理：移除了传统应用程序类的事件监听和数据检查方法
// 这些功能现在由ApplicationBootstrap和相关管理器处理
// 🧹 已清理：移除了传统应用程序类的所有方法
// 包括token刷新、登录状态恢复、错误处理等功能
// 这些功能现在由ApplicationBootstrap和相关管理器统一处理
// 🧹 已清理：移除了传统应用程序类的所有剩余方法
// 包括数据检查、错误处理、系统信息显示、应用重启等功能
// 这些功能现在由ApplicationBootstrap和相关管理器统一处理

// 🧹 已清理：移除了复杂的系统健康检查函数
// 这个功能过于复杂且很少使用，现在由ApplicationBootstrap的简化版本处理

// 🧹 已清理：移除了复杂的监控控制台命令设置函数
// 这个功能过于复杂且很少使用，增加了系统复杂性
// 基本的调试功能现在由Logger直接提供