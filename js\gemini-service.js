/**
 * @OTA_SERVICE Gemini AI服务模块
 * 负责与Google Gemini API的交互，提供订单内容智能解析功能
 * 支持实时分析、多订单处理、图像识别等功能
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    // 获取依赖模块 - 使用统一的服务定位器

class GeminiService {
    constructor() {
        // 内嵌API密钥配置（个人自用项目，忽略安全警告）
        this.apiKey = 'AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s';

        // 更新为Gemini 2.0 Flash模型
        this.modelVersion = 'gemini-2.5-flash-lite-preview-06-17';
        this.baseURL = `https://generativelanguage.googleapis.com/v1beta/models/${this.modelVersion}:generateContent`;
        this.timeout = 30000;

        // 酒店知识库
        this.hotelKnowledgeBase = {
            loaded: false,
            chineseToEnglishMap: new Map(),
            fuzzySearchIndex: new Map(),
            totalHotels: 0,
            loadError: null
        };

        // 机场翻译数据库
        this.airportTranslations = new Map([
            // 吉隆坡机场
            ['吉隆坡国际机场', { english: 'Kuala Lumpur International Airport', malay: 'Lapangan Terbang Antarabangsa Kuala Lumpur', code: 'KLIA', region: '吉隆坡' }],
            ['吉隆坡机场', { english: 'Kuala Lumpur International Airport', malay: 'Lapangan Terbang Antarabangsa Kuala Lumpur', code: 'KLIA', region: '吉隆坡' }],
            ['KLIA', { english: 'Kuala Lumpur International Airport', malay: 'Lapangan Terbang Antarabangsa Kuala Lumpur', code: 'KLIA', region: '吉隆坡' }],
            ['KLIA2', { english: 'Kuala Lumpur International Airport 2', malay: 'Lapangan Terbang Antarabangsa Kuala Lumpur 2', code: 'KLIA2', region: '吉隆坡' }],
            ['梳邦机场', { english: 'Sultan Abdul Aziz Shah Airport', malay: 'Lapangan Terbang Sultan Abdul Aziz Shah', code: 'SZB', region: '吉隆坡' }],

            // 亚庇机场
            ['亚庇国际机场', { english: 'Kota Kinabalu International Airport', malay: 'Lapangan Terbang Antarabangsa Kota Kinabalu', code: 'BKI', region: '亚庇' }],
            ['亚庇机场', { english: 'Kota Kinabalu International Airport', malay: 'Lapangan Terbang Antarabangsa Kota Kinabalu', code: 'BKI', region: '亚庇' }],
            ['哥打京那巴鲁国际机场', { english: 'Kota Kinabalu International Airport', malay: 'Lapangan Terbang Antarabangsa Kota Kinabalu', code: 'BKI', region: '亚庇' }],

            // 新山机场
            ['新山士乃国际机场', { english: 'Senai International Airport', malay: 'Lapangan Terbang Antarabangsa Senai', code: 'JHB', region: '新山' }],
            ['新山机场', { english: 'Senai International Airport', malay: 'Lapangan Terbang Antarabangsa Senai', code: 'JHB', region: '新山' }],
            ['士乃机场', { english: 'Senai International Airport', malay: 'Lapangan Terbang Antarabangsa Senai', code: 'JHB', region: '新山' }],

            // 槟城机场
            ['槟城国际机场', { english: 'Penang International Airport', malay: 'Lapangan Terbang Antarabangsa Pulau Pinang', code: 'PEN', region: '槟城' }],
            ['槟城机场', { english: 'Penang International Airport', malay: 'Lapangan Terbang Antarabangsa Pulau Pinang', code: 'PEN', region: '槟城' }],
            ['峇六拜国际机场', { english: 'Penang International Airport', malay: 'Lapangan Terbang Antarabangsa Pulau Pinang', code: 'PEN', region: '槟城' }],

            // 斗湖机场
            ['斗湖机场', { english: 'Tawau Airport', malay: 'Lapangan Terbang Tawau', code: 'TWU', region: '斗湖' }],
            ['斗湖国际机场', { english: 'Tawau Airport', malay: 'Lapangan Terbang Tawau', code: 'TWU', region: '斗湖' }],

            // 新加坡机场
            ['新加坡樟宜机场', { english: 'Singapore Changi Airport', malay: 'Lapangan Terbang Changi Singapura', code: 'SIN', region: '新加坡' }],
            ['新加坡机场', { english: 'Singapore Changi Airport', malay: 'Lapangan Terbang Changi Singapura', code: 'SIN', region: '新加坡' }],
            ['樟宜机场', { english: 'Singapore Changi Airport', malay: 'Lapangan Terbang Changi Singapura', code: 'SIN', region: '新加坡' }],
            ['樟宜国际机场', { english: 'Singapore Changi Airport', malay: 'Lapangan Terbang Changi Singapura', code: 'SIN', region: '新加坡' }]
        ]);
        
        // 默认API配置（避免重复代码）
        this.defaultAPIConfig = {
            generationConfig: {
                temperature: 0.3,
                topK: 1,
                topP: 0.5,
                maxOutputTokens: 2048,
                stopSequences: []
            },
            safetySettings: [
                { category: 'HARM_CATEGORY_HARASSMENT', threshold: 'BLOCK_NONE' },
                { category: 'HARM_CATEGORY_HATE_SPEECH', threshold: 'BLOCK_NONE' },
                { category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT', threshold: 'BLOCK_NONE' },
                { category: 'HARM_CATEGORY_DANGEROUS_CONTENT', threshold: 'BLOCK_NONE' }
            ]
        };
        
        // 视觉分析专用配置
        this.visionAPIConfig = {
            generationConfig: {
                temperature: 0.1,
                topK: 32,
                topP: 1,
                maxOutputTokens: 1024
            },
            safetySettings: [
                { category: 'HARM_CATEGORY_HARASSMENT', threshold: 'BLOCK_NONE' },
                { category: 'HARM_CATEGORY_HATE_SPEECH', threshold: 'BLOCK_NONE' },
                { category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT', threshold: 'BLOCK_NONE' },
                { category: 'HARM_CATEGORY_DANGEROUS_CONTENT', threshold: 'BLOCK_NONE' }
            ]
        };
        
        // 获取logger实例
        this.logger = getLogger();

        // 实时分析配置
        this.realtimeConfig = {
            enabled: true,
            debounceDelay: 500, // 1.5秒防抖延迟
            minInputLength: 20, // 最小输入长度才触发分析
            maxRetries: 2, // 最大重试次数
            confidenceThreshold: 0.3 // 最低置信度阈值
        };

        // 初始化酒店知识库（异步，不阻塞构造函数）
        this.initializeHotelKnowledgeBase().catch(error => {
            getLogger().log(`酒店知识库初始化失败: ${error.message}`, 'error');
        });
        
        // 分析状态管理
        this.analysisState = {
            isAnalyzing: false,
            lastAnalyzedText: '',
            currentRequest: null,
            analysisHistory: []
        };
        
        // 基础验证配置
        this.verificationEnabled = false;

        // OTA参考号识别规则配置
        this.otaReferenceConfig = {
            // 排除规则 - 这些内容不应被识别为参考号
            excludePatterns: {
                customerName: /^[A-Za-z\s\u4e00-\u9fff]{2,50}$/,
                phoneNumber: /^[\+]?[\d\s\-\(\)]{8,20}$/,
                flightNumber: this.getFlightNumberPatterns(),
                dateTime: /^\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4}|\d{1,2}:\d{2}/,
                address: /^[\u4e00-\u9fff\w\s,\.\-#]{10,}$/,
                email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                price: /^[A-Z]{3}?\s*\d+(\.\d{2})?$/,
                serviceType: /^(接机|送机|包车|charter|pickup|dropoff)$/i,
                luggageCount: /^\d+\s*(件|pcs|luggage)$/i,
                passengerCount: /^\d+\s*(人|pax|passenger)$/i
            },

            // 目标识别规则 - 这些格式可能是参考号
            targetPatterns: {
                chongDealer: /^CD[A-Z0-9]{6,12}$/,
                generic: /^[A-Z]{2,4}[0-9]{6,10}$/,
                withSeparator: /^[A-Z0-9]{2,6}[-_][A-Z0-9]{3,8}$/,
                alphanumeric: /^[A-Z0-9]{8,15}$/,
                withPrefix: /^(GMH|OTA|REF|ORDER|BOOK)-[A-Z0-9]{4,10}$/i,
                teamNumber: /团号[:：]\s*([A-Z0-9\-]{6,20})/i,
                confirmationCode: /确认号[:：]\s*([A-Z0-9\-]{6,20})/i
            },

            // 平台特定规则
            platformRules: {
                'Chong Dealer': {
                    patterns: [/^CD[A-Z0-9]{6,12}$/, /^CHONG[A-Z0-9]{4,8}$/i],
                    priority: 10
                },
                'Klook': {
                    patterns: [/^KL[A-Z0-9]{8,12}$/, /^KLOOK[A-Z0-9]{4,8}$/i],
                    priority: 9
                },
                'KKday': {
                    patterns: [/^KK[A-Z0-9]{8,12}$/, /^KKDAY[A-Z0-9]{4,8}$/i],
                    priority: 9
                },
                'Generic': {
                    patterns: [/^[A-Z]{2,4}[0-9]{6,10}$/, /^[A-Z0-9]{8,15}$/],
                    priority: 5
                }
            }
        };

        // 通用化提示词模板系统
        this.promptTemplates = {
            // 基础系统角色定义
            systemRole: {
                base: `你是一个高度智能的、专门为用车服务订单处理引擎。`,
                task: `你的 **唯一任务** 是精确地、无逻辑遗漏地解析非结构化订单文本，并根据下方定义的规则，直接计算并输出一个 **JSON数组**。`,
                format: `即使只有一个订单，也必须以数组形式 \\\`[ { ... } ]\\\` 返回。`
            },

            // JSON Schema定义
            jsonSchema: {
                header: `### **输出格式契约 (JSON Schema)**\n\n你必须严格按照以下JSON结构输出。**所有未在文本中找到对应信息的字段，其值必须为 \\\`null\\\`**，不能省略字段。`,
                structure: `\\\`\\\`\\\`json
[
  {
    "customer_name": "string | null",
    "customer_contact": "string | null",
    "customer_email": "string | null",
    "ota_reference_number": "string | null",
    "flight_info": "string | null",
    "departure_time": "string | null",
    "arrival_time": "string | null",
    "flight_type": "string ('Arrival' or 'Departure') | null",
    "pickup_date": "string (YYYY-MM-DD) | null",
    "pickup_time": "string (HH:MM) | null",
    "pickup": "string | null",
    "dropoff": "string | null",
    "passenger_count": "number | null",
    "luggage_count": "number | null",
    "sub_category_id": "number | null",
    "car_type_id": "number | null",
    "driving_region_id": "number | null",
    "languages_id_array": "array of numbers | null",
    "baby_chair": "boolean | null",
    "tour_guide": "boolean | null",
    "meet_and_greet": "boolean | null",
    "needs_paging_service": "boolean | null",
    "ota_price": "number | null",
    "currency": "string ('MYR' | 'USD' | 'CNY') | null",
    "extra_requirement": "string | null"
  }
]
\\\`\\\`\\\``
            },

            // 容错和重试配置
            errorHandling: {
                retryStrategies: [
                    'simplify_prompt',
                    'add_examples',
                    'reduce_complexity',
                    'fallback_parsing'
                ],
                fallbackResponses: {
                    parseError: `解析失败，请检查输入格式`,
                    timeoutError: `请求超时，请稍后重试`,
                    formatError: `输出格式不正确，请重新生成`
                }
            }
        };

        // 智能ID填充映射表（基于api return id list.md）
        this.idMappings = {
            // 后台用户映射：邮箱 -> ID
            backendUsers: {
                '<EMAIL>': 37,
                '<EMAIL>': 89,
                '<EMAIL>': 310,
                '<EMAIL>': 311,
                '<EMAIL>': 312,
                'SMW <EMAIL>': 342,
                'SMW <EMAIL>': 343,
                '<EMAIL>': 420,
                '<EMAIL>': 421,
                '空空@gomyhire.com': 777,
                '<EMAIL>': 1047,
                '<EMAIL>': 1181,
                '<EMAIL>': 1201,
                'Swee <EMAIL>': 1652,
                'Skymirror <EMAIL>': 2249,
                '<EMAIL>': 2446,
                '<EMAIL>': 2666
            },
            // 子分类映射
            subCategories: [
                { id: 2, name: 'Pickup' },
                { id: 3, name: 'Dropoff' },
                { id: 4, name: 'Charter' }
            ],
            // 车型映射（基于乘客人数，优先使用5 Seater）
            carTypes: [
                { id: 38, name: '4 Seater Hatchback (3 passenger, 2 x L size luggage)', passengerLimit: 3 },
                { id: 5, name: '5 Seater (3 passenger, 3 x L size luggage)', passengerLimit: 3 },
                { id: 33, name: 'Premium 5 Seater (Mercedes/BMW Only) (3 passenger, 3 x L size luggage)', passengerLimit: 3 },
                { id: 37, name: 'Extended 5 (4 passenger, 4 x L size luggage)', passengerLimit: 4 },
                { id: 35, name: '7 Seater SUV (4 passenger, 4 x L size luggage)', passengerLimit: 4 },
                { id: 15, name: '7 Seater MPV (5 passenger, 4 x L size luggage)', passengerLimit: 5 },
                { id: 16, name: 'Standard Size MPV (5 passenger, 4 x L size luggage)', passengerLimit: 5 },
                { id: 31, name: 'Luxury Mpv (Serena) (5 passenger, 4 x L size luggage)', passengerLimit: 5 },
                { id: 32, name: 'Velfire/ Alphard (6 passenger, 4 x L size luggage)', passengerLimit: 6 },
                { id: 36, name: 'Alphard (6 passenger, 4 x L size luggage)', passengerLimit: 6 },
                { id: 20, name: '10 Seater MPV / Van (7 passenger, 7 x L size luggage)', passengerLimit: 7 },
                { id: 30, name: '12 seat Starex (7 passenger, 7 x L size luggage)', passengerLimit: 7 },
                { id: 23, name: '14 Seater Van (10 passenger, 10 x L size luggage)', passengerLimit: 10 },
                { id: 24, name: '18 Seater Van (12 passenger, 12 x L size luggage)', passengerLimit: 12 },
                { id: 25, name: '30 Seat Mni Bus (29 passenger, 29 x L size luggage)', passengerLimit: 29 },
                { id: 26, name: '44 Seater Bus (43 passenger, 43 x L size luggage)', passengerLimit: 43 },
                { id: 34, name: 'Ticket (N/A passenger, N/A luggage)', passengerLimit: 0 },
                { id: 39, name: 'Ticket (Non-Malaysian) (N/A passenger, N/A luggage)', passengerLimit: 0 }
            ],
            // 行驶区域映射
            drivingRegions: [
                { id: 1, name: 'Kl/selangor (KL)' },
                { id: 2, name: 'Penang (PNG)' },
                { id: 3, name: 'Johor (JB)' },
                { id: 4, name: 'Sabah (SBH)' },
                { id: 5, name: 'Singapore (SG)' },
                { id: 6, name: '携程专车 (CTRIP)' },
                { id: 8, name: 'Complete (COMPLETE)' },
                { id: 9, name: 'Paging (PG)' },
                { id: 10, name: 'Charter (CHRT)' },
                { id: 12, name: 'Malacca (MLK)' },
                { id: 13, name: 'SMW (SMW)' }
            ],
            // 语言映射 - 使用统一语言管理器
            languages: null // 动态获取
        };

        this.orderParsingPrompt = `
你是一个高度智能的、专为马来西亚和新加坡设计的用车服务订单处理引擎。
你的 **唯一任务** 是精确地、无逻辑遗漏地解析非结构化订单文本，并根据下方定义的 **五步严格规则**，直接计算并输出一个 **JSON数组**。
即使只有一个订单，也必须以数组形式 \\\`[ { ... } ]\\\` 返回。

---

### **输出格式契约 (JSON Schema)**

你必须严格按照以下JSON结构输出。**所有未在文本中找到对应信息的字段，其值必须为 \\\`null\\\`**，不能省略字段。

\\\`\\\`\\\`json
[
  {
    "customer_name": "string | null",
    "customer_contact": "string | null",
    "customer_email": "string | null",
    "ota_reference_number": "string | null",
    "flight_info": "string | null",
    "departure_time": "string | null",
    "arrival_time": "string | null",
    "flight_type": "string ('Arrival' or 'Departure') | null",
    "pickup_date": "string (YYYY-MM-DD) | null",
    "pickup_time": "string (HH:MM) | null",
    "pickup": "string | null",
    "dropoff": "string | null",
    "passenger_count": "number | null",
    "luggage_count": "number | null",
    "sub_category_id": "number | null",
    "car_type_id": "number | null",
    "driving_region_id": "number | null",
    "languages_id_array": "array of numbers | null",
    "baby_chair": "boolean | null",
    "tour_guide": "boolean | null",
    "meet_and_greet": "boolean | null",
    "needs_paging_service": "boolean | null",
    "ota_price": "number | null",
    "currency": "string ('MYR' | 'USD' | 'CNY') | null",
    "extra_requirement": "string | null"
  }
]
\\\`\\\`\\\`

---

### **第一步：多订单识别**

-   **核心指令**: 仔细阅读整个文本，判断它是否包含多个独立的订单。独立的订单通常由换行符、分隔线（如"---"）或明确的编号（如“订单1”，“订单2”）分隔。
-   **输出**: 如果识别出多个订单，则在最终的JSON数组中为每个订单创建一个独立的对象。

---

### **第二步：航班信息识别与提取规则**

**航班号识别 ('flight_info') 增强规则**
你必须准确识别和提取航班号信息，遵循以下规则：

**1. 航班号格式识别**
- **标准格式**: 2-3位航空公司代码 + 1-4位数字 (如: MH123, CZ351, SQ807)
- **带字母后缀**: 航班号后可能有字母后缀 (如: MH123A, CZ351B)
- **分段航班**: 用斜杠分隔的航班号 (如: MH123/456, CZ351/852)
- **带连字符**: 航班号中可能包含连字符 (如: MH-123, CZ-351)
- **数字开头**: 少数航班号以数字开头 (如: 9W123, 6E456)

**2. 亚洲主要航空公司代码参考**
- **马来西亚**: MH(马航), AK(亚航), FY(飞萤), OD(马印航空), MXD(MASwings)
- **新加坡**: SQ(新航), 3K(捷星亚洲), TR(酷航), MI(丝绸航空)
- **中国**: CZ(南航), CA(国航), MU(东航), HU(海航), SC(山航), ZH(深航), FM(上航), MF(厦航)
- **泰国**: TG(泰航), WE(泰微笑), FD(亚航泰国), SL(泰狮航)
- **印尼**: GA(鹰航), JT(狮航), QG(花旗航空), IN(印尼航空)
- **其他**: PR(菲航), VN(越航), JL(日航), NH(全日空), KE(大韩), OZ(韩亚), EK(阿联酋), QR(卡航)

**3. 航班信息提取优先级**
- 优先提取明确标注的航班号 (如: "航班MH123", "Flight CZ351")
- 识别时间前后的航班代码 (如: "MH123 15:30抵达", "14:00 CZ351起飞")
- 从完整描述中提取 (如: "乘坐马航MH123航班")
- 如果文本中有多个航班号，选择与服务时间最相关的一个

**4. 航班类型判断 ('flight_type')**
- **到达航班 ('Arrival')**: 包含"抵达"、"降落"、"到达"、"落地"、"arrival"等关键词
- **出发航班 ('Departure')**: 包含"起飞"、"出发"、"departure"、"takeoff"等关键词
- **自动推断**: 如果是接机服务，通常是到达航班；如果是送机服务，通常是出发航班

**5. 航班时间处理**
- **到达时间 ('arrival_time')**: 航班降落时间，用于接机服务
- **出发时间 ('departure_time')**: 航班起飞时间，用于送机服务
- **时间格式**: 统一使用24小时制 HH:MM 格式 (如: 15:30, 02:45)
- **时间关联**: 确保航班时间与航班号、航班类型保持一致

---

### **第三步：应用核心业务逻辑和计算规则 (对每个订单独立应用)**

**规则1: 服务类型 ('sub_category_id') 自动判断 (带冲突解决)**
你必须根据以下 **优先级顺序** 决定服务类型：
  1.  **包车关键词 (最高优先级)**: 如果包含 "包车", "charter", "全天", "day tour", "多小时", "景点游", "半日游" -> **直接判定为包车 (4)**，并停止后续判断。
  2.  **地点判断**:
      -   如果\\\`pickup\\\`是机场而\\\`dropoff\\\`不是机场 -> **接机 (2)**。
      -   如果\\\`dropoff\\\`是机场而\\\`pickup\\\`不是机场 -> **送机 (3)**。
  3.  **航班类型判断**:
      -   如果 \\\`flight_type\\\` 是 'Arrival' 或文本含 "到达"、"落地" -> **接机 (2)**。
      -   如果 \\\`flight_type\\\` 是 'Departure' 或文本含 "出发"、"起飞" -> **送机 (3)**。
  4.  **通用关键词**:
      -   "接机", "迎接", "airport pickup", "从机场" -> 接机 (2)
      -   "送机", "去机场", "airport dropoff", "到机场" -> 送机 (3)
  5.  **默认值**: 如果以上规则都无法判断，默认为接机 (2)。

**规则1A: 智能机场地址补全 (重要) - 自动应用，无需用户干预**
**必须自动补全缺失的机场信息，直接填入对应字段**：

**接机场景自动补全**:
- 如果判定为接机服务(sub_category_id: 2)，但\\\`pickup\\\`字段为空或不包含机场关键词，而\\\`dropoff\\\`包含酒店/地址：
  - 根据\\\`dropoff\\\`中的酒店/地址自动识别城市，并在\\\`pickup\\\`字段补全对应机场
  - 吉隆坡地区酒店/地标 → \\\`pickup\\\`: "吉隆坡国际机场KLIA"
  - 亚庇地区酒店/地标 → \\\`pickup\\\`: "亚庇国际机场"  
  - 新山地区酒店/地标 → \\\`pickup\\\`: "新山士乃机场"
  - 槟城地区酒店/地标 → \\\`pickup\\\`: "槟城国际机场"
  - 新加坡地区酒店/地标 → \\\`pickup\\\`: "新加坡樟宜机场"

**送机场景自动补全**:
- 如果判定为送机服务(sub_category_id: 3)，但\\\`dropoff\\\`字段为空或不包含机场关键词，而\\\`pickup\\\`包含酒店/地址：
  - 根据\\\`pickup\\\`中的酒店/地址自动识别城市，并在\\\`dropoff\\\`字段补全对应机场
  - 使用相同的城市-机场映射规则

**城市识别关键词**:
- 吉隆坡地区: Shangri-La, Hilton, Marriott, DoubleTree, Ritz-Carlton, Four Seasons, Sunway, PARKROYAL, Majestic, KLCC, Bukit Bintang, KL Sentral, 文华东方, 香格里拉, 希尔顿, 逸林, 万豪, 丽思卡尔顿, 君悦, 四季, 双威, 百乐海, 大华等
- 亚庇地区: Tanjung Aru, Rasa Ria, Magellan, Pacific Sutera, Hyatt Regency, 丹绒亚路, 沙利雅, 麦哲伦, 太平洋, 凯悦等
- 新山地区: Legoland, Renaissance, DoubleTree Johor, 新山, JB, 乐高, 万丽等
- 槟城地区: Eastern Oriental, Hard Rock Penang, Golden Sands, 槟城, Penang, 东方大酒店, 硬石, 香格里拉金沙等
- 新加坡地区: Marina Bay Sands, Raffles, Mandarin Oriental Singapore, Hotel Boss, 新加坡, Singapore, 滨海湾金沙, 莱佛士, 文华东方, 老板酒店等

**重要**: 补全的机场信息必须直接填入\\\`pickup\\\`或\\\`dropoff\\\`字段中，不要放在\\\`extra_requirement\\\`中。这是自动处理，不需要用户确认。

**规则2: 接送时间 ('pickup_time') 处理**
你必须准确解析客户指定的接送时间。
  -   **对于送机 (sub_category_id: 3)**:
      -   'pickup_time' = 客户明确指定的接送时间，**直接使用，不要进行任何计算**。
      -   如果文本中明确包含航班起飞时间，将其记录在 'departure_time' 字段中。
  -   **对于接机 (sub_category_id: 2)**:
      -   'pickup_time' = 客户指定的接送时间或航班到达时间。
  -   **对于包车 (sub_category_id: 4)**:
      -   'pickup_time' 等于客户指定的开始时间。
  -   **重要**: 在返回的JSON中，'pickup_time' 必须是**客户原始指定的时间**。

**规则3: 车型 ('car_type_id') 智能推荐 (考虑行李)**
根据'passenger_count'和'luggage_count'，从下面的 **车型ID参考表** 中选择**最经济且最合适**的车型ID。
  -   1-3人, 行李≤3: 5 (5 Seater)
  -   4人, 或行李>3: 37 (Extended 5) 或 35 (7 Seater SUV)
  -   5-6人: 15 (7 Seater MPV)
  -   7-10人: 20 (10 Seater MPV/Van)
  -   如果提到 "豪华", "高级", "Velfire", "Alphard" -> 优先考虑 32 (Velfire/Alphard)。

**规则4: 举牌服务 ('needs_paging_service') 自动识别**
  -   检测关键词：'举牌', '举牌接机', '举牌服务', 'meet and greet', 'meet & greet', 'meet-and-greet', '接机牌', '迎接服务', '接机服务', 'paging', 'paging service', '举牌迎接', '机场迎接', '接机员', '迎宾服务'
  -   如果检测到举牌服务关键词 -> 设置 'needs_paging_service': true, 'meetAndGreet': true
  -   否则 -> 设置 'needs_paging_service': false, 'meetAndGreet': false

**规则5: 语言 ('languages_id_array') 智能判断 (简化规则)**
  -   如果'customer_name'是中文，或文本中包含中文字符 -> **直接返回 \\\`[4]\\\` (中文)**。
  -   否则，**默认返回 \\\`[2]\\\` (英文)**。

---

### **第三步：数据格式化**

  -   **日期 ('pickup_date')**: 必须是 'YYYY-MM-DD'。必须能处理 "今天", "明天", "后天", "周三", "下周一" 等相对日期，并根据上方提供的**当前日期信息**计算出准确日期。**重要**: 年份必须根据当前年份正确计算，如果是跨年情况需要正确处理。
  -   **时间 ('pickup_time', etc.)**: 必须是 'HH:MM' 24小时制。必须能处理 "早上8点", "下午3:30", "8am", "3:30pm" 等格式。
  -   **价格 ('ota_price')**: 必须是纯数字。支持多种价格格式识别：
      * "RM150", "RM 150", "150 RM" -> 提取数字 \\\`150\\\`
      * "$150", "USD 150", "150 USD", "150美元" -> 提取数字 \\\`150\\\`
      * "￥200", "200元", "200人民币", "200 RMB", "200 CNY" -> 提取数字 \\\`200\\\`
      * "150马币", "150令吉" -> 提取数字 \\\`150\\\`
      * 同时在 'currency' 字段中标注原始货币类型：'MYR', 'USD', 'CNY'
  -   **布尔值**: 'baby_chair' 等字段必须是 \`true\` 或 \`false\`，而不是字符串。

---

### **第四步：参考ID与地点数据库**

#### **ID参考表**
-   **sub_category_id**: 2:接机, 3:送机, 4:包车
-   **car_type_id**: 5:'5 Seater', 37:'Extended 5', 35:'7 Seater SUV', 15:'7 Seater MPV', 32:'Velfire/Alphard', 20:'10 Seater Van'
-   **driving_region_id**: 1:'KL/Selangor', 2:'Penang', 3:'Johor', 4:'Sabah', 5:'Singapore', 12:'Malacca'
-   **languages_id_array**: 2:'English', 4:'Chinese'

#### **地点数据库 (地点标准化)**
你必须使用此表将文本中的地点名称（特别是酒店）标准化为官方名称。

*   **主要机场**:
    *   \\\`KLIA\\\`, \\\`KUL\\\`: "Kuala Lumpur International Airport (KLIA1)"
    *   \\\`KLIA2\\\`: "Kuala Lumpur International Airport 2 (KLIA2)"
    *   \\\`PEN\\\`: "Penang International Airport"
    *   \\\`JHB\\\`, \\\`Senai\\\`: "Senai International Airport, Johor Bahru"
    *   \\\`BKI\\\`: "Kota Kinabalu International Airport"
    *   \\\`SIN\\\`, \\\`Changi\\\`: "Singapore Changi Airport"
*   **吉隆坡(Kuala Lumpur)酒店**:
    *   \\\`文华东方\\\`, \\\`Mandarin Oriental\\\`: "Mandarin Oriental Kuala Lumpur"
    *   \\\`香格里拉\\\`, \\\`Shangri-La\\\`: "Shangri-La Hotel Kuala Lumpur"
    *   \\\`希尔顿\\\`, \\\`Hilton\\\`: "Hilton Kuala Lumpur"
    *   \\\`逸林\\\`, \\\`DoubleTree\\\`: "DoubleTree by Hilton Kuala Lumpur"
    *   \\\`万豪\\\`, \\\`Marriott\\\`: "JW Marriott Hotel Kuala Lumpur"
    *   \\\`丽思卡尔顿\\\`, \\\`Ritz-Carlton\\\`: "The Ritz-Carlton Kuala Lumpur"
    *   \\\`君悦\\\`, \\\`Grand Hyatt\\\`: "Grand Hyatt Kuala Lumpur"
    *   \\\`四季\\\`, \\\`Four Seasons\\\`: "Four Seasons Hotel Kuala Lumpur"
    *   \\\`双威\\\`, \\\`Sunway\\\`: "Sunway Resort Hotel"
    *   \\\`百乐海\\\`, \\\`PARKROYAL\\\`: "PARKROYAL COLLECTION Kuala Lumpur"
    *   \\\`大华\\\`, \\\`Majestic\\\`: "The Majestic Hotel Kuala Lumpur"
    *   \\\`菲斯\\\`, \\\`FACE\\\`: "THE FACE Style Hotel"
*   **槟城(Penang)酒店**:
    *   \\\`东方大酒店\\\`, \\\`E&O\\\`: "Eastern & Oriental Hotel"
    *   \\\`硬石\\\`, \\\`Hard Rock\\\`: "Hard Rock Hotel Penang"
    *   \\\`香格里拉金沙\\\`, \\\`Golden Sands\\\`: "Shangri-La Golden Sands, Penang"
    *   \\\`百乐海\\\`, \\\`PARKROYAL\\\`: "PARKROYAL Penang Resort"
    *   \\\`蓝屋\\\`, \\\`张弼士\\\`: "Cheong Fatt Tze Mansion"
*   **新山(Johor Bahru)酒店**:
    *   \\\`逸林\\\`, \\\`DoubleTree\\\`: "DoubleTree by Hilton Hotel Johor Bahru"
    *   \\\`万丽\\\`, \\\`Renaissance\\\`: "Renaissance Johor Bahru Hotel"
    *   \\\`乐高\\\`, \\\`Legoland\\\`: "Legoland Hotel Malaysia"
*   **亚庇(Kota Kinabalu)酒店**:
    *   \\\`丹绒亚路香格里拉\\\`, \\\`Tanjung Aru\\\`: "Shangri-La Tanjung Aru, Kota Kinabalu"
    *   \\\`沙利雅香格里拉\\\`, \\\`Rasa Ria\\\`: "Shangri-La Rasa Ria, Kota Kinabalu"
    *   \\\`麦哲伦\\\`, \\\`Magellan\\\`: "The Magellan Sutera Resort"
    *   \\\`太平洋\\\`, \\\`Pacific\\\`: "The Pacific Sutera Hotel"
    *   \\\`凯悦\\\`, \\\`Hyatt\\\`: "Hyatt Regency Kinabalu"
*   **新加坡(Singapore)酒店**:
    *   \\\`滨海湾金沙\\\`, \\\`MBS\\\`: "Marina Bay Sands"
    *   \\\`莱佛士\\\`, \\\`Raffles\\\`: "Raffles Singapore"
    *   \\\`文华东方\\\`, \\\`Mandarin Oriental\\\`: "Mandarin Oriental Singapore"
    *   \\\`老板酒店\\\`, \\\`Hotel Boss\\\`: "Hotel Boss"

---

### **第五步：查看示例**

**示例1 (送机 - 记录航班信息):**
"王先生, 明早航班MH123 02:00从KLIA起飞, 请22:30从KL Hilton送机"
→ **JSON输出:**
\\\`\\\`\\\`json
[
  {
    "customer_name": "王先生",
    "customer_contact": null,
    "sub_category_id": 3,
    "flight_info": "MH123",
    "departure_time": "02:00",
    "arrival_time": null,
    "flight_type": "Departure",
    "pickup_date": "YYYY-MM-DD",
    "pickup_time": "22:30",
    "pickup": "Hilton Kuala Lumpur",
    "dropoff": "Kuala Lumpur International Airport (KLIA1)",
    "passenger_count": 1,
    "luggage_count": null,
    "car_type_id": 5,
    "driving_region_id": 1,
    "languages_id_array": [4],
    "needs_paging_service": false,
    "ota_price": null,
    "currency": null,
    "extra_requirement": null
  }
]
\\\`\\\`\\\`

**示例2 (航班号识别增强):**
"张先生乘坐新航SQ807/123分段航班，15:45抵达KLIA2，需要接机到双子塔"
→ **JSON输出:**
\\\`\\\`\\\`json
[
  {
    "customer_name": "张先生",
    "customer_contact": null,
    "sub_category_id": 2,
    "flight_info": "SQ807/123",
    "arrival_time": "15:45",
    "departure_time": null,
    "flight_type": "Arrival",
    "pickup_date": "YYYY-MM-DD",
    "pickup_time": "15:45",
    "pickup": "Kuala Lumpur International Airport 2 (KLIA2)",
    "dropoff": "Petronas Twin Towers",
    "passenger_count": 1,
    "luggage_count": null,
    "car_type_id": 5,
    "driving_region_id": 1,
    "languages_id_array": [2],
    "needs_paging_service": false,
    "ota_price": null,
    "currency": null,
    "extra_requirement": null
  }
]
\\\`\\\`\\\`

**示例3 (多订单解析):**
"订单1: 李女士, 13800138000, 明天下午3点半CZ351降落KLIA2, 4大1小, 需要儿童座椅, 到Sunway酒店. 订单2: 同一个客户, 后天早上9点从Sunway酒店包车8小时, 中文司机"
→ **JSON输出:**
\\\`\\\`\\\`json
[
  {
    "customer_name": "李女士",
    "customer_contact": "13800138000",
    "sub_category_id": 2,
    "flight_info": "CZ351",
    "arrival_time": "15:30",
    "departure_time": null,
    "flight_type": "Arrival",
    "pickup_date": "YYYY-MM-DD",
    "pickup_time": "15:30",
    "pickup": "Kuala Lumpur International Airport 2 (KLIA2)",
    "dropoff": "Sunway Resort Hotel",
    "passenger_count": 5,
    "luggage_count": null,
    "car_type_id": 15,
    "driving_region_id": 1,
    "languages_id_array": [4],
    "baby_chair": true,
    "needs_paging_service": false,
    "ota_price": null,
    "currency": null,
    "extra_requirement": null
  },
  {
    "customer_name": "李女士",
    "customer_contact": "13800138000",
    "sub_category_id": 4,
    "flight_info": null,
    "arrival_time": null,
    "departure_time": null,
    "flight_type": null,
    "pickup_date": "YYYY-MM-DD",
    "pickup_time": "09:00",
    "pickup": "Sunway Resort Hotel",
    "dropoff": null,
    "passenger_count": 5,
    "luggage_count": null,
    "car_type_id": 15,
    "driving_region_id": 1,
    "languages_id_array": [4],
    "baby_chair": false,
    "needs_paging_service": false,
    "ota_price": null,
    "currency": null,
    "extra_requirement": "包车8小时, 中文司机"
  }
]
\\\`\\\`\\\`

---

**最终指令：**
现在，请严格按照以上所有规则，分析给定的订单描述，并 **只输出一个符合规范的、完整的JSON数组**。
        `;

        // 初始化时构建通用化提示词（保留原有提示词作为备用）
        this.universalPrompt = this.buildUniversalPrompt('standard');
    }

    /**
     * 获取增强的航班号识别正则表达式模式
     * @returns {RegExp} 航班号识别正则表达式
     */
    getFlightNumberPatterns() {
        // 综合航班号格式正则表达式
        return /^([A-Z]{2,3}\d{1,4}[A-Z]?|[A-Z]{2}\d{3,4}\/\d{1,3}|[A-Z]{2,3}-?\d{1,4}[A-Z]?|[0-9][A-Z]\d{3,4})$/;
    }

    /**
     * 获取亚洲主要航空公司代码列表（包含数字开头的特殊代码）
     * @returns {Array} 航空公司代码数组
     */
    getAsianAirlineCodes() {
        return [
            // 马来西亚航空公司
            'MH', 'AK', 'FY', 'OD', 'MXD',
            // 新加坡航空公司
            'SQ', '3K', 'TR', 'MI',
            // 中国航空公司
            'CZ', 'CA', 'MU', 'HU', 'SC', 'ZH', 'FM', 'MF', 'JD', 'G5',
            // 泰国航空公司
            'TG', 'WE', 'FD', 'SL',
            // 印尼航空公司
            'GA', 'JT', 'QG', 'IN',
            // 菲律宾航空公司
            'PR', '5J', 'Z2', 'DG',
            // 越南航空公司
            'VN', 'VJ', 'BL',
            // 日本航空公司
            'JL', 'NH', 'MM', 'BC', 'GK',
            // 韩国航空公司
            'KE', 'OZ', 'LJ', '7C',
            // 印度航空公司
            'AI', '6E', 'SG', 'I5', 'UK', '9W', // 添加Jet Airways
            // 国际航空公司
            'EK', 'QR', 'EY', 'TK', 'LH', 'BA', 'AF', 'KL',
            // 数字开头的特殊航空公司代码
            '9W', '6E', '3K', '5J', '7C'
        ];
    }

    /**
     * 获取数字开头的航空公司代码列表
     * @returns {Array} 数字开头的航空公司代码
     */
    getNumericStartAirlineCodes() {
        return [
            '9W', // Jet Airways (印度)
            '6E', // IndiGo (印度)
            '3K', // Jetstar Asia (新加坡)
            '5J', // Cebu Pacific (菲律宾)
            '7C', // Jeju Air (韩国)
            '8M', // Myanmar Airways International
            '2P', // PAL Express (菲律宾)
            '4U'  // Germanwings (德国)
        ];
    }

    /**
     * 验证航班号格式是否有效（支持数字开头）
     * @param {string} flightNumber - 航班号
     * @returns {boolean} 是否为有效航班号
     */
    validateFlightNumber(flightNumber) {
        if (!flightNumber || typeof flightNumber !== 'string') {
            return false;
        }

        const cleanFlight = flightNumber.trim().toUpperCase();
        const airlineCodes = this.getAsianAirlineCodes();
        const numericStartCodes = this.getNumericStartAirlineCodes();

        // 检查是否匹配增强的航班号格式
        const flightPattern = this.getFlightNumberPatterns();
        if (!flightPattern.test(cleanFlight)) {
            return false;
        }

        // 处理数字开头的航班号
        const numericMatch = cleanFlight.match(/^([0-9][A-Z])/);
        if (numericMatch) {
            const airlineCode = numericMatch[1];
            return numericStartCodes.includes(airlineCode);
        }

        // 处理标准格式的航班号
        const airlineMatch = cleanFlight.match(/^([A-Z]{2,3})/);
        if (airlineMatch) {
            const airlineCode = airlineMatch[1];
            return airlineCodes.includes(airlineCode);
        }

        // 处理带连字符的航班号
        const hyphenMatch = cleanFlight.match(/^([A-Z]{2,3})-/);
        if (hyphenMatch) {
            const airlineCode = hyphenMatch[1];
            return airlineCodes.includes(airlineCode);
        }

        return false;
    }

    /**
     * 从文本中智能提取航班号（支持无关键词识别）
     * @param {string} text - 输入文本
     * @returns {string|null} 提取的航班号
     */
    extractFlightNumber(text) {
        if (!text || typeof text !== 'string') {
            return null;
        }

        // 第一阶段：明确标注的航班号（最高优先级）
        const explicitPatterns = [
            /航班\s*[：:]\s*([A-Z0-9\/\-]+)/gi,
            /Flight\s*[：:]\s*([A-Z0-9\/\-]+)/gi,
            /航班号\s*[：:]\s*([A-Z0-9\/\-]+)/gi,
            /Flight\s+Number\s*[：:]\s*([A-Z0-9\/\-]+)/gi
        ];

        for (const pattern of explicitPatterns) {
            const matches = text.match(pattern);
            if (matches) {
                for (const match of matches) {
                    const extracted = match.match(/([A-Z0-9\/\-]+)$/i);
                    const candidate = extracted ? extracted[1] : null;
                    if (candidate && this.validateFlightNumber(candidate)) {
                        if (this.logger) this.logger.log(`✅ 明确标注航班号: ${candidate}`, 'success');
                        return candidate.toUpperCase();
                    }
                }
            }
        }

        // 第二阶段：无关键词智能识别
        const candidates = this.extractFlightNumberCandidates(text);
        const scoredCandidates = candidates.map(candidate => ({
            flight: candidate,
            score: this.calculateFlightNumberScore(candidate, text)
        })).filter(item => item.score > 0);

        // 按分数排序，选择最高分的候选项
        if (scoredCandidates.length > 0) {
            scoredCandidates.sort((a, b) => b.score - a.score);
            const bestCandidate = scoredCandidates[0];

            if (bestCandidate.score >= 3) { // 最低置信度阈值
                if (this.logger) this.logger.log(`✅ 智能识别航班号: ${bestCandidate.flight} (分数: ${bestCandidate.score})`, 'success');
                return bestCandidate.flight.toUpperCase();
            }
        }

        if (this.logger) this.logger.log('❌ 未找到有效的航班号', 'warn');
        return null;
    }

    /**
     * 提取航班号候选项（无关键词）
     * @param {string} text - 输入文本
     * @returns {Array} 候选航班号数组
     */
    extractFlightNumberCandidates(text) {
        const candidates = [];

        // 基础航班号格式模式
        const patterns = [
            // 标准格式：2-3位字母 + 1-4位数字 + 可选字母后缀
            /\b([A-Z]{2,3}\d{1,4}[A-Z]?)\b/g,
            // 分段航班：航班号/数字
            /\b([A-Z]{2,3}\d{1,4}\/\d{1,3})\b/g,
            // 带连字符：航班号-数字
            /\b([A-Z]{2,3}-\d{1,4}[A-Z]?)\b/g,
            // 数字开头（少数航空公司）
            /\b([0-9][A-Z]\d{3,4})\b/g
        ];

        for (const pattern of patterns) {
            let match;
            while ((match = pattern.exec(text)) !== null) {
                const candidate = match[1];
                if (this.validateFlightNumber(candidate) && !candidates.includes(candidate)) {
                    candidates.push(candidate);
                }
            }
        }

        return candidates;
    }

    /**
     * 计算航班号候选项的置信度分数
     * @param {string} candidate - 候选航班号
     * @param {string} text - 原始文本
     * @returns {number} 置信度分数
     */
    calculateFlightNumberScore(candidate, text) {
        let score = 0;
        const lowerText = text.toLowerCase();
        const candidatePos = text.indexOf(candidate);

        // 基础分数：有效的航空公司代码
        if (this.validateFlightNumber(candidate)) {
            score += 2;
        }

        // 上下文分析：时间信息
        const timePatterns = [
            /\d{1,2}[:：]\d{2}/g,
            /\d{1,2}点\d{0,2}分?/g,
            /(上午|下午|早上|晚上|凌晨)\s*\d{1,2}[:：]?\d{0,2}/g
        ];

        for (const pattern of timePatterns) {
            const timeMatches = text.match(pattern);
            if (timeMatches) {
                // 检查航班号与时间的距离
                for (const timeMatch of timeMatches) {
                    const timePos = text.indexOf(timeMatch);
                    const distance = Math.abs(candidatePos - timePos);
                    if (distance < 50) { // 50字符内认为相关
                        score += 1.5;
                        break;
                    }
                }
            }
        }

        // 上下文分析：机场相关词汇
        const airportKeywords = [
            '机场', 'airport', 'klia', 'klia2', 'changi', 'terminal',
            '航站楼', '候机楼', '登机', 'boarding', 'gate',
            '抵达', '到达', '降落', 'arrival', 'arrive',
            '起飞', '出发', 'departure', 'takeoff'
        ];

        for (const keyword of airportKeywords) {
            if (lowerText.includes(keyword.toLowerCase())) {
                score += 1;
                break;
            }
        }

        // 上下文分析：服务类型词汇
        const serviceKeywords = [
            '接机', '送机', '包车', 'pickup', 'dropoff', 'transfer',
            '乘客', '客人', 'passenger', 'guest'
        ];

        for (const keyword of serviceKeywords) {
            if (lowerText.includes(keyword.toLowerCase())) {
                score += 0.5;
                break;
            }
        }

        // 排除规则：减分项
        // 如果候选项看起来像电话号码
        if (this.looksLikePhoneNumber(candidate, text)) {
            score -= 3;
        }

        // 如果候选项看起来像订单号/参考号
        if (this.looksLikeOrderNumber(candidate, text)) {
            score -= 2;
        }

        // 如果候选项看起来像价格
        if (this.looksLikePrice(candidate, text)) {
            score -= 2;
        }

        return Math.max(0, score); // 确保分数不为负
    }

    /**
     * 检查候选项是否看起来像电话号码
     * @param {string} candidate - 候选项
     * @param {string} text - 原始文本
     * @returns {boolean} 是否像电话号码
     */
    looksLikePhoneNumber(candidate, text) {
        // 检查是否包含在电话号码格式中
        const phonePatterns = [
            /[\+]?[\d\s\-\(\)]{8,20}/g,
            /\d{3,4}[-\s]?\d{3,4}[-\s]?\d{3,4}/g,
            /\(\d{2,4}\)\s?\d{3,4}[-\s]?\d{3,4}/g
        ];

        for (const pattern of phonePatterns) {
            const matches = text.match(pattern);
            if (matches) {
                for (const match of matches) {
                    if (match.includes(candidate)) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    /**
     * 检查候选项是否看起来像订单号/参考号
     * @param {string} candidate - 候选项
     * @param {string} text - 原始文本
     * @returns {boolean} 是否像订单号
     */
    looksLikeOrderNumber(candidate, text) {
        const orderKeywords = [
            '订单', '团号', '确认号', '参考号', 'order', 'reference', 'confirmation',
            'booking', 'ref', 'no', 'number'
        ];

        const candidatePos = text.indexOf(candidate);
        const beforeText = text.substring(Math.max(0, candidatePos - 20), candidatePos).toLowerCase();
        const afterText = text.substring(candidatePos + candidate.length, candidatePos + candidate.length + 20).toLowerCase();

        for (const keyword of orderKeywords) {
            if (beforeText.includes(keyword) || afterText.includes(keyword)) {
                return true;
            }
        }

        // 检查是否符合常见订单号格式（长度超过6位且包含多种字符类型）
        if (candidate.length > 6 && /[A-Z]/.test(candidate) && /\d/.test(candidate)) {
            const letterCount = (candidate.match(/[A-Z]/g) || []).length;
            const digitCount = (candidate.match(/\d/g) || []).length;
            // 如果字母和数字比例相对均衡，可能是订单号
            if (letterCount >= 2 && digitCount >= 3) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查候选项是否看起来像价格
     * @param {string} candidate - 候选项
     * @param {string} text - 原始文本
     * @returns {boolean} 是否像价格
     */
    looksLikePrice(candidate, text) {
        const priceKeywords = [
            '价格', '费用', '金额', '钱', 'price', 'cost', 'fee', 'amount',
            'rm', 'myr', 'usd', 'sgd', 'cny', '$', '￥', '元'
        ];

        const candidatePos = text.indexOf(candidate);
        const beforeText = text.substring(Math.max(0, candidatePos - 15), candidatePos).toLowerCase();
        const afterText = text.substring(candidatePos + candidate.length, candidatePos + candidate.length + 15).toLowerCase();

        for (const keyword of priceKeywords) {
            if (beforeText.includes(keyword) || afterText.includes(keyword)) {
                return true;
            }
        }

        // 检查是否紧邻货币符号
        const currencyPattern = /[RM$￥]\s*$/;
        if (currencyPattern.test(beforeText)) {
            return true;
        }

        return false;
    }
    
    /**
     * 对Gemini返回的原始数据进行后期处理和规范化
     * @param {string} rawText - 从Gemini API返回的原始文本
     * @returns {object|null} - 解析和清理后的JSON对象，或在失败时返回null
     */
    postProcessParsedData(rawText) {
        if (!rawText) {
            getLogger().log('Gemini后处理失败：输入文本为空', 'error');
            return null;
        }

        // 尝试从Markdown代码块中提取JSON
        const jsonMatch = rawText.match(/```(json)?\s*([\s\S]*?)\s*```/);
        const jsonString = jsonMatch ? jsonMatch[2].trim() : rawText.trim();

        try {
            let data = JSON.parse(jsonString);
            
            // 新的返回格式是数组，确保后续处理能兼容
            // 如果不是数组，为了兼容旧格式，将其包装成数组
            if (!Array.isArray(data)) {
                data = [data];
            }
            
            // 对数组中的每个订单对象进行格式规范化
            const processedData = data.map(order => this.normalizeDataFormats(order));
            
            getLogger().log('Gemini后处理成功', 'success', { count: processedData.length, data: processedData });
            return processedData;

        } catch (error) {
            getLogger().log('JSON解析失败', 'error', { 
                originalText: rawText,
                jsonString: jsonString,
                error: error.message 
            });
            // 尝试进行更宽松的修复性解析
            return this.basicParse(jsonString);
        }
    }

    /**
     * 规范化单个订单对象中的数据格式
     * @param {object} data - 单个订单对象
     * @returns {object} - 格式规范化后的订单对象
     */
    normalizeDataFormats(data) {
        const normalizedData = { ...data };

        // 规范化电话号码
        if (normalizedData.customer_contact) {
            normalizedData.customer_contact = this.normalizePhoneNumber(normalizedData.customer_contact);
        }
        
        // 规范化日期
        if (normalizedData.pickup_date) {
            normalizedData.pickup_date = this.normalizeDate(normalizedData.pickup_date);
        }

        // 规范化时间
        if (normalizedData.pickup_time) {
            normalizedData.pickup_time = this.normalizeTime(normalizedData.pickup_time);
        }
        if (normalizedData.flight_time) {
            normalizedData.flight_time = this.normalizeTime(normalizedData.flight_time);
        }
        if (normalizedData.departure_time) {
            normalizedData.departure_time = this.normalizeTime(normalizedData.departure_time);
        }
        if (normalizedData.arrival_time) {
            normalizedData.arrival_time = this.normalizeTime(normalizedData.arrival_time);
        }

        // 规范化地点并进行地址翻译
        if (normalizedData.pickup) {
            normalizedData.pickup = this.normalizeLocation(normalizedData.pickup);
            // 异步地址翻译（不阻塞主流程）
            this.translateAddressAsync(normalizedData.pickup, 'pickup', normalizedData);
        }
        if (normalizedData.dropoff) {
            normalizedData.dropoff = this.normalizeLocation(normalizedData.dropoff);
            // 异步地址翻译（不阻塞主流程）
            this.translateAddressAsync(normalizedData.dropoff, 'dropoff', normalizedData);
        }

        // 确保数字段为数字类型
        const integerFields = ['passenger_count', 'luggage_count'];
        integerFields.forEach(field => {
            if (normalizedData[field] !== null && normalizedData[field] !== undefined) {
                normalizedData[field] = parseInt(normalizedData[field], 10) || null;
            }
        });

        // 字段名称映射 - 将Gemini JSON返回的字段名映射为内部字段名
        // 内部处理使用驼峰命名，最终由表单管理器映射为API字段名
        if (normalizedData.ota_price !== undefined) {
            normalizedData.otaPrice = parseFloat(normalizedData.ota_price) || null;
            delete normalizedData.ota_price;
        }

        // 确保ID字段为数字
        const idFields = ['sub_category_id', 'car_type_id', 'driving_region_id'];
        idFields.forEach(field => {
            if (normalizedData[field] !== null && normalizedData[field] !== undefined) {
                normalizedData[field] = parseInt(normalizedData[field], 10) || null;
            }
        });

        // 确保布尔字段为布尔类型
        const booleanFields = ['baby_chair', 'tour_guide', 'meet_and_greet'];
        booleanFields.forEach(field => {
            if (normalizedData[field] !== null && normalizedData[field] !== undefined) {
                normalizedData[field] = String(normalizedData[field]).toLowerCase() === 'true';
            }
        });
        
        // 确保语言ID数组为数组
        if (normalizedData.languages_id_array && !Array.isArray(normalizedData.languages_id_array)) {
            // 尝试从字符串（如"[4]"）转换
            try {
                const parsedArray = JSON.parse(normalizedData.languages_id_array);
                if(Array.isArray(parsedArray)) {
                    normalizedData.languages_id_array = parsedArray;
                }
            } catch (e) {
                // 如果解析失败，则将其包装在数组中
                normalizedData.languages_id_array = [parseInt(normalizedData.languages_id_array, 10) || 2];
            }
        }


        // 记录Gemini数据规范化完成
        getLogger().log('Gemini数据规范化完成', 'info', {
            hasPickupDate: !!normalizedData.pickup_date,
            hasPickupTime: !!normalizedData.pickup_time,
            hasPickup: !!normalizedData.pickup,
            hasDropoff: !!normalizedData.dropoff,
            hasLuggageCount: !!normalizedData.luggage_count
        });

        return normalizedData;
    }
    
    /**
     * 规范化电话号码（移除+、-、空格）
     * @param {string} phone - 原始电话号码
     * @returns {string} - 清理后的电话号码
     */
    normalizePhoneNumber(phone) {
        if (!phone || typeof phone !== 'string') return '';
        return phone.replace(/[+\-\s]/g, '');
    }

    /**
     * 规范化日期格式为 YYYY-MM-DD
     * @param {string} date - 原始日期字符串
     * @returns {string} - YYYY-MM-DD格式的日期
     */
    normalizeDate(date) {
        if (!date || typeof date !== 'string') return '';
        try {
            // 尝试直接解析
            const d = new Date(date);
            if (!isNaN(d.getTime())) {
                return d.toISOString().split('T')[0];
            }
        } catch (e) {
            // 失败则返回原始值
            return date;
        }
        return date;
    }
    
    /**
     * 规范化时间格式为 HH:MM
     * @param {string} time - 原始时间字符串
     * @returns {string} - HH:MM格式的时间
     */
    normalizeTime(time) {
        if (!time || typeof time !== 'string') return '';
        const match = time.match(/(\d{1,2}):(\d{2})/);
        if (match) {
            const hour = match[1].padStart(2, '0');
            const minute = match[2];
            return `${hour}:${minute}`;
        }
        return time; // 格式不符则返回原值
    }

    /**
     * 规范化地点名称（移除潜在的星号、多余空格）
     * @param {string} location - 原始地点字符串
     * @returns {string} - 清理后的地点字符串
     */
    normalizeLocation(location) {
        if (!location || typeof location !== 'string') return '';
        return location.replace(/\*/g, '').trim();
    }
    
    /**
     * 从解析数据中计算置信度分数
     * @param {object} data - 解析后的数据
     * @returns {number} - 0到1之间的置信度分数
     */
    calculateConfidence(data) {
        if (!data || typeof data !== 'object') return 0;

        const totalFields = Object.keys(data).length;
        if (totalFields === 0) return 0;

        let filledFields = 0;
        for (const key in data) {
            if (data[key] !== null && data[key] !== undefined && data[key] !== '') {
                filledFields++;
            }
        }
        return filledFields / totalFields;
    }

    /**
     * 基础的、更宽松的JSON解析尝试
     * @param {string} text - 可能不完全是JSON的字符串
     * @returns {object|null} - 解析后的对象或null
     */
    basicParse(text) {
        try {
            // 这是一个非常基础的实现，未来可以扩展为更复杂的修复逻辑
            // 例如，尝试添加缺失的括号、引号等
            const sanitized = text
                .replace(/,\s*}/g, '}')
                .replace(/,\s*]/g, ']');
            
            let data = JSON.parse(sanitized);
             if (!Array.isArray(data)) {
                data = [data];
            }
            return data;
        } catch (error) {
            getLogger().log('基础JSON解析也失败', 'error', { text: text, error: error.message });
            return null;
        }
    }

    /**
     * 模拟网络延迟
     * @param {number} ms - 延迟毫秒数
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 获取当前分析状态
     * @returns {object}
     */
    getStatus() {
        return {
            isAnalyzing: this.analysisState.isAnalyzing,
            lastAnalyzedText: this.analysisState.lastAnalyzedText
        };
    }
    
    /**
     * 生成一个随机的示例订单，用于测试
     * @returns {string} - 示例订单描述
     */
    generateSampleOrder() {
        const samples = [
            `客户：张三先生 +60123456789\n接送：KLIA2机场 到 Pavilion KL购物中心\n时间：明天 14:30\n人数：2大1小\n要求：需要儿童座椅`,
            `预订人：李小姐 (<EMAIL>)\n航班：MH370 15:45抵达\n从：吉隆坡国际机场\n到：Bukit Bintang武吉免登\n日期：后天下午4点\n乘客：3人 + 2件大行李\n特殊要求：司机能说中文`,
            `Customer: John Smith\nFrom: KL Sentral Station\nTo: Genting Highlands\nDate:下周三 09:00\nPassengers: 4 adults\nLuggage: 3 large suitcases\nService: Charter tour (8 hours)`,
            `订单1: 刘女士, 13912345678, 明天下午3点半航班CZ351降落KLIA2, 4大1小, 需要儿童座椅, 到Sunway酒店.\n---\n订单2: 同一个客户, 后天早上9点从Sunway酒店包车8小时, 中文司机, 需要去双子塔和独立广场`
        ];
        
        return samples[Math.floor(Math.random() * samples.length)];
    }

    /**
     * 更新Gemini内部的ID映射表（例如，从API获取最新数据后）
     * @param {object} systemData - 包含最新ID映射的对象
     */
    updateIdMappings(systemData) {
        try {
            if (systemData.backend_users) {
                this.idMappings.backendUsers = systemData.backend_users.reduce((acc, user) => {
                    acc[user.email] = user.id;
                    return acc;
                }, {});
            }
            if (systemData.sub_categories) {
                this.idMappings.subCategories = systemData.sub_categories;
            }
            if (systemData.car_types) {
                this.idMappings.carTypes = systemData.car_types.map(car => ({
                    id: car.id,
                    name: car.name,
                    passengerLimit: car.max_passenger_capacity || 0
                }));
            }
            if (systemData.driving_regions) {
                this.idMappings.drivingRegions = systemData.driving_regions;
            }
            if (systemData.languages) {
                this.idMappings.languages = systemData.languages;
            }
            getLogger().log('Gemini ID 映射已更新', 'info', this.idMappings);
        } catch (err) {
            getLogger().logError('同步 Gemini ID 映射失败', err);
        }
    }
    
    /**
     * 动态配置实时分析参数
     * @function
     * @param {Object} config - 实时分析配置项
     * @returns {void}
     */
    configureRealtimeAnalysis(config) {
        // 参数校验，确保传入为对象
        if (typeof config !== 'object' || config === null) return;
        // 合并配置到realtimeConfig
        this.realtimeConfig = { ...this.realtimeConfig, ...config };
    }

    /**
     * 动态配置实时分析参数（别名方法，供旧版本调用）
     * @function
     * @param {Object} config - 实时分析配置项
     * @returns {void}
     */
    setRealtimeAnalysis(config) {
        // 调用新的配置方法
        this.configureRealtimeAnalysis(config);
    }

    /**
     * 判断Gemini服务是否可用
     * @returns {boolean}
     */
    isAvailable() {
        // 只要apiKey存在且非空字符串即认为可用
        return typeof this.apiKey === 'string' && this.apiKey.trim().length > 0;
    }

    /**
     * 核心函数：解析订单文本
     * @param {string} text - 用户输入的订单描述
     * @param {boolean} isRealtime - 是否为实时分析模式
     * @returns {Promise<object|null>} - 解析后的数据或null
     */
    async parseOrder(text, isRealtime = false) {
        if (!text || text.length < (isRealtime ? this.realtimeConfig.minInputLength : 10)) {
            return null;
        }

        this.analysisState.isAnalyzing = true;
        this.analysisState.lastAnalyzedText = text;

        // 获取当前日期信息以帮助Gemini正确解析相对日期
        const currentDate = new Date();
        const dateContext = `
**当前日期信息（用于相对日期计算）:**
- 当前日期: ${currentDate.toLocaleDateString('zh-CN')} (${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}-${String(currentDate.getDate()).padStart(2, '0')})
- 当前年份: ${currentDate.getFullYear()}
- 当前月份: ${currentDate.getMonth() + 1}
- 今天是: ${currentDate.toLocaleDateString('zh-CN', { weekday: 'long' })}
`;

        const requestBody = {
            contents: [{
                parts: [{ text: this.orderParsingPrompt + dateContext + "\n\n**订单描述:**\n" + text }]
            }],
            ...this.defaultAPIConfig
        };

        try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), this.timeout);
            this.analysisState.currentRequest = controller;

            const response = await fetch(`${this.baseURL}?key=${this.apiKey}`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(requestBody),
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                const errorBody = await response.json();
                throw new Error(`API请求失败: ${response.status} - ${(errorBody.error && errorBody.error.message) || '未知错误'}`);
            }

            const data = await response.json();
            const rawText = data.candidates && data.candidates[0] && data.candidates[0].content && data.candidates[0].content.parts && data.candidates[0].content.parts[0] && data.candidates[0].content.parts[0].text;
            
            // 在console中显示Gemini原始返回结果
            console.group('🤖 Gemini API返回结果');
            console.log('原始响应数据:', data);
            console.log('提取的文本内容:', rawText);
            console.groupEnd();
            
            if (!rawText) {
                throw new Error('API返回内容为空或格式不正确');
            }

            const processedData = this.postProcessParsedData(rawText);
            
            // 显示处理后的数据
            console.group('📊 处理后的订单数据');
            console.log('解析结果:', processedData);
            console.groupEnd();
            this.analysisState.isAnalyzing = false;
            return processedData;

        } catch (error) {
            this.analysisState.isAnalyzing = false;
            if (error.name === 'AbortError') {
                getLogger().log('Gemini请求超时', 'error');
            } else {
                getLogger().logError('Gemini解析时发生严重错误', error);
            }
            return null;
        }
    }

    /**
     * 专门用于多订单解析的增强方法
     * @param {Array} orderSegments - 订单片段数组
     * @returns {Promise<Array>} - 解析后的多订单数据数组
     */
    async parseMultipleOrders(orderSegments) {
        if (!Array.isArray(orderSegments) || orderSegments.length === 0) {
            return [];
        }

        if (this.logger) this.logger.log(`开始解析 ${orderSegments.length} 个订单片段`, 'info');

        const results = [];
        const errors = [];

        // 获取当前日期信息
        const currentDate = new Date();
        const dateContext = `
**当前日期信息（用于相对日期计算）:**
- 当前日期: ${currentDate.toLocaleDateString('zh-CN')} (${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}-${String(currentDate.getDate()).padStart(2, '0')})
- 当前年份: ${currentDate.getFullYear()}
- 当前月份: ${currentDate.getMonth() + 1}
- 今天是: ${currentDate.toLocaleDateString('zh-CN', { weekday: 'long' })}
`;

        // 构建专门的多订单解析提示词
        const multiOrderPrompt = `
你是一个专业的多订单解析助手。我将提供多个独立的订单片段，请为每个片段解析出完整的订单信息。

**重要指令：**
1. 我提供的每个片段都是一个独立的订单
2. 为每个片段返回一个完整的JSON对象
3. 必须返回JSON数组格式，即使只有一个订单
4. 严格按照以下JSON schema格式返回

**JSON Schema:**
\\\`\\\`\\\`json
[
  {
    "customer_name": "string | null",
    "customer_contact": "string | null", 
    "customer_email": "string | null",
    "ota_reference_number": "string | null",
    "flight_info": "string | null",
    "departure_time": "string | null",
    "arrival_time": "string | null", 
    "flight_type": "string ('Arrival' or 'Departure') | null",
    "pickup_date": "string (YYYY-MM-DD) | null",
    "pickup_time": "string (HH:MM) | null",
    "pickup": "string | null",
    "dropoff": "string | null",
    "passenger_count": "number | null",
    "luggage_count": "number | null",
    "sub_category_id": "number | null",
    "car_type_id": "number | null", 
    "driving_region_id": "number | null",
    "languages_id_array": "array of numbers | null",
    "baby_chair": "boolean | null",
    "tour_guide": "boolean | null",
    "meet_and_greet": "boolean | null",
    "needs_paging_service": "boolean | null",
    "ota_price": "number | null",
    "currency": "string ('MYR' | 'USD' | 'CNY') | null",
    "extra_requirement": "string | null"
  }
]
\\\`\\\`\\\`

${this.orderParsingPrompt.split('**第一步：多订单识别**')[1] || this.orderParsingPrompt}

${dateContext}

**待解析的订单片段：**
`;

        // 逐个解析订单片段
        for (let i = 0; i < orderSegments.length; i++) {
            const segment = orderSegments[i];
            
            try {
                this.logger?.log(`解析订单片段 ${i + 1}/${orderSegments.length}`, 'info');
                
                const fullPrompt = multiOrderPrompt + `\n\n**订单片段 ${i + 1}:**\n${segment}`;
                
                const requestBody = {
                    contents: [{
                        parts: [{ text: fullPrompt }]
                    }],
                    ...this.defaultAPIConfig
                };

                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), this.timeout);

                const response = await fetch(`${this.baseURL}?key=${this.apiKey}`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(requestBody),
                    signal: controller.signal
                });

                clearTimeout(timeoutId);

                if (!response.ok) {
                    const errorBody = await response.json();
                    throw new Error(`API请求失败: ${response.status} - ${errorBody.error?.message || '未知错误'}`);
                }

                const data = await response.json();
                const rawText = data.candidates?.[0]?.content?.parts?.[0]?.text;
                
                // 在console中显示多订单解析结果
                console.group(`🤖 Gemini多订单解析结果 (片段 ${i + 1}/${orderSegments.length})`);
                console.log('原始响应数据:', data);
                console.log('提取的文本内容:', rawText);
                console.groupEnd();
                
                if (!rawText) {
                    throw new Error('API返回内容为空');
                }

                const processedData = this.postProcessParsedData(rawText);
                
                // 显示处理后的订单数据
                console.group(`📊 多订单处理结果 (片段 ${i + 1})`);
                console.log('解析结果:', processedData);
                console.groupEnd();
                
                if (processedData && processedData.success) {
                    // 确保返回的是数组，取第一个元素作为此片段的解析结果
                    const orderData = Array.isArray(processedData.data) ? processedData.data[0] : processedData.data;
                    
                    if (orderData) {
                        // 添加元数据
                        orderData._segment_index = i;
                        orderData._original_text = segment;
                        orderData._confidence = processedData.confidence || 0;
                        
                        results.push(orderData);
                        this.logger?.log(`订单片段 ${i + 1} 解析成功`, 'success');
                    } else {
                        throw new Error('解析结果为空');
                    }
                } else {
                    throw new Error(processedData?.message || '解析失败');
                }

                // 添加延迟避免API限制
                if (i < orderSegments.length - 1) {
                    await new Promise(resolve => setTimeout(resolve, 500));
                }

            } catch (error) {
                this.logger?.logError(`订单片段 ${i + 1} 解析失败`, error);
                errors.push({
                    index: i,
                    segment: segment.substring(0, 100) + '...',
                    error: error.message
                });
                
                // 添加失败的占位符
                results.push({
                    _segment_index: i,
                    _original_text: segment,
                    _parse_error: error.message,
                    customer_name: null,
                    customer_contact: null,
                    pickup: null,
                    dropoff: null,
                    pickup_date: null,
                    pickup_time: null,
                    sub_category_id: 2, // 默认接机
                    passenger_count: 1
                });
            }
        }

        this.logger?.log(`多订单解析完成，成功: ${results.filter(r => !r._parse_error).length}, 失败: ${errors.length}`, 'info');

        return {
            success: true,
            data: results,
            errors: errors,
            totalSegments: orderSegments.length,
            successCount: results.filter(r => !r._parse_error).length,
            failureCount: errors.length
        };
    }

    /**
     * 🔧 简化版：多订单检测（移除过度复杂的验证系统）
     * @param {string} orderText - 原始订单文本
     * @param {Object} options - 选项（向后兼容）
     * @returns {Promise<Object>} 检测结果和解析订单
     */
    async detectAndSplitMultiOrdersWithVerification(orderText, options = {}) {
        // 简化：直接调用基础方法，移除复杂的多重验证
        return await this.detectAndSplitMultiOrders(orderText);
    }

    /**
     * 一体化智能多订单检测、分割和完整解析（方案一实现）
     * @param {string} orderText - 原始订单文本
     * @returns {Promise<Object>} 包含检测结果和完整解析订单的对象
     */
    async detectAndSplitMultiOrders(orderText) {
        if (!orderText || typeof orderText !== 'string' || orderText.trim().length < 50) {
            return {
                isMultiOrder: false,
                orders: [],
                orderCount: 0,
                confidence: 0,
                analysis: '输入文本过短或无效'
            };
        }

        this.logger?.log('🤖 Gemini开始一体化多订单检测和完整解析...', 'info');

        try {
            // 获取当前日期信息
            const currentDate = new Date();
            const dateContext = `
**当前日期信息（用于相对日期计算）:**
- 当前日期: ${currentDate.toLocaleDateString('zh-CN')} (${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}-${String(currentDate.getDate()).padStart(2, '0')})
- 当前年份: ${currentDate.getFullYear()}
- 当前月份: ${currentDate.getMonth() + 1}
- 今天是: ${currentDate.toLocaleDateString('zh-CN', { weekday: 'long' })}
`;

            // 🔧 强化JSON格式约束的一体化prompt：检测+分割+完整解析
            const enhancedMultiOrderPrompt = `
**【重要指令：必须返回有效JSON格式】**
**【关键要求：对于多订单情况，必须确保orderCount>=2且isMultiOrder=true】**

你是专业的多订单智能处理助手。请完成以下任务：
1. **仔细分析输入文本，识别是否包含多个独立的订单记录**
2. **如果文本中包含多个客户、多个团号、多个航班时间等，必须识别为多订单**
3. **特别注意：如果文本包含换行分隔的多个订单，每行一个订单，必须设置正确的orderCount**
4. 如果是多订单，分割并完整解析每个订单  
5. 返回结构化的订单数据

**【关键修复】多订单判断的最终检查：**
- 在生成最终JSON之前，重新计算orders数组的长度
- 如果orders.length > 1，强制设置：orderCount = orders.length, isMultiOrder = true
- 如果检测到举牌服务关键词，即使只有一个订单也要设置：isMultiOrder = true
- 如果检测到多个团号，必须设置：orderCount >= 团号数量, isMultiOrder = true

**返回格式（严格要求）：**
\`\`\`json
{
  "isMultiOrder": boolean,
  "orderCount": number,
  "confidence": number,
  "orders": [订单对象数组],
  "analysis": "分析说明"
}
\`\`\`

**重要：orderCount必须等于orders数组的实际长度！**

**多订单判断标准（按业务逻辑）：**
1. **按日期分离**：不同日期的服务必须分为不同订单
2. **按订单号/团号分离**：每个唯一的订单号/团号都是独立订单
3. **往返订单分离**：同一团号的往返（接机+送机）如果日期不同，必须分为两个订单
4. **举牌/Meet and Greet检测**：包含举牌、举牌接机、举牌服务、meet and greet、meet & greet、meet-and-greet、接机牌、迎接服务、接机服务、paging、paging service、举牌迎接、机场迎接、接机员、迎宾服务等服务的订单必须触发多订单模块（即使是单一订单也要设置isMultiOrder=true）
5. **服务类型分离**：接机、送机、包车、酒店接送等不同服务类型需要分开处理
6. **强制多订单标识**：如果文本包含多个团号（如EJBTBY250716-3, EJBTBY250716-4）或多个不同日期（如16/7, 21/7），**必须设置isMultiOrder=true且orderCount>=2**
7. **换行分隔识别**：每个换行的独立订单信息都应该被识别为单独的订单

**具体检测规则：**
- 包含多个不同的日期（如16/7、17/7、21/7等）→ **强制设置orderCount>=2, isMultiOrder=true**
- 包含多个不同的订单号/团号（如EJBTBY250716-3, EJBTBY250716-4等）→ **强制设置orderCount>=2, isMultiOrder=true**
- 包含接机和送机的组合（即使同一团号，不同日期需分离）→ **强制设置orderCount>=2, isMultiOrder=true**
- 包含举牌、举牌接机、举牌服务、meet and greet、meet & greet、meet-and-greet、接机牌、迎接服务、接机服务、paging、paging service、举牌迎接、机场迎接、接机员、迎宾服务等关键词 → **强制设置isMultiOrder=true**
- 包含多个不同的客户姓名或联系方式 → **强制设置orderCount>=2, isMultiOrder=true**
- 包含多个不同的航班信息和时间 → **强制设置orderCount>=2, isMultiOrder=true**
- 包含7 SEATER等特殊车型要求
- 包含聊天记录格式（如[2025/7/10 17:27]等时间戳格式）
- 每个消息块中的独立订单信息应分别解析
- **换行符分隔的订单**：如果文本包含多行独立的订单信息（每行都包含团号、日期、客人信息），**每行都是一个独立订单，必须设置orderCount=行数, isMultiOrder=true**

**多订单识别示例：**

**示例1 - 不同团号的订单（应识别为多订单）：**
\`\`\`
接机：团号：EJBTBY250716-3 1PAX 16/7 KLIA2 IN 2110 (AK169) 客人：农玉琴 客人联系：18589880205
接机：团号：EJBTBY250716-4 2PAX 16/7 KLIA2 IN 1010 (AK115) 客人：甘小姐 客人联系：13119629639
\`\`\`
结果：orderCount=2，isMultiOrder=true（不同团号）

**示例2 - 同一团号的往返订单（应识别为多订单）：**
\`\`\`
接机：团号：EJBTBY250717 2PAX 17/7 KLIA2 PICKUP 0600 客人：简锦霞 客人联系：13424915035
送机：团号：EJBTBY250717 2PAX 21/7 MOXY PUTRAJAYA PICKUP 0600 - KLIA2 (AK188 1000) 客人：简锦霞 客人联系：13424915035
\`\`\`
结果：orderCount=2，isMultiOrder=true（同团号不同日期的往返）

**示例3 - 包含举牌/Meet and Greet的订单（应识别为多订单）：**
\`\`\`
接机：团号：EJBTBY250716-1 2PAX 16/7 KLIA2 IN 1010 (AK115) Meet and Greet 客人：张三 客人联系：123456789
\`\`\`
结果：orderCount=1，isMultiOrder=true（包含Meet and Greet服务）

**示例4 - 聊天记录格式（应识别为多订单）：**
\`\`\`
[2025/7/10 17:27] Joshua: 接机：团号：EJBTBY250712-1 2PAX 13/7 KLIA IN 0100 (MF857) - MOXY PUTRAJAYA 客人：朱芸 客人联系：18130403306
[2025/7/11 19:22] Joshua: 送机：团号：EJBTBY250710-1 2PAX 14/7 THE FACE STYLE HOTEL KL 0500AM - KLIA2 (AK5136 0915) 客人：刘凯 客人联系：18764221412
[2025/7/11 19:22] Joshua: 接机：团号：EJBTBY250715 2PAX 15/7 KLIA2 IN 2015 (AK181) - MOXY PUTRAJAYA 客人：顾婉婷 & 苟晓琼 客人联系：13884407028
\`\`\`
结果：orderCount=3，isMultiOrder=true（不同团号、不同日期、不同客户）

**ID映射参考：**
- 子分类ID: 接机=2, 送机=3, 包车=4
- 行驶区域ID: 吉隆坡=1, 雪兰莪=2, 其他根据地点推测
- 车型ID: 根据乘客人数选择 (1-3人=5, 4人=37, 5人=15, 6人=32, 7人=20, 10人=23, 12人=24)
- 语言ID: 中文=1, 英文=2, 马来文=3, 多语言=[1,2]

**货币处理：**
- 识别并保留原始货币单位
- 支持: MYR, USD, SGD, CNY, RMB(转为CNY)

**【格式要求：必须严格遵守】**
1. 必须以\`\`\`json开头
2. 必须以\`\`\`结尾
3. 中间必须是完整有效的JSON对象
4. 不允许有任何额外的文字说明

\`\`\`json
{
  "isMultiOrder": true,
  "orderCount": 11,
  "confidence": 0.95,
  "analysis": "检测到11个独立的订单记录，包含不同的团号、日期、客户信息和服务类型",
  "orders": [
    {
      "rawText": "接机：团号：EJBTBY250716-3 1PAX 16/7 KLIA2 IN 2110 (AK169) - MOXY PUTRAJAYA 客人：农玉琴 客人联系：18589880205",
      "customerName": "农玉琴",
      "customerContact": "18589880205",
      "customerEmail": null,
      "pickup": "KLIA2",
      "dropoff": "MOXY PUTRAJAYA",
      "pickupDate": "2025-07-16",
      "pickupTime": "21:10",
      "passengerCount": 1,
      "luggageCount": 1,
      "flightInfo": "AK169",
      "otaReferenceNumber": "EJBTBY250716-3",
      "otaPrice": null,
      "currency": "MYR",
      "carTypeId": 5,
      "subCategoryId": 2,
      "drivingRegionId": 1,
      "languagesIdArray": [1, 2],
      "extraRequirement": null,
      "babyChair": false,
      "tourGuide": false,
      "meetAndGreet": false
    },
    {
      "rawText": "接机：团号：EJBTBY250716-4 2PAX 16/7 KLIA2 IN 1010 (AK115) - MOXY PUTRAJAYA 客人：甘小姐 客人联系：13119629639",
      "customerName": "甘小姐",
      "customerContact": "13119629639",
      "customerEmail": null,
      "pickup": "KLIA2",
      "dropoff": "MOXY PUTRAJAYA",
      "pickupDate": "2025-07-16",
      "pickupTime": "10:10",
      "passengerCount": 2,
      "luggageCount": 2,
      "flightInfo": "AK115",
      "otaReferenceNumber": "EJBTBY250716-4",
      "otaPrice": null,
      "currency": "MYR",
      "carTypeId": 5,
      "subCategoryId": 2,
      "drivingRegionId": 1,
      "languagesIdArray": [1, 2],
      "extraRequirement": null,
      "babyChair": false,
      "tourGuide": false,
      "meetAndGreet": false
    }
  ]
}
\\\`\\\`\\\`

**【重要约束】**
- **按业务逻辑分离**：每个唯一的团号/订单号 + 日期组合都是独立订单
- **往返订单分离**：同一团号的不同日期服务（接机+送机）必须分为两个订单
- **Meet and Greet触发**：包含举牌、meet and greet等服务的订单必须设置isMultiOrder=true
- **车型特殊要求**：7 SEATER等特殊车型要求应正确映射到carTypeId
- 如果是单订单，orders数组包含一个完整解析的订单对象
- 如果是多订单，orders数组包含多个完整解析的订单对象
- 所有字段必须尽可能准确解析，无法确定的使用null或合理默认值
- rawText字段保存对应的原始文本片段
- 日期格式必须为YYYY-MM-DD，时间格式为HH:MM (24小时制)
- 响应中除了JSON代码块外，不得包含任何其他内容

${dateContext}

**待分析和解析的文本：**
${orderText}

请分析并返回完整的JSON结果：`;

            const response = await this.generateContent(enhancedMultiOrderPrompt);
            
            if (!response || !response.text) {
                throw new Error('Gemini响应为空');
            }

            this.logger?.log('🤖 Gemini一体化解析完成，处理响应...', 'info');

            // 🔧 增强调试：记录原始响应
            this.logger?.log('📝 Gemini原始响应内容:', 'debug', { 
                responseLength: response.text.length,
                responsePreview: response.text.substring(0, 500) + '...'
            });

            // 🔧 使用新的智能容错解析系统
            this.logger?.log('🔍 开始智能容错解析...', 'debug');

            const parseResult = this.intelligentErrorRecovery(response.text, 1);

            if (parseResult && parseResult.orders && parseResult.orders.length > 0) {
                this.logger?.log('✅ 智能容错解析成功', 'success', {
                    orderCount: parseResult.orderCount,
                    confidence: parseResult.confidence
                });

                return parseResult;
            }

            // 如果智能解析也失败，尝试传统解析作为最后手段
            this.logger?.log('⚠️ 智能解析失败，尝试传统解析...', 'warning');

            let jsonString = null;
            let fallbackResult = null;

            // 策略1: 标准 ```json 格式
            let jsonMatch = response.text.match(/```json\s*([\s\S]*?)\s*```/i);
            if (jsonMatch) {
                jsonString = jsonMatch[1].trim();
                this.logger?.log('✅ 发现标准JSON代码块格式', 'debug');
            }
            
            // 策略2: 不带语言标识的代码块
            if (!jsonString) {
                jsonMatch = response.text.match(/```\s*(\{[\s\S]*?\})\s*```/);
                if (jsonMatch) {
                    jsonString = jsonMatch[1].trim();
                    this.logger?.log('✅ 发现无语言标识的代码块格式', 'debug');
                }
            }
            
            // 策略3: 纯JSON对象匹配 (最后一个完整的JSON对象)
            if (!jsonString) {
                const jsonMatches = response.text.match(/\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}/g);
                if (jsonMatches && jsonMatches.length > 0) {
                    // 优先选择最大的JSON对象
                    jsonString = jsonMatches.reduce((longest, current) => 
                        current.length > longest.length ? current : longest
                    );
                    this.logger?.log('✅ 发现纯JSON对象格式', 'debug');
                }
            }
            
            // 策略4: 尝试修复常见JSON格式问题
            if (!jsonString) {
                // 查找看起来像JSON的部分
                const possibleJson = response.text.match(/[\s\S]*?(\{[\s\S]*orderCount[\s\S]*\})/);
                if (possibleJson) {
                    jsonString = possibleJson[1].trim();
                    this.logger?.log('⚠️ 发现可能的JSON片段，尝试修复', 'warning');
                }
            }
            
            if (!jsonString) {
                this.logger?.logError('❌ 所有JSON解析策略都失败', { 
                    responseText: response.text,
                    responseLength: response.text.length,
                    containsJson: response.text.includes('{'),
                    containsCodeBlock: response.text.includes('```')
                });
                throw new Error(`未找到有效的JSON响应格式。响应内容: ${response.text.substring(0, 300)}...`);
            }

            this.logger?.log('🔍 提取到的JSON字符串:', 'debug', { 
                jsonString: jsonString.substring(0, 500) + (jsonString.length > 500 ? '...' : ''),
                length: jsonString.length
            });

            // 尝试解析JSON，带错误恢复
            try {
                parseResult = JSON.parse(jsonString);
                this.logger?.log('✅ JSON解析成功', 'success');
            } catch (parseError) {
                this.logger?.log('⚠️ 首次JSON解析失败，尝试修复...', 'warning');
                
                // 尝试修复常见的JSON错误
                let fixedJson = jsonString
                    .replace(/，/g, ',')           // 中文逗号
                    .replace(/：/g, ':')           // 中文冒号
                    .replace(/"/g, '"')           // 中文引号
                    .replace(/"/g, '"')           // 中文引号
                    .replace(/\n\s*\/\/.*$/gm, '') // 移除注释
                    .replace(/,(\s*[}\]])/g, '$1') // 移除多余逗号
                    .replace(/([{\[,]\s*)(\w+):/g, '$1"$2":'); // 修复未加引号的键
                
                try {
                    parseResult = JSON.parse(fixedJson);
                    this.logger?.log('✅ JSON修复并解析成功', 'success');
                } catch (fixError) {
                    this.logger?.logError('❌ JSON修复也失败了', { 
                        originalError: parseError.message,
                        fixError: fixError.message,
                        originalJson: jsonString.substring(0, 200),
                        fixedJson: fixedJson.substring(0, 200)
                    });
                    throw new Error(`JSON解析失败: ${parseError.message}。原始内容: ${jsonString.substring(0, 200)}...`);
                }
            }

            // 验证和清理解析结果
            const cleanedResult = this.validateAndCleanOrderResult(parseResult);

            // 🔧 集成举牌服务检测逻辑
            const pagingServiceManager = window.getPagingServiceManager ? window.getPagingServiceManager() : null;
            if (pagingServiceManager) {
                // 检测原始文本是否包含举牌服务
                const hasPagingService = pagingServiceManager.detectPagingService(orderText);
                
                if (hasPagingService) {
                    this.logger?.log('🏷️ 检测到举牌服务，触发多订单模式', 'info');
                    
                    // 强制设置为多订单模式
                    cleanedResult.isMultiOrder = true;
                    
                    // 为所有订单标记需要举牌服务
                    if (cleanedResult.orders && cleanedResult.orders.length > 0) {
                        cleanedResult.orders.forEach(order => {
                            order.meetAndGreet = true;
                            order.needsPagingService = true;
                        });
                    }
                    
                    // 更新分析说明
                    cleanedResult.analysis = `${cleanedResult.analysis} - 检测到举牌服务，已触发多订单模式`;
                }
            }

            this.logger?.log(`🎯 一体化解析结果: ${cleanedResult.isMultiOrder ? '多订单' : '单订单'}`, 
                cleanedResult.isMultiOrder ? 'success' : 'info', {
                orderCount: cleanedResult.orderCount,
                confidence: cleanedResult.confidence,
                analysis: cleanedResult.analysis
            });

            return cleanedResult;

        } catch (error) {
            this.logger?.logError('Gemini一体化解析失败，回退处理', error);
            
            // 回退到最基本的结构
            return {
                isMultiOrder: false,
                orderCount: 1,
                confidence: 0,
                analysis: `解析失败: ${error.message}`,
                orders: [{
                    rawText: orderText,
                    customerName: null,
                    customerContact: null,
                    customerEmail: null,
                    pickup: null,
                    dropoff: null,
                    pickupDate: null,
                    pickupTime: null,
                    passengerCount: 1,
                    luggageCount: 1,
                    flightInfo: null,
                    otaReferenceNumber: null,
                    otaPrice: null,
                    currency: 'MYR',
                    carTypeId: 5, // 默认5 Seater
                    subCategoryId: 2, // 默认接机
                    drivingRegionId: 1, // 默认吉隆坡
                    languagesIdArray: [1, 2], // 默认中英文
                    extraRequirement: null,
                    babyChair: false,
                    tourGuide: false,
                    meetAndGreet: false
                }],
                originalText: orderText,
                error: error.message
            };
        }
    }

    /**
     * 验证和清理订单解析结果
     * @param {Object} result - Gemini返回的原始结果
     * @returns {Object} 清理后的标准结果
     */
    validateAndCleanOrderResult(result) {
        this.logger?.log('🔍 开始验证和清理订单结果', 'debug', {
            原始isMultiOrder: result.isMultiOrder,
            原始orderCount: result.orderCount,
            订单数组长度: result.orders ? result.orders.length : 0
        });

        // 确保基本结构存在
        const cleanedResult = {
            isMultiOrder: Boolean(result.isMultiOrder),
            orderCount: result.orderCount || (result.orders ? result.orders.length : 1),
            confidence: Math.max(0, Math.min(1, result.confidence || 0.8)),
            analysis: result.analysis || '解析完成',
            orders: []
        };

        // 处理订单数组
        if (result.orders && Array.isArray(result.orders)) {
            cleanedResult.orders = result.orders.map(order => this.validateOrderFields(order));
        } else {
            // 如果没有orders数组，创建一个默认订单
            cleanedResult.orders = [this.createDefaultOrder(result.originalText || '')];
        }

        // 🔧 强化多订单识别验证逻辑
        const actualOrderCount = cleanedResult.orders.length;
        
        // 第一阶段：基于订单数量的强制验证
        if (actualOrderCount > 1) {
            cleanedResult.isMultiOrder = true;
            cleanedResult.orderCount = actualOrderCount;
            this.logger?.log('✅ 基于订单数量识别为多订单', 'info', {
                实际订单数: actualOrderCount,
                强制设置: 'isMultiOrder=true'
            });
        }

        // 第二阶段：基于内容特征的多订单识别
        const multiOrderIndicators = this.analyzeMultiOrderIndicators(cleanedResult.orders, result.originalText);
        
        if (multiOrderIndicators.hasMultipleIndicators) {
            cleanedResult.isMultiOrder = true;
            cleanedResult.analysis += ` - 检测到多订单特征: ${multiOrderIndicators.reasons.join(', ')}`;
            this.logger?.log('✅ 基于内容特征识别为多订单', 'info', {
                检测原因: multiOrderIndicators.reasons,
                特征数量: multiOrderIndicators.indicatorCount
            });
        }

        // 第三阶段：举牌服务特殊处理
        if (multiOrderIndicators.hasPagingService) {
            cleanedResult.isMultiOrder = true;
            cleanedResult.analysis += ' - 检测到举牌服务，强制启用多订单模式';
            this.logger?.log('🏷️ 检测到举牌服务，强制多订单模式', 'info');
            
            // 为所有订单标记举牌服务
            cleanedResult.orders.forEach(order => {
                order.meetAndGreet = true;
                order.needsPagingService = true;
            });
        }

        // 第四阶段：最终一致性检查和修正
        const finalOrderCount = cleanedResult.orders.length;
        if (cleanedResult.isMultiOrder && finalOrderCount === 1) {
            // 多订单模式但只有一个订单 - 检查是否应该强制多订单（如举牌服务）
            if (!multiOrderIndicators.hasPagingService) {
                this.logger?.log('⚠️ 多订单标识与实际订单数不符，修正为单订单', 'warning', {
                    设置的isMultiOrder: cleanedResult.isMultiOrder,
                    实际订单数: finalOrderCount
                });
                cleanedResult.isMultiOrder = false;
            }
        }

        // 强制确保orderCount与实际长度一致
        cleanedResult.orderCount = finalOrderCount;

        this.logger?.log('🎯 订单验证和清理完成', 'success', {
            最终isMultiOrder: cleanedResult.isMultiOrder,
            最终orderCount: cleanedResult.orderCount,
            实际订单数: cleanedResult.orders.length,
            置信度: cleanedResult.confidence,
            多订单指标: multiOrderIndicators.indicatorCount
        });

        return cleanedResult;
    }

    /**
     * 分析多订单特征指标
     * @param {Array} orders - 订单数组
     * @param {string} originalText - 原始文本
     * @returns {Object} 多订单指标分析结果
     */
    analyzeMultiOrderIndicators(orders, originalText = '') {
        const indicators = {
            hasMultipleIndicators: false,
            hasPagingService: false,
            reasons: [],
            indicatorCount: 0
        };

        // 检测1：多个不同的团号/参考号
        const referenceNumbers = new Set();
        orders.forEach(order => {
            if (order.otaReferenceNumber) {
                referenceNumbers.add(order.otaReferenceNumber.toLowerCase());
            }
        });
        if (referenceNumbers.size > 1) {
            indicators.reasons.push(`多个团号(${referenceNumbers.size}个)`);
            indicators.indicatorCount++;
        }

        // 检测2：多个不同的日期
        const dates = new Set();
        orders.forEach(order => {
            if (order.pickupDate) {
                dates.add(order.pickupDate);
            }
        });
        if (dates.size > 1) {
            indicators.reasons.push(`多个日期(${dates.size}个)`);
            indicators.indicatorCount++;
        }

        // 检测3：多个不同的客户
        const customers = new Set();
        orders.forEach(order => {
            if (order.customerName) {
                customers.add(order.customerName.toLowerCase());
            }
            if (order.customerContact) {
                customers.add(order.customerContact);
            }
        });
        if (customers.size > 1) {
            indicators.reasons.push(`多个客户(${customers.size}个)`);
            indicators.indicatorCount++;
        }

        // 检测4：不同的服务类型（接机+送机）
        const serviceTypes = new Set();
        orders.forEach(order => {
            if (order.subCategoryId) {
                serviceTypes.add(order.subCategoryId);
            }
        });
        if (serviceTypes.size > 1) {
            indicators.reasons.push(`多种服务类型(${serviceTypes.size}种)`);
            indicators.indicatorCount++;
        }

        // 检测5：举牌/Meet and Greet服务关键词
        const pagingKeywords = [
            '举牌', '举牌接机', '举牌服务', 'meet and greet', 'meet & greet', 
            'meet-and-greet', '接机牌', '迎接服务', '接机服务', 'paging', 
            'paging service', '举牌迎接', '机场迎接', '接机员', '迎宾服务'
        ];
        
        const textToSearch = (originalText || '').toLowerCase();
        const orderTexts = orders.map(order => (order.rawText || '').toLowerCase()).join(' ');
        const allText = textToSearch + ' ' + orderTexts;

        const foundPagingKeywords = pagingKeywords.filter(keyword => 
            allText.includes(keyword.toLowerCase())
        );

        if (foundPagingKeywords.length > 0) {
            indicators.hasPagingService = true;
            indicators.reasons.push(`举牌服务(${foundPagingKeywords.length}个关键词)`);
            indicators.indicatorCount++;
        }

        // 检测6：聊天记录格式标识
        const chatPatterns = [
            /\[\d{4}\/\d{1,2}\/\d{1,2}\s+\d{1,2}:\d{2}\]/,  // [2025/7/10 17:27]
            /\d{1,2}:\d{2}\s*Joshua:/i,                      // 时间戳 + 姓名
            /\d{1,2}:\d{2}\s*[A-Za-z]+:/                     // 时间戳 + 用户名
        ];

        const hasChatFormat = chatPatterns.some(pattern => pattern.test(allText));
        if (hasChatFormat) {
            indicators.reasons.push('聊天记录格式');
            indicators.indicatorCount++;
        }

        // 检测7：换行分隔的多个订单结构
        const lineCount = (originalText || '').split('\n').filter(line => {
            // 检查每行是否包含典型的订单信息
            const trimmed = line.trim();
            return trimmed.length > 20 && (
                trimmed.includes('团号') || 
                trimmed.includes('客人') || 
                trimmed.includes('接机') || 
                trimmed.includes('送机') ||
                /\d{1,2}\/\d{1,2}/.test(trimmed) // 日期格式
            );
        }).length;

        if (lineCount >= 2) {
            indicators.reasons.push(`多行订单结构(${lineCount}行)`);
            indicators.indicatorCount++;
        }

        // 综合判断
        indicators.hasMultipleIndicators = indicators.indicatorCount >= 2 || 
                                         (indicators.indicatorCount >= 1 && orders.length > 1);

        this.logger?.log('🔍 多订单特征分析完成', 'debug', {
            指标数量: indicators.indicatorCount,
            检测原因: indicators.reasons,
            举牌服务: indicators.hasPagingService,
            多订单判定: indicators.hasMultipleIndicators
        });

        return indicators;
    }

    /**
     * 验证和清理单个订单字段 - 直接输出API格式字段名
     * @param {Object} order - 原始订单对象
     * @returns {Object} 清理后的订单对象（使用API标准字段名）
     */
    validateOrderFields(order) {
        this.logger?.log('🔧 开始字段验证和格式化', 'debug', {
            原始字段数: Object.keys(order).length,
            包含字段: Object.keys(order).slice(0, 8) // 显示前8个字段
        });

        const validatedOrder = {
            // === 原始数据字段 ===
            raw_text: order.rawText || '',

            // === 客户信息字段 - 直接使用API格式 ===
            customer_name: order.customerName || null,
            customer_contact: this.cleanPhoneNumber(order.customerContact),
            customer_email: this.validateEmail(order.customerEmail),

            // === 地点字段 - API标准格式 ===
            pickup: order.pickup || null,
            destination: order.dropoff || null,  // dropoff → destination

            // === 时间字段 - 直接使用API格式 ===
            date: this.validateDate(order.pickupDate),      // pickupDate → date
            time: this.validateTime(order.pickupTime),      // pickupTime → time

            // === 乘客和行李字段 - API标准格式 ===
            passenger_number: Math.max(1, parseInt(order.passengerCount) || 1), // passengerCount → passenger_number
            luggage_number: Math.max(0, parseInt(order.luggageCount) || 1),     // luggageCount → luggage_number

            // === 航班信息字段 - API标准格式 ===
            flight_info: order.flightInfo || null,         // flightInfo → flight_info

            // === OTA参考字段 - API标准格式 ===
            ota_reference_number: order.otaReferenceNumber || null, // otaReferenceNumber → ota_reference_number

            // === 价格字段 - API标准格式 ===
            ota_price: this.validatePrice(order.otaPrice),
            currency: this.validateCurrency(order.currency),

            // === 服务分类字段 - API标准格式 ===
            car_type_id: this.validateCarTypeId(order.carTypeId, order.passengerCount),     // carTypeId → car_type_id
            sub_category_id: this.validateSubCategoryId(order.subCategoryId),              // subCategoryId → sub_category_id
            driving_region_id: this.validateDrivingRegionId(order.drivingRegionId),        // drivingRegionId → driving_region_id

            // === 语言字段 - API标准格式 ===
            languages_id_array: this.validateLanguagesArray(order.languagesIdArray),       // languagesIdArray → languages_id_array

            // === 服务选项字段 - API标准格式 ===
            extra_requirement: order.extraRequirement || null,     // extraRequirement → extra_requirement
            baby_chair: Boolean(order.babyChair),                  // babyChair → baby_chair
            tour_guide: Boolean(order.tourGuide),                  // tourGuide → tour_guide
            meet_and_greet: Boolean(order.meetAndGreet)            // meetAndGreet → meet_and_greet
        };

        // 统计验证结果
        const nonNullFields = Object.keys(validatedOrder).filter(key => validatedOrder[key] !== null && validatedOrder[key] !== undefined);
        
        this.logger?.log('✅ 字段验证和格式化完成', 'debug', {
            输出字段数: Object.keys(validatedOrder).length,
            非空字段数: nonNullFields.length,
            API格式字段: '已全部转换为API标准格式',
            关键字段状态: {
                客户信息: !!validatedOrder.customer_name,
                地点信息: !!(validatedOrder.pickup && validatedOrder.destination),
                时间信息: !!(validatedOrder.date && validatedOrder.time),
                OTA参考: !!validatedOrder.ota_reference_number
            }
        });

        return validatedOrder;
    }

    // 字段验证辅助方法
    cleanPhoneNumber(phone) {
        if (!phone) return null;
        // 清理电话号码格式，保留数字、+、-、空格
        return phone.toString().replace(/[^\d+\-\s]/g, '').trim() || null;
    }

    validateEmail(email) {
        if (!email) return null;
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email) ? email : null;
    }

    validateDate(date) {
        if (!date) return null;
        // 验证YYYY-MM-DD格式
        const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
        if (dateRegex.test(date)) {
            const dateObj = new Date(date);
            if (!isNaN(dateObj.getTime())) {
                return date;
            }
        }
        return null;
    }

    validateTime(time) {
        if (!time) return null;
        // 验证HH:MM格式
        const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
        return timeRegex.test(time) ? time : null;
    }

    validatePrice(price) {
        const numPrice = parseFloat(price);
        return (!isNaN(numPrice) && numPrice >= 0) ? numPrice : null;
    }

    validateCurrency(currency) {
        const validCurrencies = ['MYR', 'USD', 'SGD', 'CNY'];
        return validCurrencies.includes(currency) ? currency : 'MYR';
    }

    validateCarTypeId(carTypeId, passengerCount) {
        const id = parseInt(carTypeId);
        
        // 如果提供了有效的车型ID，验证其有效性
        if (!isNaN(id) && id > 0) {
            try {
                const vehicleConfigManager = getVehicleConfigManager ? getVehicleConfigManager() : null;
                if (vehicleConfigManager && vehicleConfigManager.isValidCarTypeId(id)) {
                    return id;
                }
            } catch (error) {
                getLogger()?.log('车型ID验证失败，使用推荐逻辑', 'warning', { carTypeId: id, error: error.message });
            }
        }
        
        // 如果没有提供有效车型ID，根据乘客数量推荐
        return this.recommendCarType(passengerCount);
    }

    validateSubCategoryId(subCategoryId) {
        const id = parseInt(subCategoryId);
        const validIds = [2, 3, 4]; // 接机、送机、包车
        return validIds.includes(id) ? id : 2; // 默认接机
    }

    validateDrivingRegionId(drivingRegionId) {
        const id = parseInt(drivingRegionId);
        return (!isNaN(id) && id > 0) ? id : 1; // 默认吉隆坡
    }

    validateLanguagesArray(languages) {
        try {
            const languageManager = getLanguageManager();
            const validation = languageManager.validateLanguageIdsSync(languages);
            
            if (validation.valid && validation.validIds.length > 0) {
                return validation.validIds;
            } else {
                // 获取默认语言选择（使用同步方法以提高性能）
                const defaultSelection = languageManager.getDefaultSelectionSync('ai-analysis');
                return defaultSelection.length > 0 ? defaultSelection : [2]; // English fallback
            }
        } catch (error) {
            getLogger().logError('语言验证失败', error);
            return [2]; // English fallback
        }
    }

    createDefaultOrder(rawText) {
        return {
            rawText: rawText,
            customerName: null,
            customerContact: null,
            customerEmail: null,
            pickup: null,
            dropoff: null,
            pickupDate: null,
            pickupTime: null,
            passengerCount: 1,
            luggageCount: 1,
            flightInfo: null,
            otaReferenceNumber: null,
            otaPrice: null,
            currency: 'MYR',
            carTypeId: 5,
            subCategoryId: 2,
            drivingRegionId: 1,
            languagesIdArray: [1, 2],
            extraRequirement: null,
            babyChair: false,
            tourGuide: false,
            meetAndGreet: false
        };
    }

    /**
     * 解析图片分析返回的结构化文本，转换为标准订单数据格式
     * @param {string} analysisText - 图片分析返回的结构化文本
     * @returns {Object} 标准化的订单数据对象
     */
    parseImageAnalysisResult(analysisText) {
        if (!analysisText || typeof analysisText !== 'string') {
            return {
                isMultiOrder: false,
                orderCount: 0,
                confidence: 0,
                analysis: '图片分析结果为空',
                orders: []
            };
        }

        try {
            // 检查是否为无效结果
            if (analysisText.includes('无法识别') || analysisText.trim().length < 10) {
                return {
                    isMultiOrder: false,
                    orderCount: 0,
                    confidence: 0,
                    analysis: '图片中未识别到有效订单信息',
                    orders: []
                };
            }

            // 检查是否为部分信息
            if (analysisText.includes('部分信息:')) {
                const partialText = analysisText.replace('部分信息:', '').trim();
                return {
                    isMultiOrder: false,
                    orderCount: 1,
                    confidence: 0.3,
                    analysis: '识别到部分信息，需要人工确认',
                    orders: [{
                        rawText: partialText,
                        customerName: null,
                        customerContact: null,
                        customerEmail: null,
                        pickup: null,
                        dropoff: null,
                        pickupDate: null,
                        pickupTime: null,
                        passengerCount: 1,
                        luggageCount: 1,
                        flightInfo: null,
                        otaReferenceNumber: null,
                        otaPrice: null,
                        currency: 'MYR',
                        carTypeId: 5,
                        subCategoryId: 2,
                        drivingRegionId: 1,
                        languagesIdArray: [2, 4],
                        extraRequirement: `图片识别结果: ${partialText}`,
                        babyChair: false,
                        tourGuide: false,
                        meetAndGreet: false
                    }]
                };
            }

            // 分割多个订单（按"订单1:"、"订单2:"等标识分割）
            const orderSections = this.splitOrderSections(analysisText);
            const orders = [];
            let totalConfidence = 0;

            for (let i = 0; i < orderSections.length; i++) {
                const orderData = this.parseOrderSection(orderSections[i]);
                if (orderData) {
                    orders.push(orderData);
                    totalConfidence += orderData._confidence || 0.8;
                }
            }

            const avgConfidence = orders.length > 0 ? totalConfidence / orders.length : 0;

            return {
                isMultiOrder: orders.length > 1,
                orderCount: orders.length,
                confidence: Math.min(avgConfidence, 0.95), // 图片识别置信度上限95%
                analysis: `从图片识别到 ${orders.length} 个订单，平均置信度 ${Math.round(avgConfidence * 100)}%`,
                orders: orders
            };

        } catch (error) {
            getLogger().logError('图片分析结果解析失败', error);
            return {
                isMultiOrder: false,
                orderCount: 0,
                confidence: 0,
                analysis: `解析失败: ${error.message}`,
                orders: [],
                error: error.message
            };
        }
    }

    /**
     * 分割订单段落
     * @param {string} text - 分析文本
     * @returns {Array} 订单段落数组
     */
    splitOrderSections(text) {
        // 按订单标识分割
        const orderPattern = /订单\s*\d+\s*[:：]/g;
        const sections = [];
        
        if (orderPattern.test(text)) {
            // 有明确的订单标识
            const parts = text.split(orderPattern);
            // 跳过第一个空段落
            for (let i = 1; i < parts.length; i++) {
                if (parts[i].trim()) {
                    sections.push(parts[i].trim());
                }
            }
        } else {
            // 没有订单标识，作为单个订单处理
            sections.push(text.trim());
        }
        
        return sections.length > 0 ? sections : [text];
    }

    /**
     * 解析单个订单段落
     * @param {string} section - 订单段落文本
     * @returns {Object} 订单数据对象
     */
    parseOrderSection(section) {
        try {
            const orderData = {
                rawText: section,
                customerName: this.extractField(section, ['客户姓名', '姓名']),
                customerContact: this.extractField(section, ['联系电话', '电话', '手机']),
                customerEmail: this.extractField(section, ['客户邮箱', '邮箱']),
                pickup: this.extractField(section, ['上车地点', '出发地']),
                dropoff: this.extractField(section, ['目的地点', '目的地']),
                pickupDate: this.extractAndFormatDate(section, ['接送日期', '日期']),
                pickupTime: this.extractAndFormatTime(section, ['接送时间', '时间']),
                passengerCount: this.extractNumber(section, ['乘客人数', '人数']),
                luggageCount: this.extractNumber(section, ['行李件数', '行李']),
                flightInfo: this.extractField(section, ['航班信息', '航班号']),
                otaReferenceNumber: this.enhancedOtaReferenceExtractor(section), // 使用增强提取器
                // 使用统一的价格货币提取方法
                // 注意：这里使用otaPrice作为内部字段名，将在表单管理器中映射为API的ota_price字段
                ...(() => {
                    const priceAndCurrency = this.extractPriceAndCurrency(section);
                    return {
                        otaPrice: priceAndCurrency.price,
                        currency: priceAndCurrency.currency
                    };
                })(),
                carTypeId: this.recommendCarType(this.extractNumber(section, ['乘客人数', '人数'])),
                subCategoryId: this.detectServiceType(section),
                drivingRegionId: this.detectDrivingRegion(section),
                languagesIdArray: this.detectLanguages(section),
                extraRequirement: this.extractField(section, ['特殊要求', '备注', '要求']),
                babyChair: this.detectSpecialService(section, ['儿童座椅', '婴儿座椅']),
                tourGuide: this.detectSpecialService(section, ['导游', '司导']),
                meetAndGreet: this.detectSpecialService(section, ['迎接服务', '接机牌', '举牌']),
                _confidence: this.calculateSectionConfidence(section)
            };

            return orderData;

        } catch (error) {
            getLogger().logError('订单段落解析失败', error);
            return null;
        }
    }

    /**
     * 提取字段值
     * @param {string} text - 文本
     * @param {Array} labels - 标签数组
     * @returns {string|null} 提取的值
     */
    extractField(text, labels) {
        for (const label of labels) {
            const pattern = new RegExp(`${label}\\s*[:：]\\s*([^\\n\\r]+)`, 'i');
            const match = text.match(pattern);
            if (match && match[1]) {
                return match[1].trim().replace(/\[.*?\]/g, ''); // 移除[待确认]等标记
            }
        }
        return null;
    }

    /**
     * 提取并格式化日期
     * @param {string} text - 文本
     * @param {Array} labels - 标签数组
     * @returns {string|null} YYYY-MM-DD格式的日期
     */
    extractAndFormatDate(text, labels) {
        const dateValue = this.extractField(text, labels);
        if (dateValue) {
            // 验证日期格式
            const datePattern = /(\d{4})-(\d{2})-(\d{2})/;
            const match = dateValue.match(datePattern);
            if (match) {
                return dateValue;
            }
        }
        return null;
    }

    /**
     * 提取并格式化时间
     * @param {string} text - 文本
     * @param {Array} labels - 标签数组
     * @returns {string|null} HH:MM格式的时间
     */
    extractAndFormatTime(text, labels) {
        const timeValue = this.extractField(text, labels);
        if (timeValue) {
            // 验证时间格式
            const timePattern = /(\d{1,2}):(\d{2})/;
            const match = timeValue.match(timePattern);
            if (match) {
                const hour = match[1].padStart(2, '0');
                const minute = match[2];
                return `${hour}:${minute}`;
            }
        }
        return null;
    }

    /**
     * 提取数字
     * @param {string} text - 文本
     * @param {Array} labels - 标签数组
     * @returns {number|null} 提取的数字
     */
    extractNumber(text, labels) {
        const value = this.extractField(text, labels);
        if (value) {
            const numberMatch = value.match(/(\d+)/);
            if (numberMatch) {
                return parseInt(numberMatch[1], 10);
            }
        }
        return null;
    }

    /**
     * 提取价格
     * @param {string} text - 文本
     * @returns {number|null} 价格数值
     */
    extractPrice(text) {
        // 扩展的价格关键词列表
        const priceKeywords = [
            // 基础价格词汇
            '价格信息', '价格', '费用', '金额', '总价', '单价', '报价', '成本', '花费',
            // 商业收费术语
            '商家收取', '平台收费', '服务费', '手续费', '收费标准', '付款', '支付',
            // 英文关键词
            'price', 'cost', 'fee', 'amount', 'total', 'charge', 'payment', 'fare', 'rate'
        ];

        // 首先尝试通过关键词提取
        const priceField = this.extractField(text, priceKeywords);
        getLogger().log('价格字段提取', 'debug', { 
            found: !!priceField, 
            field: priceField?.substring(0, 100),
            keywordsUsed: priceKeywords.length
        });
        
        if (priceField) {
            // 增强的价格匹配正则，支持货币符号和单位
            const pricePatterns = [
                // 带货币符号的格式：¥100, $50, RM100
                /[¥$€£]?\s*(\d{1,6}(?:[,，]\d{3})*(?:\.\d{1,2})?)\s*(?:MYR|USD|CNY|SGD|人民币|美元|马币|令吉|元)?/g,
                // 纯数字格式
                /(\d{1,6}(?:[,，]\d{3})*(?:\.\d{1,2})?)/g
            ];

            for (const pattern of pricePatterns) {
                const matches = [...priceField.matchAll(pattern)];
                if (matches.length > 0) {
                    // 取第一个匹配的价格
                    const priceStr = matches[0][1].replace(/[,，]/g, '');
                    const price = parseFloat(priceStr);
                    if (price > 0 && price <= 99999) { // 合理性检查
                        getLogger().log(`价格提取成功: ${priceStr} -> ${price}`, 'info', {
                            source: priceField,
                            pattern: pattern.source
                        });
                        return price;
                    }
                }
            }
        }

        // 回退机制：在整个文本中寻找价格模式
        const fallbackPatterns = [
            // 寻找带有明确价格上下文的数字
            /(?:收取|费用|价格|金额|总计|合计)[：:\s]*[¥$RM]?\s*(\d{1,6}(?:[,，]\d{3})*(?:\.\d{1,2})?)/gi,
            // 寻找货币符号后的数字
            /[¥$RM]\s*(\d{1,6}(?:[,，]\d{3})*(?:\.\d{1,2})?)/g
        ];

        for (const pattern of fallbackPatterns) {
            const matches = [...text.matchAll(pattern)];
            if (matches.length > 0) {
                const priceStr = matches[0][1].replace(/[,，]/g, '');
                const price = parseFloat(priceStr);
                if (price > 0 && price <= 99999) {
                    getLogger().log(`价格回退提取成功: ${priceStr} -> ${price}`, 'info', {
                        source: matches[0][0],
                        pattern: pattern.source
                    });
                    return price;
                }
            }
        }

        getLogger().log('未能提取到有效价格', 'warning', { textSample: text.substring(0, 100) });
        return null;
    }

    /**
     * 提取货币类型
     * @param {string} text - 文本
     * @returns {string} 货币代码
     */
    extractCurrency(text) {
        // 货币识别模式 - 按优先级排序
        const currencyPatterns = [
            // 标准货币代码
            { pattern: /\bMYR\b/i, currency: 'MYR' },
            { pattern: /\bUSD\b/i, currency: 'USD' },
            { pattern: /\bSGD\b/i, currency: 'SGD' },
            { pattern: /\bCNY\b/i, currency: 'CNY' },
            
            // 货币符号
            { pattern: /RM\s*\d/i, currency: 'MYR' },
            { pattern: /\$\s*\d/, currency: 'USD' }, // 默认美元，除非有其他指示
            { pattern: /¥\s*\d/, currency: 'CNY' },
            
            // 中文货币名称
            { pattern: /马币|令吉|林吉特/i, currency: 'MYR' },
            { pattern: /美元|美金/i, currency: 'USD' },
            { pattern: /新币|新加坡元/i, currency: 'SGD' },
            { pattern: /人民币|元(?!素)/i, currency: 'CNY' } // 排除"元素"等词
        ];

        // 在完整文本中搜索货币指示符
        for (const { pattern, currency } of currencyPatterns) {
            if (pattern.test(text)) {
                getLogger().log(`货币识别成功: ${currency}`, 'info', {
                    pattern: pattern.source,
                    match: text.match(pattern)?.[0]
                });
                return currency;
            }
        }

        // 默认货币 - 基于业务地区
        getLogger().log('未检测到明确货币类型，使用默认值: MYR', 'info');
        return 'MYR';
    }

    /**
     * 统一提取价格和货币信息
     * @param {string} text - 文本
     * @returns {object} {price: number|null, currency: string}
     */
    extractPriceAndCurrency(text) {
        const result = {
            price: null,
            currency: 'MYR',
            source: null
        };

        try {
            getLogger().log('开始价格货币统一提取', 'debug', { 
                textLength: text.length,
                textSample: text.substring(0, 200) 
            });

        // 扩展的价格关键词
        const priceKeywords = [
            '价格信息', '价格', '费用', '金额', '总价', '单价', '报价', '成本', '花费',
            '商家收取', '平台收费', '服务费', '手续费', '收费标准', '付款', '支付',
            'price', 'cost', 'fee', 'amount', 'total', 'charge', 'payment', 'fare', 'rate'
        ];

        // 统一的价格-货币匹配模式
        const unifiedPatterns = [
            // 带明确货币符号的完整模式
            { pattern: /(?:收取|费用|价格|金额|总计|合计)[：:\s]*RM\s*(\d{1,6}(?:[,，]\d{3})*(?:\.\d{1,2})?)/gi, currency: 'MYR' },
            { pattern: /(?:收取|费用|价格|金额|总计|合计)[：:\s]*\$\s*(\d{1,6}(?:[,，]\d{3})*(?:\.\d{1,2})?)/gi, currency: 'USD' },
            { pattern: /(?:收取|费用|价格|金额|总计|合计)[：:\s]*¥\s*(\d{1,6}(?:[,，]\d{3})*(?:\.\d{1,2})?)/gi, currency: 'CNY' },
            
            // 带货币代码的模式
            { pattern: /(\d{1,6}(?:[,，]\d{3})*(?:\.\d{1,2})?)\s*MYR/gi, currency: 'MYR' },
            { pattern: /(\d{1,6}(?:[,，]\d{3})*(?:\.\d{1,2})?)\s*USD/gi, currency: 'USD' },
            { pattern: /(\d{1,6}(?:[,，]\d{3})*(?:\.\d{1,2})?)\s*CNY/gi, currency: 'CNY' },
            { pattern: /(\d{1,6}(?:[,，]\d{3})*(?:\.\d{1,2})?)\s*SGD/gi, currency: 'SGD' },
            
            // 中文货币名称模式
            { pattern: /(\d{1,6}(?:[,，]\d{3})*(?:\.\d{1,2})?)\s*(?:马币|令吉)/gi, currency: 'MYR' },
            { pattern: /(\d{1,6}(?:[,，]\d{3})*(?:\.\d{1,2})?)\s*(?:美元|美金)/gi, currency: 'USD' },
            { pattern: /(\d{1,6}(?:[,，]\d{3})*(?:\.\d{1,2})?)\s*人民币/gi, currency: 'CNY' },
            { pattern: /(\d{1,6}(?:[,，]\d{3})*(?:\.\d{1,2})?)\s*新币/gi, currency: 'SGD' }
        ];

            // 首先尝试统一模式匹配
            for (const { pattern, currency } of unifiedPatterns) {
                const matches = [...text.matchAll(pattern)];
                if (matches.length > 0) {
                    const priceStr = matches[0][1].replace(/[,，]/g, '');
                    const price = parseFloat(priceStr);
                    if (price > 0 && price <= 99999) {
                        result.price = price;
                        result.currency = currency;
                        result.source = matches[0][0];
                        getLogger().log(`价格货币统一提取成功: ${price} ${currency}`, 'info', {
                            source: matches[0][0],
                            pattern: pattern.source
                        });
                        return result;
                    } else {
                        getLogger().log(`价格超出合理范围: ${price}`, 'warning', {
                            source: matches[0][0]
                        });
                    }
                }
            }

            // 如果统一模式失败，分别提取价格和货币
            getLogger().log('统一模式未匹配，尝试分离提取', 'debug');
            result.price = this.extractPrice(text);
            result.currency = this.extractCurrency(text);
            
            if (result.price !== null) {
                getLogger().log(`分离提取成功: ${result.price} ${result.currency}`, 'info');
            } else {
                getLogger().log('价格提取完全失败', 'warning', {
                    textSample: text.substring(0, 100)
                });
            }

        } catch (error) {
            getLogger().logError('价格货币提取过程发生错误', error, {
                textSample: text.substring(0, 100)
            });
            // 返回默认值以确保系统稳定性
            result.price = null;
            result.currency = 'MYR';
        }

        return result;
    }

    /**
     * 检测服务类型
     * @param {string} text - 文本
     * @returns {number} 服务类型ID
     */
    detectServiceType(text) {
        const serviceField = this.extractField(text, ['服务类型']);
        if (serviceField) {
            if (serviceField.includes('接机')) return 2;
            if (serviceField.includes('送机')) return 3;
            if (serviceField.includes('包车')) return 4;
        }
        return 2; // 默认接机
    }

    /**
     * 检测行驶区域
     * @param {string} text - 文本
     * @returns {number} 区域ID
     */
    detectDrivingRegion(text) {
        const lowerText = text.toLowerCase();
        if (lowerText.includes('kl') || lowerText.includes('kuala lumpur') || lowerText.includes('吉隆坡')) return 1;
        if (lowerText.includes('penang') || lowerText.includes('槟城')) return 2;
        if (lowerText.includes('johor') || lowerText.includes('新山')) return 3;
        if (lowerText.includes('sabah') || lowerText.includes('沙巴')) return 4;
        if (lowerText.includes('singapore') || lowerText.includes('新加坡')) return 5;
        return 1; // 默认吉隆坡
    }

    // 🔧 语言检测方法已移除 - 现在完全依赖统一语言检测器 (js/core/language-detector.js)
    // 如需语言检测功能，请使用: window.OTA.unifiedLanguageDetector.detectAndApply(text, sourceField)

    /**
     * 推荐车型
     * @param {number} passengerCount - 乘客人数
     * @returns {number} 车型ID
     */
    recommendCarType(passengerCount) {
        try {
            // 优先使用车型配置管理器进行智能推荐
            const vehicleConfigManager = getVehicleConfigManager ? getVehicleConfigManager() : null;
            if (vehicleConfigManager) {
                const recommendation = vehicleConfigManager.recommendCarType(passengerCount);
                getLogger()?.log('使用车型配置管理器推荐车型', 'debug', { 
                    passengerCount, 
                    recommendedCarTypeId: recommendation.carTypeId 
                });
                return recommendation.carTypeId;
            }
        } catch (error) {
            getLogger()?.log('车型配置管理器不可用，使用降级逻辑', 'warning', { error: error.message });
        }

        // 降级逻辑：使用硬编码推荐规则
        const passengers = parseInt(passengerCount) || 0;
        if (!passengers || passengers <= 3) return 5; // 5 Seater - 统一默认
        if (passengers <= 4) return 37; // Extended 5
        if (passengers <= 5) return 15; // 7 Seater MPV
        if (passengers <= 6) return 32; // Velfire/Alphard
        if (passengers <= 7) return 20; // 10 Seater MPV
        if (passengers <= 10) return 23; // 14 Seater Van
        if (passengers <= 12) return 24; // 18 Seater Van
        return 25; // 30 Seat Mini Bus
    }

    /**
     * 检测特殊服务
     * @param {string} text - 文本
     * @param {Array} keywords - 关键词数组
     * @returns {boolean} 是否包含特殊服务
     */
    detectSpecialService(text, keywords) {
        const lowerText = text.toLowerCase();
        return keywords.some(keyword => 
            lowerText.includes(keyword.toLowerCase()) || 
            text.includes(keyword)
        );
    }

    /**
     * 计算段落置信度
     * @param {string} section - 订单段落
     * @returns {number} 置信度 (0-1)
     */
    calculateSectionConfidence(section) {
        let confidence = 0.5; // 基础置信度
        
        // 根据包含的字段数量提升置信度
        const fields = ['客户姓名', '联系电话', '接送日期', '接送时间', '上车地点', '目的地点'];
        let fieldCount = 0;
        
        for (const field of fields) {
            if (section.includes(field)) {
                fieldCount++;
            }
        }
        
        confidence += (fieldCount / fields.length) * 0.4; // 最多提升40%
        
        // 如果包含结构化标记，提升置信度
        if (section.includes('【订单信息】')) {
            confidence += 0.1;
        }
        
        return Math.min(confidence, 0.95); // 图片识别最高95%置信度
    }

    /**
     * 分析图片内容并提取订单信息 (增强版)
     * @param {string} base64Image - Base64编码的图片
     * @returns {Promise<Object>} 标准化的订单数据对象
     */
    async analyzeImage(base64Image) {
        if (!base64Image || typeof base64Image !== 'string') {
            throw new Error('无效的图片数据');
        }

        try {
            // 移除data:image前缀，保留纯base64
            const base64Data = base64Image.replace(/^data:image\/[a-z]+;base64,/, '');
            
            // 专业OTA订单图片分析提示词 - 与文本解析保持一致的业务逻辑
            const imageAnalysisPrompt = `
你是专业的OTA(在线旅游代理)订单图片分析专家，专门处理用车服务订单。

**核心任务**: 从图片中识别并提取完整的订单信息，按照标准化格式输出。

**分析步骤**:

1. **文字识别** - 准确识别图片中的所有文字内容（中文、英文、马来文、数字、符号）

2. **多订单检测** - 判断图片是否包含多个独立订单：
   - 多个不同的日期/时间
   - 多个不同的客户信息
   - 多个不同的航班信息
   - 明确的订单分隔标识

3. **订单信息提取** - 对每个订单提取以下关键信息：
   - **客户信息**: 姓名、电话、邮箱
   - **时间信息**: 日期、时间、航班号、航班时间
   - **地点信息**: 上车地点、目的地、机场代码
   - **服务信息**: 乘客人数、行李数量、车型要求
   - **价格信息**: 金额、货币类型
   - **特殊要求**: 儿童座椅、导游、迎接服务等

4. **业务规则应用**:
   - **服务类型判断**: 机场接送=接机/送机，市内=包车
   - **时间处理**: 直接使用客户指定的接送时间
   - **车型推荐**: 根据乘客人数智能推荐合适车型
   - **地点标准化**: 统一机场、酒店名称格式

**输出格式要求**:

如果识别到订单信息，请按以下格式输出完整的结构化订单描述：

\`\`\`
【订单信息】
客户姓名: [姓名]
联系电话: [电话号码]
客户邮箱: [邮箱地址]
服务类型: [接机/送机/包车]
航班信息: [航班号 时间]
接送日期: [YYYY-MM-DD]
接送时间: [HH:MM]
上车地点: [具体地址]
目的地点: [具体地址]
乘客人数: [数字]人
行李件数: [数字]件
特殊要求: [详细描述]
价格信息: [金额]
\`\`\`

如果是多个订单，请为每个订单单独输出上述格式，并在开头标注"订单1:"、"订单2:"等。

如果无法识别到完整订单信息，但有部分相关文字，请输出："部分信息: [识别到的文字内容]"

如果完全无法识别任何文字，请输出："无法识别"

**重要注意事项**:
- 确保日期格式为 YYYY-MM-DD
- 确保时间格式为 24小时制 HH:MM
- 机场代码要转换为完整机场名称
- 价格要明确标注货币类型(MYR/USD/SGD/CNY)
- 电话号码保留原始格式
- 如有疑问的信息，在字段后添加"[待确认]"标记

请开始分析图片:`;

            const requestBody = {
                contents: [{
                    parts: [
                        { text: imageAnalysisPrompt },
                        {
                            inline_data: {
                                mime_type: "image/jpeg", // 假设JPEG，Gemini会自动检测
                                data: base64Data
                            }
                        }
                    ]
                }],
                ...this.visionAPIConfig
            };

            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), this.timeout * 2); // 图片分析需要更长时间

            const response = await fetch(`${this.baseURL}?key=${this.apiKey}`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(requestBody),
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                const errorBody = await response.json();
                throw new Error(`Vision API请求失败: ${response.status} - ${errorBody.error?.message || '未知错误'}`);
            }

            const data = await response.json();
            const rawAnalysisText = data.candidates?.[0]?.content?.parts?.[0]?.text?.trim();
            
            // 在console中显示Gemini图片分析结果
            console.group('📷 Gemini图片分析返回结果');
            console.log('原始响应数据:', data);
            console.log('图片分析文本内容:', rawAnalysisText);
            console.groupEnd();
            
            if (rawAnalysisText) {
                getLogger().log('图片分析完成，正在解析结果...', 'info', { textLength: rawAnalysisText.length });
                
                // 使用新的解析方法转换为标准订单数据格式
                const parsedResult = this.parseImageAnalysisResult(rawAnalysisText);
                
                // 显示处理后的图片分析数据
                console.group('📊 图片分析处理结果');
                console.log('解析结果:', parsedResult);
                console.groupEnd();
                
                getLogger().log('图片分析结果解析完成', 'success', {
                    orderCount: parsedResult.orderCount,
                    confidence: parsedResult.confidence,
                    isMultiOrder: parsedResult.isMultiOrder
                });
                
                return parsedResult;
            } else {
                getLogger().log('图片中未检测到任何内容', 'warning');
                return this.parseImageAnalysisResult('无法识别');
            }

        } catch (error) {
            getLogger().logError('图片分析失败', error);
            if (error.name === 'AbortError') {
                throw new Error('图片分析超时');
            }
            throw new Error(`图片分析失败: ${error.message}`);
        }
    }

    /**
     * 初始化酒店知识库
     * 在系统启动时预加载酒店数据到内存
     */
    async initializeHotelKnowledgeBase() {
        try {
            getLogger().log('🏨 开始加载酒店知识库...', 'info');

            // 🔧 修复CORS问题：首先尝试内联酒店数据，然后才尝试加载文件
            if (window.inlineHotelData && window.inlineHotelData.loaded) {
                // 使用内联酒店数据
                getLogger().log('✅ 使用内联酒店数据初始化知识库', 'info');
                this.buildHotelSearchIndex(window.inlineHotelData.data);
                this.hotelKnowledgeBase.loaded = true;
                this.hotelKnowledgeBase.totalHotels = window.inlineHotelData.data.metadata?.total_hotels || 0;
                getLogger().log(`✅ 酒店知识库加载完成，共 ${this.hotelKnowledgeBase.totalHotels} 家酒店`, 'success');
                return;
            }

            // 尝试从网络加载（仅在HTTP/HTTPS环境下）
            if (window.location.protocol === 'http:' || window.location.protocol === 'https:') {
                const response = await fetch('./hotels_by_region.json');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const hotelData = await response.json();
                this.buildHotelSearchIndex(hotelData);

                this.hotelKnowledgeBase.loaded = true;
                this.hotelKnowledgeBase.totalHotels = hotelData.metadata?.total_hotels || 0;

                getLogger().log(`✅ 酒店知识库加载完成，共 ${this.hotelKnowledgeBase.totalHotels} 家酒店`, 'success');
            } else {
                throw new Error('本地文件协议不支持动态加载，请使用内联数据');
            }

        } catch (error) {
            this.hotelKnowledgeBase.loadError = error.message;

            // 检查是否是CORS错误或文件协议错误
            if (error.message.includes('Failed to fetch') || error.message.includes('CORS') || error.message.includes('本地文件协议')) {
                getLogger().log('❌ 酒店知识库加载失败: 无法访问外部文件', 'error');
                getLogger().log('💡 建议: 使用HTTP服务器运行应用，或确保内联数据已加载', 'warning');
                this.hotelKnowledgeBase.loadError = '无法访问外部文件，请检查运行环境';
            } else {
                getLogger().log(`❌ 酒店知识库加载失败: ${error.message}`, 'error');
            }

            // 🔧 启用降级模式：使用基础酒店映射数据
            this.enableFallbackHotelData();
            
            // 不抛出错误，允许系统继续运行，回退到AI翻译
            getLogger().log('🔄 系统已启用降级模式，将使用基础酒店数据和AI翻译', 'info');
        }
    }

    /**
     * 启用降级酒店数据
     */
    enableFallbackHotelData() {
        try {
            // 基础酒店映射数据（从现有代码中提取）
            const fallbackHotels = {
                metadata: { total_hotels: 50 },
                regions: {
                    'Kuala Lumpur': [
                        { name: 'Mandarin Oriental Kuala Lumpur', keywords: ['文华东方', 'Mandarin Oriental'] },
                        { name: 'Shangri-La Hotel Kuala Lumpur', keywords: ['香格里拉', 'Shangri-La'] },
                        { name: 'Hilton Kuala Lumpur', keywords: ['希尔顿', 'Hilton'] },
                        { name: 'DoubleTree by Hilton Kuala Lumpur', keywords: ['逸林', 'DoubleTree'] },
                        { name: 'JW Marriott Hotel Kuala Lumpur', keywords: ['万豪', 'Marriott'] },
                        { name: 'The Ritz-Carlton Kuala Lumpur', keywords: ['丽思卡尔顿', 'Ritz-Carlton'] },
                        { name: 'Grand Hyatt Kuala Lumpur', keywords: ['君悦', 'Grand Hyatt'] },
                        { name: 'Four Seasons Hotel Kuala Lumpur', keywords: ['四季', 'Four Seasons'] },
                        { name: 'Sunway Resort Hotel', keywords: ['双威', 'Sunway'] },
                        { name: 'PARKROYAL COLLECTION Kuala Lumpur', keywords: ['百乐海', 'PARKROYAL'] },
                        { name: 'The Majestic Hotel Kuala Lumpur', keywords: ['大华', 'Majestic'] },
                        { name: 'THE FACE Style Hotel', keywords: ['菲斯', 'FACE'] }
                    ],
                    'Penang': [
                        { name: 'Eastern & Oriental Hotel', keywords: ['东方大酒店', 'E&O'] },
                        { name: 'Hard Rock Hotel Penang', keywords: ['硬石', 'Hard Rock'] },
                        { name: 'Shangri-La Golden Sands, Penang', keywords: ['香格里拉金沙', 'Golden Sands'] },
                        { name: 'PARKROYAL Penang Resort', keywords: ['百乐海', 'PARKROYAL'] },
                        { name: 'Cheong Fatt Tze Mansion', keywords: ['蓝屋', '张弼士'] }
                    ],
                    'Johor Bahru': [
                        { name: 'DoubleTree by Hilton Hotel Johor Bahru', keywords: ['逸林', 'DoubleTree'] },
                        { name: 'Renaissance Johor Bahru Hotel', keywords: ['万丽', 'Renaissance'] },
                        { name: 'Legoland Hotel Malaysia', keywords: ['乐高', 'Legoland'] }
                    ],
                    'Kota Kinabalu': [
                        { name: 'Shangri-La Tanjung Aru, Kota Kinabalu', keywords: ['丹绒亚路香格里拉', 'Tanjung Aru'] },
                        { name: 'Shangri-La Rasa Ria, Kota Kinabalu', keywords: ['沙利雅香格里拉', 'Rasa Ria'] },
                        { name: 'The Magellan Sutera Resort', keywords: ['麦哲伦', 'Magellan'] },
                        { name: 'The Pacific Sutera Hotel', keywords: ['太平洋', 'Pacific'] },
                        { name: 'Hyatt Regency Kinabalu', keywords: ['凯悦', 'Hyatt'] }
                    ],
                    'Singapore': [
                        { name: 'Marina Bay Sands', keywords: ['滨海湾金沙', 'MBS'] },
                        { name: 'Raffles Singapore', keywords: ['莱佛士', 'Raffles'] },
                        { name: 'Mandarin Oriental Singapore', keywords: ['文华东方', 'Mandarin Oriental'] },
                        { name: 'Hotel Boss', keywords: ['老板酒店', 'Hotel Boss'] }
                    ]
                }
            };

            this.buildHotelSearchIndex(fallbackHotels);
            this.hotelKnowledgeBase.loaded = true;
            this.hotelKnowledgeBase.totalHotels = fallbackHotels.metadata.total_hotels;
            this.hotelKnowledgeBase.fallbackMode = true;

            getLogger().log(`✅ 降级酒店数据已启用，共 ${this.hotelKnowledgeBase.totalHotels} 家酒店`, 'success');
        } catch (error) {
            getLogger().logError('降级酒店数据启用失败', error);
        }
    }

    /**
     * 构建酒店搜索索引
     * @param {Object} hotelData - 酒店数据
     */
    buildHotelSearchIndex(hotelData) {
        const { chineseToEnglishMap, fuzzySearchIndex } = this.hotelKnowledgeBase;

        // 遍历所有区域的酒店数据
        Object.values(hotelData.hotels_by_region || {}).forEach(hotels => {
            hotels.forEach(hotel => {
                const chineseName = hotel.chinese_name;
                const englishName = hotel.english_name;
                const region = hotel.region;

                if (chineseName && englishName) {
                    // 精确匹配索引
                    chineseToEnglishMap.set(chineseName, {
                        english: englishName,
                        malay: this.generateMalayHotelName(englishName),
                        region: region,
                        source: 'knowledge_base'
                    });

                    // 模糊匹配索引 - 提取关键词
                    this.addToFuzzyIndex(chineseName, englishName, region, fuzzySearchIndex);
                }
            });
        });

        getLogger().log(`🔍 酒店搜索索引构建完成: 精确匹配 ${chineseToEnglishMap.size} 条，模糊匹配索引 ${fuzzySearchIndex.size} 条`, 'info');
    }

    /**
     * 添加到模糊搜索索引
     * @param {string} chineseName - 中文名称
     * @param {string} englishName - 英文名称
     * @param {string} region - 区域
     * @param {Map} fuzzyIndex - 模糊索引
     */
    addToFuzzyIndex(chineseName, englishName, region, fuzzyIndex) {
        // 提取关键词（去除常见的酒店后缀）
        const keywords = this.extractHotelKeywords(chineseName);

        keywords.forEach(keyword => {
            if (!fuzzyIndex.has(keyword)) {
                fuzzyIndex.set(keyword, []);
            }
            fuzzyIndex.get(keyword).push({
                chinese: chineseName,
                english: englishName,
                malay: this.generateMalayHotelName(englishName),
                region: region,
                source: 'knowledge_base'
            });
        });
    }

    /**
     * 提取酒店关键词
     * @param {string} hotelName - 酒店名称
     * @returns {Array} 关键词数组
     */
    extractHotelKeywords(hotelName) {
        // 移除常见的酒店后缀
        const suffixes = ['酒店', '宾馆', '旅馆', '度假村', '度假屋', '民宿', '客栈', '饭店', '大酒店', '国际酒店', '豪华酒店'];
        let cleanName = hotelName;

        suffixes.forEach(suffix => {
            cleanName = cleanName.replace(new RegExp(suffix + '$'), '');
        });

        // 分割关键词
        const keywords = [cleanName];

        // 添加品牌关键词（如果包含）
        const brands = ['香格里拉', '希尔顿', '万豪', '洲际', '凯悦', '喜来登', '威斯汀', '丽思卡尔顿', '四季', '文华东方'];
        brands.forEach(brand => {
            if (hotelName.includes(brand)) {
                keywords.push(brand);
            }
        });

        return keywords.filter(k => k.length > 0);
    }

    /**
     * 生成马来文酒店名称（简化版本）
     * @param {string} englishName - 英文名称
     * @returns {string} 马来文名称
     */
    generateMalayHotelName(englishName) {
        // 简化的马来文转换规则
        const malayTranslations = {
            'Hotel': 'Hotel',
            'Resort': 'Resort',
            'Inn': 'Inn',
            'Lodge': 'Lodge',
            'Suites': 'Suites',
            'International': 'Antarabangsa',
            'Grand': 'Grand',
            'Royal': 'Diraja',
            'Imperial': 'Imperial',
            'Palace': 'Istana',
            'Garden': 'Taman',
            'Beach': 'Pantai',
            'City': 'Bandar',
            'Tower': 'Menara',
            'Plaza': 'Plaza'
        };

        let malayName = englishName;
        Object.entries(malayTranslations).forEach(([english, malay]) => {
            malayName = malayName.replace(new RegExp(`\\b${english}\\b`, 'gi'), malay);
        });

        return malayName;
    }

    /**
     * 异步地址翻译（不阻塞主流程）
     * @param {string} address - 原始地址
     * @param {string} fieldName - 字段名称（pickup/dropoff）
     * @param {Object} dataObject - 数据对象引用
     */
    async translateAddressAsync(address, fieldName, dataObject) {
        if (!address || typeof address !== 'string' || address.trim().length < 3) {
            return; // 地址太短或无效，跳过翻译
        }

        // 检查是否包含中文字符，如果没有则跳过翻译
        if (!/[\u4e00-\u9fff]/.test(address)) {
            getLogger().log(`地址不包含中文，跳过翻译: ${address}`, 'debug');
            return;
        }

        try {
            const translatedAddresses = await this.translateAddress(address);

            // 将翻译结果添加到数据对象中
            if (translatedAddresses.malay) {
                dataObject[`${fieldName}_translated_ms`] = translatedAddresses.malay;
            }
            if (translatedAddresses.english) {
                dataObject[`${fieldName}_translated_en`] = translatedAddresses.english;
            }

            // 添加翻译来源信息
            dataObject[`${fieldName}_translation_source`] = translatedAddresses.source;
            if (translatedAddresses.region) {
                dataObject[`${fieldName}_region`] = translatedAddresses.region;
            }
            if (translatedAddresses.code) {
                dataObject[`${fieldName}_airport_code`] = translatedAddresses.code;
            }

            getLogger().log(`地址翻译完成: ${fieldName}`, 'info', {
                original: address,
                malay: translatedAddresses.malay,
                english: translatedAddresses.english,
                source: translatedAddresses.source,
                region: translatedAddresses.region,
                code: translatedAddresses.code
            });

        } catch (error) {
            getLogger().log(`地址翻译失败: ${fieldName}`, 'warning', {
                address,
                error: error.message
            });
            // 翻译失败不影响主流程，继续处理
        }
    }

    /**
     * 查询机场翻译
     * @param {string} address - 地址字符串
     * @returns {Object|null} 机场翻译结果或null
     */
    queryAirportTranslation(address) {
        // 精确匹配
        if (this.airportTranslations.has(address)) {
            const result = this.airportTranslations.get(address);
            getLogger().log(`✈️ 机场精确匹配: ${address} → ${result.english}`, 'info');
            return {
                english: result.english,
                malay: result.malay,
                region: result.region,
                code: result.code,
                source: 'airport_database'
            };
        }

        // 模糊匹配 - 检查是否包含机场关键词
        const airportKeywords = ['机场', '国际机场', 'airport', 'international airport'];
        const hasAirportKeyword = airportKeywords.some(keyword =>
            address.toLowerCase().includes(keyword.toLowerCase())
        );

        if (hasAirportKeyword) {
            // 尝试通过城市名匹配
            for (const [key, value] of this.airportTranslations.entries()) {
                // 检查地址是否包含机场所在城市
                if (address.includes(value.region) || key.includes(address.replace(/机场|国际机场/g, ''))) {
                    getLogger().log(`✈️ 机场模糊匹配: ${address} → ${value.english}`, 'info');
                    return {
                        english: value.english,
                        malay: value.malay,
                        region: value.region,
                        code: value.code,
                        source: 'airport_database'
                    };
                }
            }
        }

        return null;
    }

    /**
     * 从酒店知识库查询翻译
     * @param {string} address - 地址字符串
     * @returns {Object|null} 翻译结果或null
     */
    queryHotelKnowledgeBase(address) {
        if (!this.hotelKnowledgeBase.loaded) {
            return null;
        }

        const { chineseToEnglishMap, fuzzySearchIndex } = this.hotelKnowledgeBase;

        // 1. 精确匹配
        if (chineseToEnglishMap.has(address)) {
            const result = chineseToEnglishMap.get(address);
            getLogger().log(`🎯 酒店知识库精确匹配: ${address} → ${result.english}`, 'info');
            return result;
        }

        // 2. 模糊匹配
        const fuzzyResult = this.performFuzzyHotelSearch(address, fuzzySearchIndex);
        if (fuzzyResult) {
            getLogger().log(`🔍 酒店知识库模糊匹配: ${address} → ${fuzzyResult.english}`, 'info');
            return fuzzyResult;
        }

        return null;
    }

    /**
     * 执行模糊酒店搜索
     * @param {string} address - 地址字符串
     * @param {Map} fuzzyIndex - 模糊搜索索引
     * @returns {Object|null} 匹配结果或null
     */
    performFuzzyHotelSearch(address, fuzzyIndex) {
        // 提取地址中的关键词
        const addressKeywords = this.extractHotelKeywords(address);

        let bestMatch = null;
        let bestScore = 0;

        addressKeywords.forEach(keyword => {
            if (fuzzyIndex.has(keyword)) {
                const candidates = fuzzyIndex.get(keyword);
                candidates.forEach(candidate => {
                    // 计算匹配分数
                    const score = this.calculateMatchScore(address, candidate.chinese);
                    if (score > bestScore && score > 0.6) { // 设置最低匹配阈值
                        bestScore = score;
                        bestMatch = candidate;
                    }
                });
            }
        });

        return bestMatch;
    }

    /**
     * 计算匹配分数
     * @param {string} input - 输入地址
     * @param {string} candidate - 候选酒店名称
     * @returns {number} 匹配分数 (0-1)
     */
    calculateMatchScore(input, candidate) {
        // 简化的字符串相似度计算
        const inputClean = input.replace(/[酒店宾馆旅馆度假村民宿客栈饭店]/g, '');
        const candidateClean = candidate.replace(/[酒店宾馆旅馆度假村民宿客栈饭店]/g, '');

        // 包含关系检查
        if (inputClean.includes(candidateClean) || candidateClean.includes(inputClean)) {
            return 0.8;
        }

        // 计算公共字符数
        let commonChars = 0;
        const minLength = Math.min(inputClean.length, candidateClean.length);

        for (let i = 0; i < minLength; i++) {
            if (inputClean[i] === candidateClean[i]) {
                commonChars++;
            }
        }

        return commonChars / Math.max(inputClean.length, candidateClean.length);
    }

    /**
     * 地址翻译方法（集成酒店知识库）
     * @param {string} address - 需要翻译的中文地址
     * @param {Array} targetLanguages - 目标语言数组，默认['ms', 'en']
     * @returns {Promise<Object>} 翻译结果对象
     */
    async translateAddress(address, targetLanguages = ['ms', 'en']) {
        if (!address || typeof address !== 'string') {
            throw new Error('无效的地址输入');
        }

        // 优先查询机场翻译数据库
        const airportResult = this.queryAirportTranslation(address);
        if (airportResult) {
            return {
                original: address,
                malay: airportResult.malay,
                english: airportResult.english,
                source: 'airport_database',
                region: airportResult.region,
                code: airportResult.code
            };
        }

        // 其次查询酒店知识库
        const knowledgeBaseResult = this.queryHotelKnowledgeBase(address);
        if (knowledgeBaseResult) {
            return {
                original: address,
                malay: knowledgeBaseResult.malay,
                english: knowledgeBaseResult.english,
                source: 'knowledge_base',
                region: knowledgeBaseResult.region
            };
        }

        // 回退到 Gemini AI 翻译
        getLogger().log(`🤖 本地数据库无匹配，使用AI翻译: ${address}`, 'info');

        const prompt = `请将以下中文地址翻译为马来文和英文的官方标准地名：

地址: ${address}

要求：
1. 翻译为马来西亚官方使用的马来文地名
2. 翻译为标准英文地名
3. 保持地址的准确性和官方性
4. 如果是酒店名称，请提供官方英文名称
5. 如果是机场代码，请提供完整的官方名称
6. 如果是地标建筑，请提供官方译名

请以JSON格式返回：
\`\`\`json
{
    "original": "原始中文地址",
    "malay": "马来文官方地名",
    "english": "英文官方地名"
}
\`\`\``;

        try {
            const requestBody = {
                contents: [{
                    parts: [{ text: prompt }]
                }],
                generationConfig: {
                    temperature: 0.1, // 低温度确保翻译准确性
                    topK: 1,
                    topP: 0.5,
                    maxOutputTokens: 512
                },
                safetySettings: this.defaultAPIConfig.safetySettings
            };

            const response = await fetch(`${this.baseURL}?key=${this.apiKey}`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                throw new Error(`翻译API请求失败: ${response.status}`);
            }

            const data = await response.json();
            const rawText = data.candidates?.[0]?.content?.parts?.[0]?.text;

            if (!rawText) {
                throw new Error('翻译API返回内容为空');
            }

            // 解析JSON响应
            const jsonMatch = rawText.match(/```json\s*([\s\S]*?)\s*```/);
            const jsonString = jsonMatch ? jsonMatch[1].trim() : rawText.trim();

            const translationResult = JSON.parse(jsonString);

            return {
                original: translationResult.original || address,
                malay: translationResult.malay || null,
                english: translationResult.english || null,
                source: 'ai_translation'
            };

        } catch (error) {
            getLogger().log('地址翻译失败', 'error', {
                address,
                error: error.message
            });

            // 返回原始地址，确保不影响主流程
            return {
                original: address,
                malay: null,
                english: null,
                source: 'fallback'
            };
        }
    }

    /**
     * 生成内容的封装方法，支持重试机制
     * @param {string} prompt - 要发送的提示文本
     * @param {number} maxRetries - 最大重试次数
     * @returns {Promise<object>} - 包含生成内容的对象
     */
    async generateContent(prompt, maxRetries = 2) {
        const requestBody = {
            contents: [{
                parts: [{ text: prompt }]
            }],
            ...this.defaultAPIConfig
        };

        for (let attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), this.timeout);
                this.analysisState.currentRequest = controller;

                const response = await fetch(`${this.baseURL}?key=${this.apiKey}`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(requestBody),
                    signal: controller.signal
                });

                clearTimeout(timeoutId);

                if (!response.ok) {
                    const errorBody = await response.json();
                    throw new Error(`API请求失败: ${response.status} - ${errorBody.error?.message || '未知错误'}`);
                }

                const data = await response.json();
                const rawText = data.candidates?.[0]?.content?.parts?.[0]?.text;
                
                // 在console中显示通用generateContent结果
                console.group(`🤖 Gemini generateContent返回结果 (尝试 ${attempt + 1}/${maxRetries + 1})`);
                console.log('原始响应数据:', data);
                console.log('提取的文本内容:', rawText);
                console.groupEnd();
                
                if (!rawText) {
                    throw new Error('API返回内容为空或格式不正确');
                }

                return { text: rawText };

            } catch (error) {
                if (attempt === maxRetries) {
                    getLogger().logError('生成内容请求失败', error);
                    throw error;
                }
                // 可选：在重试前添加延迟
                await this.sleep(1000);
            }
        }
    }


    /**
     * 构建通用化提示词
     * @param {string} type - 提示词类型 ('standard', 'multiOrder', 'simple')
     * @param {Object} options - 构建选项
     * @returns {string} 构建的提示词
     */
    buildUniversalPrompt(type = 'standard', options = {}) {

        try {
            const templates = this.promptTemplates;
            let prompt = '';

            // 基础系统角色
            prompt += templates.systemRole.base + '\n';
            prompt += templates.systemRole.task + '\n';
            prompt += templates.systemRole.format + '\n\n';
            prompt += '---\n\n';

            // JSON Schema
            prompt += templates.jsonSchema.header + '\n\n';
            prompt += templates.jsonSchema.structure + '\n\n';
            prompt += '---\n\n';

            // 根据类型添加特定规则
            switch (type) {
                case 'multiOrder':
                    prompt += this.buildMultiOrderRules(options);
                    break;
                case 'simple':
                    prompt += this.buildSimpleRules(options);
                    break;
                case 'standard':
                default:
                    prompt += this.buildStandardRules(options);
                    break;
            }

            // 添加容错指令
            prompt += this.buildErrorHandlingInstructions();

            this.logger?.log(`✅ 构建${type}类型提示词完成`, 'info', {
                promptLength: prompt.length,
                type: type
            });

            return prompt;

        } catch (error) {
            this.logger?.logError('构建通用化提示词失败', error);
            return this.orderParsingPrompt; // 降级到原始提示词
        }
    }

    /**
     * 构建标准规则
     * @param {Object} options - 选项
     * @returns {string} 标准规则文本
     */
    buildStandardRules(options = {}) {
        return `### **处理规则**

**规则1: 多订单识别**
- 仔细阅读整个文本，判断是否包含多个独立订单
- 独立订单通常由换行符、分隔线或明确编号分隔
- 如果识别出多个订单，为每个订单创建独立的JSON对象

**规则2: 字段提取优先级**
- 优先提取明确标识的字段（如"客户姓名："、"联系电话："）
- 使用上下文推断缺失信息
- 对于模糊信息，选择最可能的解释

**规则3: 数据验证**
- 确保日期格式为YYYY-MM-DD
- 确保时间格式为HH:MM
- 确保数字字段为有效数值
- 确保布尔字段为true/false或null

`;
    }

    /**
     * 构建多订单规则
     * @param {Object} options - 选项
     * @returns {string} 多订单规则文本
     */
    buildMultiOrderRules(options = {}) {
        return `### **多订单处理规则**

**强制多订单检测条件：**
- 包含多个不同的日期（如16/7、17/7、21/7等）→ **强制设置orderCount>=2, isMultiOrder=true**
- 包含多个不同的订单号/团号→ **强制设置orderCount>=2, isMultiOrder=true**
- 包含接机和送机的组合→ **强制设置orderCount>=2, isMultiOrder=true**
- 包含举牌服务关键词→ **强制设置isMultiOrder=true**
- 包含多个不同的客户姓名或联系方式→ **强制设置orderCount>=2, isMultiOrder=true**

**分离逻辑：**
- 按换行符、分隔符或订单标识分离
- 每个分离的部分创建独立的订单对象
- 保持原始文本的完整性和上下文

`;
    }

    /**
     * 构建简化规则
     * @param {Object} options - 选项
     * @returns {string} 简化规则文本
     */
    buildSimpleRules(options = {}) {
        return `### **简化处理规则**

**基础提取：**
- 提取客户姓名、联系方式、地址信息
- 提取时间、日期相关信息
- 提取价格信息
- 其他字段可设为null

**格式要求：**
- 严格按照JSON Schema输出
- 未找到的字段设为null
- 确保JSON格式正确

`;
    }

    /**
     * 构建错误处理指令
     * @returns {string} 错误处理指令文本
     */
    buildErrorHandlingInstructions() {
        return `### **重要提醒**

**输出要求：**
- 必须返回有效的JSON数组格式
- 即使只有一个订单也要用数组包装
- 所有字段都必须包含，未找到的设为null
- 不要添加任何解释文字，只返回JSON

**错误处理：**
- 如果文本难以理解，尽力提取可识别的信息
- 对于模糊信息，选择最合理的解释
- 确保输出格式始终正确

---

`;
    }

    /**
     * 获取适合的提示词
     * @param {string} context - 上下文类型 ('single', 'multi', 'simple', 'retry')
     * @param {Object} options - 选项
     * @returns {string} 适合的提示词
     */
    getContextualPrompt(context = 'single', options = {}) {

        try {
            switch (context) {
                case 'multi':
                    return this.buildUniversalPrompt('multiOrder', options);
                case 'simple':
                    return this.buildUniversalPrompt('simple', options);
                case 'retry':
                    // 重试时使用简化版本
                    return this.buildUniversalPrompt('simple', { ...options, simplified: true });
                case 'single':
                default:
                    return this.universalPrompt || this.orderParsingPrompt;
            }
        } catch (error) {
            this.logger?.logError('获取上下文提示词失败', error);
            return this.orderParsingPrompt; // 降级到原始提示词
        }
    }

    /**
     * 智能提示词选择
     * @param {string} orderText - 订单文本
     * @param {Object} analysisContext - 分析上下文
     * @returns {string} 选择的提示词
     */
    selectOptimalPrompt(orderText, analysisContext = {}) {

        try {
            const textLength = orderText.length;
            const lineCount = orderText.split('\n').length;
            const hasMultipleTimePoints = (orderText.match(/\d{1,2}:\d{2}/g) || []).length > 1;
            const hasMultipleDates = (orderText.match(/\d{1,2}\/\d{1,2}/g) || []).length > 1;

            // 基于文本特征选择提示词
            if (hasMultipleDates || hasMultipleTimePoints || lineCount > 10) {
                this.logger?.log('🎯 选择多订单提示词', 'info');
                return this.getContextualPrompt('multi', analysisContext);
            } else if (textLength < 200 || analysisContext.isRetry) {
                this.logger?.log('🎯 选择简化提示词', 'info');
                return this.getContextualPrompt('simple', analysisContext);
            } else {
                this.logger?.log('🎯 选择标准提示词', 'info');
                return this.getContextualPrompt('single', analysisContext);
            }

        } catch (error) {
            this.logger?.logError('智能提示词选择失败', error);
            return this.orderParsingPrompt;
        }
    }

    /**
     * 🔧 增强版OTA参考号提取器
     * @param {string} text - 输入文本
     * @param {string} otaType - OTA平台类型（可选）
     * @returns {string|null} 提取的参考号或null
     */
    enhancedOtaReferenceExtractor(text, otaType = null) {
        if (!text || typeof text !== 'string') {
            return null;
        }

        const cleanText = text.trim();

        try {
            // 步骤1: 使用平台特定规则（如果指定了平台）
            if (otaType && this.otaReferenceConfig.platformRules[otaType]) {
                const platformRule = this.otaReferenceConfig.platformRules[otaType];
                for (const pattern of platformRule.patterns) {
                    const match = cleanText.match(pattern);
                    if (match) {
                        this.logger?.log(`✅ 平台特定规则匹配: ${match[0]} (${otaType})`, 'success');
                        return match[0];
                    }
                }
            }

            // 步骤2: 查找团号/确认号等明确标识
            for (const [key, pattern] of Object.entries(this.otaReferenceConfig.targetPatterns)) {
                if (key === 'teamNumber' || key === 'confirmationCode') {
                    const match = cleanText.match(pattern);
                    if (match && match[1]) {
                        const candidate = match[1].trim();
                        if (this.isValidOtaReference(candidate)) {
                            this.logger?.log(`✅ 标识符匹配: ${candidate} (${key})`, 'success');
                            return candidate;
                        }
                    }
                }
            }

            // 步骤3: 分词并逐个检查候选项
            const words = this.extractPotentialReferences(cleanText);
            const validCandidates = [];

            for (const word of words) {
                // 排除明显不是参考号的内容
                if (this.shouldExcludeAsReference(word)) {
                    continue;
                }

                // 检查是否匹配目标模式
                if (this.matchesTargetPattern(word)) {
                    validCandidates.push({
                        value: word,
                        score: this.calculateReferenceScore(word, otaType)
                    });
                }
            }

            // 步骤4: 选择最佳候选项
            if (validCandidates.length > 0) {
                validCandidates.sort((a, b) => b.score - a.score);
                const bestCandidate = validCandidates[0];

                this.logger?.log(`✅ 最佳候选项: ${bestCandidate.value} (得分: ${bestCandidate.score})`, 'success');
                return bestCandidate.value;
            }

            this.logger?.log('❌ 未找到有效的OTA参考号', 'warn');
            return null;

        } catch (error) {
            this.logger?.logError('OTA参考号提取失败', error);
            return null;
        }
    }

    /**
     * 提取潜在的参考号候选项
     * @param {string} text - 输入文本
     * @returns {Array<string>} 候选项数组
     */
    extractPotentialReferences(text) {
        // 使用多种分割方式提取候选项
        const separators = /[\s,，。；;：:\n\r\t]+/;
        const words = text.split(separators).filter(word => word.trim().length >= 4);

        // 额外提取括号内容和特殊格式
        const bracketMatches = text.match(/[\(（][^）\)]+[\)）]/g) || [];
        const colonMatches = text.match(/[:：]\s*([A-Z0-9\-]{4,20})/g) || [];

        const allCandidates = [
            ...words,
            ...bracketMatches.map(m => m.replace(/[\(（）\)]/g, '')),
            ...colonMatches.map(m => m.replace(/[:：]\s*/, ''))
        ];

        return [...new Set(allCandidates)].filter(c => c.trim().length >= 4);
    }

    /**
     * 检查是否应该排除某个候选项
     * @param {string} candidate - 候选项
     * @returns {boolean} 是否应该排除
     */
    shouldExcludeAsReference(candidate) {
        const cleanCandidate = candidate.trim();

        for (const [key, pattern] of Object.entries(this.otaReferenceConfig.excludePatterns)) {
            if (pattern.test(cleanCandidate)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查候选项是否匹配目标模式
     * @param {string} candidate - 候选项
     * @returns {boolean} 是否匹配
     */
    matchesTargetPattern(candidate) {
        const cleanCandidate = candidate.trim().toUpperCase();

        for (const pattern of Object.values(this.otaReferenceConfig.targetPatterns)) {
            if (pattern.test(cleanCandidate)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 计算参考号候选项的得分
     * @param {string} candidate - 候选项
     * @param {string} otaType - OTA平台类型
     * @returns {number} 得分
     */
    calculateReferenceScore(candidate, otaType) {
        let score = 0;
        const cleanCandidate = candidate.trim().toUpperCase();

        // 基础得分：长度合理性
        if (cleanCandidate.length >= 6 && cleanCandidate.length <= 20) {
            score += 10;
        }

        // 格式得分：字母数字组合
        if (/^[A-Z0-9\-_]+$/.test(cleanCandidate)) {
            score += 15;
        }

        // 平台特定得分
        if (otaType && this.otaReferenceConfig.platformRules[otaType]) {
            const platformRule = this.otaReferenceConfig.platformRules[otaType];
            for (const pattern of platformRule.patterns) {
                if (pattern.test(cleanCandidate)) {
                    score += platformRule.priority * 5;
                    break;
                }
            }
        }

        // 特殊格式得分
        if (cleanCandidate.includes('-') || cleanCandidate.includes('_')) {
            score += 5; // 分隔符通常表示结构化编号
        }

        return score;
    }

    /**
     * 验证参考号是否有效
     * @param {string} reference - 参考号
     * @returns {boolean} 是否有效
     */
    isValidOtaReference(reference) {
        if (!reference || typeof reference !== 'string') {
            return false;
        }

        const cleanRef = reference.trim();

        // 基本长度检查
        if (cleanRef.length < 4 || cleanRef.length > 30) {
            return false;
        }

        // 不能全是数字或全是字母
        if (/^\d+$/.test(cleanRef) || /^[A-Za-z]+$/.test(cleanRef)) {
            return false;
        }

        // 必须包含字母或数字
        if (!/[A-Za-z0-9]/.test(cleanRef)) {
            return false;
        }

        return true;
    }

    /**
     * 智能容错解析
     * @param {string} responseText - Gemini响应文本
     * @param {number} attempt - 当前尝试次数
     * @returns {Object} 解析结果
     */
    intelligentErrorRecovery(responseText, attempt = 1) {
        const maxAttempts = 4; // 增加一个容错策略

        this.logger?.log('🔍 开始智能容错解析', 'debug', {
            尝试次数: attempt,
            最大尝试: maxAttempts,
            响应长度: responseText.length,
            响应预览: responseText.substring(0, 200) + '...'
        });

        try {
            // 尝试标准JSON解析
            const result = this.parseGeminiResponse(responseText);
            if (result && result.orders) {
                this.logger?.log('✅ 标准JSON解析成功', 'success', {
                    isMultiOrder: result.isMultiOrder,
                    orderCount: result.orderCount || result.orders.length
                });
                return result;
            }

            // 如果标准解析失败，尝试容错策略
            if (attempt <= maxAttempts) {
                this.logger?.log(`🔄 尝试容错解析策略 ${attempt}/${maxAttempts}`, 'info');

                const strategies = [
                    () => this.tryAdvancedJsonExtraction(responseText),
                    () => this.tryFixJsonFormat(responseText),
                    () => this.tryMultiOrderStructureExtraction(responseText),
                    () => this.tryFallbackParsing(responseText)
                ];

                for (let i = attempt - 1; i < strategies.length; i++) {
                    const strategy = strategies[i];
                    if (strategy) {
                        const recoveredResult = strategy();
                        if (recoveredResult) {
                            // 🔧 确保多订单标识不丢失
                            const enhancedResult = this.preserveMultiOrderIndicators(recoveredResult, responseText);
                            
                            this.logger?.log(`✅ 容错策略 ${i + 1} 成功`, 'success', {
                                策略名: strategy.name || `策略${i + 1}`,
                                isMultiOrder: enhancedResult.isMultiOrder,
                                orderCount: enhancedResult.orderCount
                            });
                            return enhancedResult;
                        }
                    }
                }
            }

            // 所有策略都失败，返回默认结果
            this.logger?.log('❌ 所有容错策略失败，返回默认结果', 'warn');
            return this.createFallbackResult(responseText);

        } catch (error) {
            this.logger?.logError('智能容错解析失败', error);
            return this.createFallbackResult(responseText);
        }
    }

    /**
     * 尝试修复JSON格式
     * @param {string} text - 响应文本
     * @returns {Object|null} 修复后的结果
     */
    tryFixJsonFormat(text) {
        try {
            // 移除可能的markdown格式
            let cleanText = text.replace(/```json\s*|\s*```/g, '');

            // 尝试修复常见的JSON格式问题
            cleanText = cleanText.replace(/,\s*}/g, '}'); // 移除尾随逗号
            cleanText = cleanText.replace(/,\s*]/g, ']'); // 移除数组尾随逗号

            // 尝试解析
            const parsed = JSON.parse(cleanText);
            if (Array.isArray(parsed)) {
                return {
                    isMultiOrder: parsed.length > 1,
                    orderCount: parsed.length,
                    orders: parsed,
                    confidence: 0.7,
                    analysis: '通过JSON格式修复解析'
                };
            }

            return null;
        } catch (error) {
            return null;
        }
    }

    /**
     * 尝试提取部分数据
     * @param {string} text - 响应文本
     * @returns {Object|null} 部分数据结果
     */
    tryExtractPartialData(text) {
        try {
    
            // 使用正则表达式提取关键信息
            const customerName = text.match(/"customer_name":\s*"([^"]+)"/)?.[1];
            const customerContact = text.match(/"customer_contact":\s*"([^"]+)"/)?.[1];
            const pickup = text.match(/"pickup":\s*"([^"]+)"/)?.[1];
            const dropoff = text.match(/"dropoff":\s*"([^"]+)"/)?.[1];

            if (customerName || customerContact || pickup || dropoff) {
                const partialOrder = {
                    customer_name: customerName || null,
                    customer_contact: customerContact || null,
                    customer_email: null,
                    ota_reference_number: null,
                    flight_info: null,
                    departure_time: null,
                    arrival_time: null,
                    flight_type: null,
                    pickup_date: null,
                    pickup_time: null,
                    pickup: pickup || null,
                    dropoff: dropoff || null,
                    passenger_count: null,
                    luggage_count: null,
                    sub_category_id: 2, // 默认接机
                    car_type_id: 5, // 默认车型 - 统一使用5座标准车
                    driving_region_id: 1, // 默认区域
                    languages_id_array: [2], // 默认英语
                    baby_chair: null,
                    tour_guide: null,
                    meet_and_greet: null,
                    needs_paging_service: null,
                    price: null,
                    currency: null,
                    extra_requirement: null
                };

                this.logger?.log('✅ 成功提取部分数据', 'info');
                return {
                    isMultiOrder: false,
                    orderCount: 1,
                    orders: [partialOrder],
                    confidence: 0.5,
                    analysis: '通过部分数据提取解析'
                };
            }

            return null;
        } catch (error) {
            return null;
        }
    }

    /**
     * 高级JSON提取策略 - 尝试识别并恢复不完整的JSON
     * @param {string} text - 响应文本
     * @returns {Object|null} 解析结果
     */
    tryAdvancedJsonExtraction(text) {
        try {
            this.logger?.log('🔧 尝试高级JSON提取', 'debug');

            // 策略1: 寻找包含关键字段的JSON片段
            const jsonPatterns = [
                /\{[\s\S]*?isMultiOrder[\s\S]*?orderCount[\s\S]*?orders[\s\S]*?\}/,
                /\{[\s\S]*?orders\s*:\s*\[[\s\S]*?\][\s\S]*?\}/,
                /\{[\s\S]*?orderCount[\s\S]*?\}/
            ];

            for (const pattern of jsonPatterns) {
                const match = text.match(pattern);
                if (match) {
                    let jsonStr = match[0];
                    
                    // 尝试修复JSON格式问题
                    jsonStr = this.repairJsonString(jsonStr);
                    
                    try {
                        const parsed = JSON.parse(jsonStr);
                        if (parsed.orders || parsed.isMultiOrder !== undefined) {
                            return this.normalizeJsonResult(parsed);
                        }
                    } catch (e) {
                        // 继续尝试下一个模式
                    }
                }
            }

            return null;
        } catch (error) {
            this.logger?.log('高级JSON提取失败', 'debug', { error: error.message });
            return null;
        }
    }

    /**
     * 多订单结构提取策略 - 专门用于识别多订单特征
     * @param {string} text - 响应文本  
     * @returns {Object|null} 解析结果
     */
    tryMultiOrderStructureExtraction(text) {
        try {
            this.logger?.log('🔧 尝试多订单结构提取', 'debug');

            // 检查多订单关键指标
            const multiOrderKeywords = [
                'isMultiOrder.*true',
                'orderCount.*[2-9]',
                '团号.*EJBTBY.*\\d+.*团号.*EJBTBY.*\\d+', // 多个团号
                '\\d{1,2}/\\d{1,2}.*\\d{1,2}/\\d{1,2}', // 多个日期
                'meet.*greet|举牌'
            ];

            const hasMultiOrderIndicators = multiOrderKeywords.some(pattern => 
                new RegExp(pattern, 'i').test(text)
            );

            // 尝试提取订单数组
            const orderArrayMatch = text.match(/orders\s*:\s*\[([\s\S]*?)\]/);
            if (orderArrayMatch) {
                try {
                    // 尝试解析订单数组
                    const orderArrayText = `[${orderArrayMatch[1]}]`;
                    const orders = JSON.parse(orderArrayText);
                    
                    if (Array.isArray(orders) && orders.length > 0) {
                        return {
                            isMultiOrder: hasMultiOrderIndicators || orders.length > 1,
                            orderCount: orders.length,
                            orders: orders,
                            confidence: 0.8,
                            analysis: '通过多订单结构提取解析'
                        };
                    }
                } catch (e) {
                    // 数组解析失败，尝试分割处理
                }
            }

            // 如果有多订单指标但无法解析完整结构，创建基础结构
            if (hasMultiOrderIndicators) {
                return {
                    isMultiOrder: true,
                    orderCount: 2, // 保守估计
                    orders: [
                        this.createDefaultOrder(text),
                        this.createDefaultOrder(text)
                    ],
                    confidence: 0.6,
                    analysis: '基于多订单指标创建结构'
                };
            }

            return null;
        } catch (error) {
            this.logger?.log('多订单结构提取失败', 'debug', { error: error.message });
            return null;
        }
    }

    /**
     * 保持多订单指标 - 确保在容错过程中不丢失多订单标识
     * @param {Object} result - 解析结果
     * @param {string} originalText - 原始文本
     * @returns {Object} 增强后的结果
     */
    preserveMultiOrderIndicators(result, originalText) {
        try {
            // 如果结果中已经有正确的多订单标识，直接返回
            if (result.isMultiOrder && result.orderCount > 1) {
                return result;
            }

            // 分析原始文本中的多订单特征
            const textIndicators = this.analyzeMultiOrderIndicators(
                result.orders || [], 
                originalText
            );

            // 应用分析结果
            if (textIndicators.hasMultipleIndicators || textIndicators.hasPagingService) {
                result.isMultiOrder = true;
                result.orderCount = Math.max(result.orderCount || 1, result.orders ? result.orders.length : 1);
                
                if (result.analysis) {
                    result.analysis += ` - 容错分析检测到多订单特征: ${textIndicators.reasons.join(', ')}`;
                } else {
                    result.analysis = `容错分析检测到多订单特征: ${textIndicators.reasons.join(', ')}`;
                }

                this.logger?.log('🔧 多订单指标已保留', 'info', {
                    原始isMultiOrder: result.isMultiOrder,
                    检测到的特征: textIndicators.reasons,
                    最终orderCount: result.orderCount
                });
            }

            return result;
        } catch (error) {
            this.logger?.log('保持多订单指标失败', 'warning', { error: error.message });
            return result;
        }
    }

    /**
     * 修复JSON字符串格式
     * @param {string} jsonStr - JSON字符串
     * @returns {string} 修复后的JSON字符串
     */
    repairJsonString(jsonStr) {
        // 移除markdown标记
        jsonStr = jsonStr.replace(/```json\s*|\s*```/g, '');
        
        // 修复常见格式问题
        jsonStr = jsonStr.replace(/,\s*}/g, '}');  // 移除对象尾随逗号
        jsonStr = jsonStr.replace(/,\s*]/g, ']');  // 移除数组尾随逗号
        jsonStr = jsonStr.replace(/([{,]\s*)(\w+):/g, '$1"$2":'); // 给未引用的键加引号
        
        return jsonStr.trim();
    }

    /**
     * 规范化JSON解析结果
     * @param {Object} parsed - 解析的对象
     * @returns {Object} 规范化结果
     */
    normalizeJsonResult(parsed) {
        return {
            isMultiOrder: Boolean(parsed.isMultiOrder),
            orderCount: parsed.orderCount || (parsed.orders ? parsed.orders.length : 1),
            orders: parsed.orders || [],
            confidence: parsed.confidence || 0.7,
            analysis: parsed.analysis || '通过高级JSON提取解析'
        };
    }

    /**
     * 尝试降级解析
     * @param {string} text - 响应文本
     * @returns {Object} 降级结果
     */
    tryFallbackParsing(text) {

        // 创建最基本的订单结构
        const fallbackOrder = {
            customer_name: null,
            customer_contact: null,
            customer_email: null,
            ota_reference_number: null,
            flight_info: null,
            departure_time: null,
            arrival_time: null,
            flight_type: null,
            pickup_date: null,
            pickup_time: null,
            pickup: null,
            dropoff: null,
            passenger_count: 1,
            luggage_count: 1,
            sub_category_id: 2, // 默认接机
            car_type_id: 5, // 默认车型 - 统一使用5座标准车
            driving_region_id: 1, // 默认区域
            languages_id_array: [2], // 默认英语
            baby_chair: false,
            tour_guide: false,
            meet_and_greet: false,
            needs_paging_service: false,
            price: null,
            currency: 'MYR',
            extra_requirement: null
        };

        this.logger?.log('⚠️ 使用降级解析，返回默认订单结构', 'warn');
        return {
            isMultiOrder: false,
            orderCount: 1,
            orders: [fallbackOrder],
            confidence: 0.3,
            analysis: '降级解析 - 使用默认结构'
        };
    }

    /**
     * 创建降级结果
     * @param {string} originalText - 原始文本
     * @returns {Object} 降级结果
     */
    createFallbackResult(originalText) {
        return {
            isMultiOrder: false,
            orderCount: 0,
            orders: [],
            confidence: 0,
            analysis: '解析完全失败',
            error: '无法解析订单内容',
            originalText: originalText
        };
    }

} // GeminiService Class End

    // 创建全局Gemini服务实例（单例模式）
    let geminiServiceInstance = null;

    /**
     * @OTA_FACTORY 获取Gemini服务实例
     * @returns {GeminiService} Gemini服务实例
     */
    function getGeminiService() {
        if (!geminiServiceInstance) {
            geminiServiceInstance = new GeminiService();
        }
        return geminiServiceInstance;
    }

    // 创建默认实例以保持向后兼容性
    const geminiService = getGeminiService();

    // 将实例暴露到全局OTA命名空间
    window.OTA.geminiService = geminiService;
    window.OTA.getGeminiService = getGeminiService;

    // 为了向后兼容，也暴露到全局window
    window.geminiService = geminiService;
    window.getGeminiService = getGeminiService;
    
    // 注册到OTA注册中心
    if (window.OTA && window.OTA.Registry) {
        window.OTA.Registry.registerService('geminiService', geminiService, '@OTA_GEMINI_SERVICE');
        window.OTA.Registry.registerFactory('getGeminiService', getGeminiService, '@OTA_GEMINI_SERVICE_FACTORY');
    }

    // 送机时间提前计算逻辑已完全移除
    // 系统现在直接使用客户指定的接送时间，不进行任何自动计算

})();
