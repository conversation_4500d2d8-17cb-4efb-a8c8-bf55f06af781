/**
 * Fliggy OTA策略 - 特供处理逻辑
 * 
 * 专门处理Fliggy通道的业务逻辑
 * 完全隔离的处理方式，不影响其他OTA通道
 * 
 * @extends BaseOTAStrategy
 */
class FliggyOTAStrategy extends BaseOTAStrategy {
    constructor() {
        super('fliggy');
        
        // Fliggy特有的配置
        this.fliggyConfig = {
            // API端点
            apiEndpoint: 'https://fliggy-api.taobao.com',
            
            // 字段映射配置
            fieldMapping: {
                customerName: 'passenger_name',
                contactPhone: 'mobile_phone',
                contactEmail: 'email_address',
                idNumber: 'id_card_number',
                passportNumber: 'passport_number'
            },
            
            // 必填字段
            requiredFields: [
                'passenger_name',
                'mobile_phone',
                'id_card_number'
            ],
            
            // 验证规则
            validationRules: {
                mobile_phone: /^1[3-9]\d{9}$/,
                id_card_number: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
                email_address: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
            },
            
            // 特殊处理标识
            enableSpecialProcessing: true,
            enableDataEncryption: true,
            enableComplianceCheck: true
        };
    }

    /**
     * 策略激活时的初始化
     * @param {Object} metadata - 激活元数据
     */
    onActivate(metadata) {
        this.log('Fliggy strategy activated');
        
        // 设置Fliggy特有的处理环境
        this.setupFliggyEnvironment();
        
        // 加载Fliggy特定配置
        this.loadFliggyConfiguration();
        
        // 初始化合规检查
        if (this.fliggyConfig.enableComplianceCheck) {
            this.initializeComplianceCheck();
        }
    }

    /**
     * 策略停用时的清理
     */
    onDeactivate() {
        this.log('Fliggy strategy deactivated');
        
        // 清理Fliggy特定资源
        this.cleanupFliggyResources();
    }

    /**
     * 处理表单数据 - Fliggy特供逻辑
     * @param {Object} formData - 原始表单数据
     * @returns {Object} 处理后的数据
     */
    processFormData(formData) {
        this.log('Processing form data with Fliggy strategy');
        
        try {
            // 1. 数据验证
            const validation = this.validateFormData(formData);
            if (!validation.isValid) {
                throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
            }
            
            // 2. 字段映射转换
            const mappedData = this.mapFieldsToFliggy(formData);
            
            // 3. Fliggy特有的数据处理
            const processedData = this.applyFliggyProcessing(mappedData);
            
            // 4. 数据加密（如果启用）
            const finalData = this.fliggyConfig.enableDataEncryption 
                ? this.encryptSensitiveData(processedData)
                : processedData;
            
            // 5. 添加Fliggy处理标识
            return {
                ...finalData,
                _processed: true,
                _strategy: 'fliggy',
                _timestamp: Date.now(),
                _fliggyProcessed: true,
                _complianceChecked: this.fliggyConfig.enableComplianceCheck
            };
            
        } catch (error) {
            this.error('Failed to process form data:', error);
            
            // 发出错误事件
            this.emit('processing-error', {
                error: error.message,
                formData
            });
            
            throw error;
        }
    }

    /**
     * Fliggy特有的验证逻辑
     * @param {Object} formData - 要验证的数据
     * @returns {Array} 错误列表
     */
    customValidation(formData) {
        const errors = [];
        
        // 映射字段进行验证
        const mappedData = this.mapFieldsToFliggy(formData);
        
        // 检查必填字段
        for (const field of this.fliggyConfig.requiredFields) {
            if (!mappedData[field] || mappedData[field].trim() === '') {
                errors.push(`Fliggy required field missing: ${field}`);
            }
        }
        
        // 格式验证
        for (const [field, pattern] of Object.entries(this.fliggyConfig.validationRules)) {
            if (mappedData[field] && !pattern.test(mappedData[field])) {
                errors.push(`Invalid format for ${field}`);
            }
        }
        
        // Fliggy特有的业务规则验证
        errors.push(...this.validateFliggyBusinessRules(mappedData));
        
        return errors;
    }

    /**
     * 字段映射到Fliggy格式
     * @param {Object} formData - 原始表单数据
     * @returns {Object} 映射后的数据
     */
    mapFieldsToFliggy(formData) {
        const mappedData = {};
        
        // 标准字段映射
        for (const [sourceField, targetField] of Object.entries(this.fliggyConfig.fieldMapping)) {
            if (formData[sourceField] !== undefined) {
                mappedData[targetField] = formData[sourceField];
            }
        }
        
        // 保留未映射的字段
        for (const [key, value] of Object.entries(formData)) {
            if (!Object.keys(this.fliggyConfig.fieldMapping).includes(key)) {
                mappedData[key] = value;
            }
        }
        
        return mappedData;
    }

    /**
     * 应用Fliggy特有的数据处理
     * @param {Object} mappedData - 映射后的数据
     * @returns {Object} 处理后的数据
     */
    applyFliggyProcessing(mappedData) {
        const processedData = { ...mappedData };
        
        // 1. 手机号格式化
        if (processedData.mobile_phone) {
            processedData.mobile_phone = this.formatPhoneNumber(processedData.mobile_phone);
        }
        
        // 2. 身份证号处理
        if (processedData.id_card_number) {
            processedData.id_card_number = processedData.id_card_number.toUpperCase();
        }
        
        // 3. 姓名格式化
        if (processedData.passenger_name) {
            processedData.passenger_name = this.formatPassengerName(processedData.passenger_name);
        }
        
        // 4. 添加Fliggy特有字段
        processedData.channel_source = 'fliggy';
        processedData.booking_platform = 'taobao_travel';
        processedData.data_version = '2.0';
        
        // 5. 特殊处理逻辑
        if (this.fliggyConfig.enableSpecialProcessing) {
            processedData.special_handling = this.getSpecialHandlingInfo(processedData);
        }
        
        return processedData;
    }

    /**
     * 加密敏感数据
     * @param {Object} data - 要加密的数据
     * @returns {Object} 加密后的数据
     */
    encryptSensitiveData(data) {
        const encryptedData = { ...data };
        const sensitiveFields = ['id_card_number', 'passport_number', 'mobile_phone'];
        
        for (const field of sensitiveFields) {
            if (encryptedData[field]) {
                encryptedData[field] = this.encrypt(encryptedData[field]);
                encryptedData[`${field}_encrypted`] = true;
            }
        }
        
        return encryptedData;
    }

    /**
     * Fliggy业务规则验证
     * @param {Object} data - 要验证的数据
     * @returns {Array} 错误列表
     */
    validateFliggyBusinessRules(data) {
        const errors = [];
        
        // 1. 年龄限制检查
        if (data.id_card_number) {
            const age = this.calculateAgeFromIdCard(data.id_card_number);
            if (age < 0 || age > 120) {
                errors.push('Invalid age calculated from ID card');
            }
        }
        
        // 2. 手机号归属地检查（示例）
        if (data.mobile_phone) {
            const region = this.getPhoneRegion(data.mobile_phone);
            if (!this.isSupportedRegion(region)) {
                errors.push('Phone number region not supported');
            }
        }
        
        // 3. 姓名合规检查
        if (data.passenger_name) {
            if (!this.isValidChineseName(data.passenger_name)) {
                errors.push('Invalid Chinese name format');
            }
        }
        
        return errors;
    }

    /**
     * 设置Fliggy环境
     * @private
     */
    setupFliggyEnvironment() {
        // 设置Fliggy特有的全局变量或配置
        if (typeof window !== 'undefined') {
            window.FLIGGY_CHANNEL_ACTIVE = true;
            window.FLIGGY_CONFIG = this.fliggyConfig;
        }
    }

    /**
     * 加载Fliggy特定配置
     * @private
     */
    loadFliggyConfiguration() {
        // 从配置管理器加载Fliggy特定配置
        if (this.configManager) {
            const channelConfig = this.configManager.getChannelConfiguration('fliggy');
            this.fliggyConfig = { ...this.fliggyConfig, ...channelConfig };
        }
    }

    /**
     * 初始化合规检查
     * @private
     */
    initializeComplianceCheck() {
        this.log('Initializing Fliggy compliance check');
        // 实现合规检查初始化逻辑
    }

    /**
     * 清理Fliggy资源
     * @private
     */
    cleanupFliggyResources() {
        if (typeof window !== 'undefined') {
            delete window.FLIGGY_CHANNEL_ACTIVE;
            delete window.FLIGGY_CONFIG;
        }
    }

    /**
     * 格式化手机号
     * @param {string} phone - 原始手机号
     * @returns {string} 格式化后的手机号
     * @private
     */
    formatPhoneNumber(phone) {
        // 移除所有非数字字符
        const cleaned = phone.replace(/\D/g, '');
        
        // 确保是11位中国手机号
        if (cleaned.length === 11 && cleaned.startsWith('1')) {
            return cleaned;
        }
        
        return phone; // 如果格式不对，返回原值
    }

    /**
     * 格式化乘客姓名
     * @param {string} name - 原始姓名
     * @returns {string} 格式化后的姓名
     * @private
     */
    formatPassengerName(name) {
        // 移除多余的空格，规范化姓名格式
        return name.trim().replace(/\s+/g, ' ');
    }

    /**
     * 获取特殊处理信息
     * @param {Object} data - 处理数据
     * @returns {Object} 特殊处理信息
     * @private
     */
    getSpecialHandlingInfo(data) {
        return {
            requires_id_verification: !!data.id_card_number,
            requires_phone_verification: !!data.mobile_phone,
            vip_customer: this.isVipCustomer(data),
            risk_level: this.calculateRiskLevel(data)
        };
    }

    /**
     * 简单加密实现（示例）
     * @param {string} data - 要加密的数据
     * @returns {string} 加密后的数据
     * @private
     */
    encrypt(data) {
        // 这里应该使用真正的加密算法
        // 示例：简单的Base64编码
        return btoa(encodeURIComponent(data));
    }

    /**
     * 从身份证号计算年龄
     * @param {string} idCard - 身份证号
     * @returns {number} 年龄
     * @private
     */
    calculateAgeFromIdCard(idCard) {
        if (idCard.length !== 18) return -1;
        
        const birthYear = parseInt(idCard.substring(6, 10));
        const currentYear = new Date().getFullYear();
        
        return currentYear - birthYear;
    }

    /**
     * 获取手机号归属地
     * @param {string} phone - 手机号
     * @returns {string} 归属地
     * @private
     */
    getPhoneRegion(phone) {
        // 简化的归属地判断
        const prefix = phone.substring(0, 3);
        const regionMap = {
            '138': 'beijing',
            '139': 'shanghai',
            '136': 'guangzhou'
            // ... 更多映射
        };
        
        return regionMap[prefix] || 'unknown';
    }

    /**
     * 检查是否支持的地区
     * @param {string} region - 地区
     * @returns {boolean} 是否支持
     * @private
     */
    isSupportedRegion(region) {
        const supportedRegions = ['beijing', 'shanghai', 'guangzhou', 'shenzhen'];
        return supportedRegions.includes(region);
    }

    /**
     * 验证中文姓名格式
     * @param {string} name - 姓名
     * @returns {boolean} 是否有效
     * @private
     */
    isValidChineseName(name) {
        // 简单的中文姓名验证
        const chineseNamePattern = /^[\u4e00-\u9fa5]{2,4}$/;
        return chineseNamePattern.test(name.replace(/\s/g, ''));
    }

    /**
     * 判断是否VIP客户
     * @param {Object} data - 客户数据
     * @returns {boolean} 是否VIP
     * @private
     */
    isVipCustomer(data) {
        // 示例VIP判断逻辑
        return data.membership_level === 'vip' || data.annual_spending > 50000;
    }

    /**
     * 计算风险等级
     * @param {Object} data - 客户数据
     * @returns {string} 风险等级
     * @private
     */
    calculateRiskLevel(data) {
        // 简化的风险评估
        let score = 0;
        
        if (data.id_card_number) score += 30;
        if (data.mobile_phone) score += 20;
        if (data.email_address) score += 10;
        
        if (score >= 50) return 'low';
        if (score >= 30) return 'medium';
        return 'high';
    }
}

// 导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FliggyOTAStrategy;
} else if (typeof window !== 'undefined') {
    window.FliggyOTAStrategy = FliggyOTAStrategy;
}
