/**
 * OTA策略基类
 * 
 * 定义所有OTA策略的通用接口和基础功能
 * 每个具体的OTA通道策略都需要继承此基类
 * 
 * @abstract
 */
class BaseOTAStrategy {
    constructor(channelName) {
        if (this.constructor === BaseOTAStrategy) {
            throw new Error('BaseOTAStrategy is abstract and cannot be instantiated directly');
        }
        
        this.channelName = channelName;
        this.isActive = false;
        this.configManager = null;
        this.eventEmitter = null;
        this.configuration = {};
        this.metadata = {};
        
        // 策略特定的日志标识
        this.logPrefix = `[${channelName}Strategy]`;
    }

    /**
     * 设置配置管理器
     * @param {OTAConfigurationManager} configManager 
     */
    setConfigManager(configManager) {
        this.configManager = configManager;
        this.loadConfiguration();
    }

    /**
     * 设置事件发射器
     * @param {OTAEventBridge} eventEmitter 
     */
    setEventEmitter(eventEmitter) {
        this.eventEmitter = eventEmitter;
    }

    /**
     * 激活策略
     * @param {Object} metadata - 激活时的元数据
     */
    activate(metadata = {}) {
        this.log('Activating strategy...');
        
        this.metadata = { ...metadata };
        this.isActive = true;
        
        // 调用子类的激活逻辑
        this.onActivate(metadata);
        
        // 发出激活事件
        this.emit('strategy-activated', {
            channel: this.channelName,
            metadata: this.metadata
        });
        
        this.log('Strategy activated');
    }

    /**
     * 停用策略
     */
    deactivate() {
        if (!this.isActive) return;
        
        this.log('Deactivating strategy...');
        
        // 调用子类的停用逻辑
        this.onDeactivate();
        
        this.isActive = false;
        this.metadata = {};
        
        // 发出停用事件
        this.emit('strategy-deactivated', {
            channel: this.channelName
        });
        
        this.log('Strategy deactivated');
    }

    /**
     * 处理表单数据 - 主要业务接口
     * @param {Object} formData - 原始表单数据
     * @returns {Object} 处理后的表单数据
     * @abstract
     */
    processFormData(formData) {
        throw new Error('processFormData must be implemented by subclass');
    }

    /**
     * 获取策略配置
     * @returns {Object} 当前配置
     */
    getConfiguration() {
        return { ...this.configuration };
    }

    /**
     * 更新策略配置
     * @param {Object} newConfig - 新配置
     */
    updateConfiguration(newConfig) {
        this.configuration = { ...this.configuration, ...newConfig };
        this.onConfigurationUpdated(newConfig);
        
        this.emit('configuration-updated', {
            channel: this.channelName,
            configuration: this.configuration
        });
    }

    /**
     * 加载配置
     * @protected
     */
    loadConfiguration() {
        if (this.configManager) {
            this.configuration = this.configManager.getChannelConfiguration(this.channelName);
        }
    }

    /**
     * 验证表单数据
     * @param {Object} formData - 要验证的数据
     * @returns {Object} 验证结果 {isValid, errors}
     */
    validateFormData(formData) {
        const errors = [];
        
        // 基础验证逻辑
        if (!formData) {
            errors.push('Form data is required');
        }
        
        // 调用子类的验证逻辑
        const customErrors = this.customValidation(formData);
        errors.push(...customErrors);
        
        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * 自定义验证逻辑 - 子类可重写
     * @param {Object} formData - 要验证的数据
     * @returns {Array} 错误列表
     * @protected
     */
    customValidation(formData) {
        return []; // 默认无自定义验证
    }

    /**
     * 策略激活时的回调 - 子类可重写
     * @param {Object} metadata - 激活元数据
     * @protected
     */
    onActivate(metadata) {
        // 子类可重写此方法
    }

    /**
     * 策略停用时的回调 - 子类可重写
     * @protected
     */
    onDeactivate() {
        // 子类可重写此方法
    }

    /**
     * 配置更新时的回调 - 子类可重写
     * @param {Object} newConfig - 新配置
     * @protected
     */
    onConfigurationUpdated(newConfig) {
        // 子类可重写此方法
    }

    /**
     * 发出事件
     * @param {string} eventName - 事件名称
     * @param {Object} data - 事件数据
     * @protected
     */
    emit(eventName, data = {}) {
        if (this.eventEmitter) {
            this.eventEmitter.emit(eventName, {
                ...data,
                source: this.channelName,
                timestamp: Date.now()
            });
        }
    }

    /**
     * 记录日志
     * @param {...any} args - 日志参数
     * @protected
     */
    log(...args) {
        console.log(this.logPrefix, ...args);
    }

    /**
     * 记录警告
     * @param {...any} args - 警告参数
     * @protected
     */
    warn(...args) {
        console.warn(this.logPrefix, ...args);
    }

    /**
     * 记录错误
     * @param {...any} args - 错误参数
     * @protected
     */
    error(...args) {
        console.error(this.logPrefix, ...args);
    }

    /**
     * 获取策略信息
     * @returns {Object} 策略信息
     */
    getInfo() {
        return {
            channelName: this.channelName,
            isActive: this.isActive,
            hasConfiguration: Object.keys(this.configuration).length > 0,
            configurationKeys: Object.keys(this.configuration),
            metadata: { ...this.metadata }
        };
    }
}

/**
 * 默认OTA策略
 * 处理无特定通道或未识别通道的情况
 */
class DefaultOTAStrategy extends BaseOTAStrategy {
    constructor() {
        super('default');
    }

    /**
     * 处理表单数据 - 默认处理逻辑
     * @param {Object} formData - 原始表单数据
     * @returns {Object} 处理后的数据
     */
    processFormData(formData) {
        this.log('Processing form data with default strategy');
        
        // 默认策略：最小化处理，保持数据原样
        return {
            ...formData,
            _processed: true,
            _strategy: 'default',
            _timestamp: Date.now()
        };
    }

    /**
     * 自定义验证 - 默认验证逻辑
     * @param {Object} formData 
     * @returns {Array} 错误列表
     */
    customValidation(formData) {
        const errors = [];
        
        // 基本的必填字段检查
        const requiredFields = ['customerName', 'contactInfo'];
        for (const field of requiredFields) {
            if (!formData[field]) {
                errors.push(`${field} is required`);
            }
        }
        
        return errors;
    }
}

// 导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { BaseOTAStrategy, DefaultOTAStrategy };
} else if (typeof window !== 'undefined') {
    window.BaseOTAStrategy = BaseOTAStrategy;
    window.DefaultOTAStrategy = DefaultOTAStrategy;
}
