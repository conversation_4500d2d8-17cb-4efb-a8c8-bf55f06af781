# 方案一：模块化配置文件系统

## 📁 文件结构
```
js/ota-configs/
├── core/
│   ├── ota-config-manager.js      # 统一配置管理器
│   ├── ota-channel-registry.js    # 渠道注册中心
│   └── ota-config-validator.js    # 配置验证器
├── channels/
│   ├── fliggy-config.js           # 飞猪专属配置
│   ├── ctrip-config.js            # 携程专属配置
│   ├── klook-config.js            # Klook专属配置
│   ├── chong-dealer-config.js     # Chong Dealer专属配置
│   └── kkday-config.js            # KKday专属配置
└── shared/
    ├── common-rules.js            # 通用识别规则
    └── default-config.js          # 默认配置模板
```

## 🏗️ 架构设计

### 核心管理器
```javascript
// js/ota-configs/core/ota-config-manager.js
class OTAConfigManager {
    constructor() {
        this.configs = new Map();
        this.activeChannel = null;
        this.isolationMode = true; // 严格隔离模式
    }
    
    // 动态加载渠道配置
    async loadChannelConfig(channelName) {
        if (this.configs.has(channelName)) {
            return this.configs.get(channelName);
        }
        
        try {
            const config = await import(`../channels/${channelName.toLowerCase()}-config.js`);
            this.configs.set(channelName, config.default);
            return config.default;
        } catch (error) {
            console.warn(`无法加载${channelName}配置，使用默认配置`);
            return this.getDefaultConfig();
        }
    }
    
    // 设置当前处理渠道（独占模式）
    setActiveChannel(channelName) {
        if (this.isolationMode && this.activeChannel && this.activeChannel !== channelName) {
            throw new Error(`隔离模式下不能同时处理多个渠道: ${this.activeChannel} -> ${channelName}`);
        }
        this.activeChannel = channelName;
    }
    
    // 清理当前渠道上下文
    clearActiveChannel() {
        this.activeChannel = null;
    }
    
    // 获取当前渠道专属配置
    getCurrentChannelConfig() {
        if (!this.activeChannel) {
            throw new Error('未设置活跃渠道，无法获取配置');
        }
        return this.configs.get(this.activeChannel);
    }
}
```

### 飞猪专属配置示例
```javascript
// js/ota-configs/channels/fliggy-config.js
export default {
    channelName: 'Fliggy',
    isolation: true, // 严格隔离标记
    
    // 识别规则
    detection: {
        referencePatterns: [
            /订单编号\d{19}/,
            /^FG[A-Z0-9]{8,12}$/i
        ],
        keywords: ['fliggy', '飞猪', '阿里旅行'],
        confidence: 0.95
    },
    
    // 字段处理规则
    fieldProcessing: {
        customer_name: {
            transform: (value) => value?.trim(),
            validate: (value) => typeof value === 'string' && value.length > 0,
            required: true
        },
        pickup_address: {
            transform: async (value) => {
                // 飞猪专属：中文地址翻译
                if (/[\u4e00-\u9fa5]/.test(value)) {
                    return await translateChineseAddress(value);
                }
                return value;
            },
            validate: (value) => typeof value === 'string'
        }
    },
    
    // 定价规则
    pricing: {
        currency: 'CNY',
        calculator: (basePrice, orderData) => {
            let finalPrice = basePrice;
            
            // 飞猪专属定价逻辑
            if (orderData.is_premium_service) {
                finalPrice *= 1.2; // 20%增值服务费
            }
            
            return {
                originalPrice: basePrice,
                finalPrice: finalPrice,
                currency: 'CNY'
            };
        }
    },
    
    // UI定制
    ui: {
        theme: 'orange', // 飞猪橙色主题
        fieldOrder: ['customer_name', 'pickup_address', 'destination_address', 'pickup_time'],
        requiredFields: ['customer_name', 'pickup_address', 'pickup_time']
    },
    
    // 验证规则
    validation: {
        orderNumber: /^FG\d{8,}$/,
        phoneNumber: /^1[3-9]\d{9}$/, // 中国手机号
        requiredServices: ['addressTranslation']
    }
};
```

## ⚖️ 优势
- ✅ **完全隔离**: 每个渠道独立配置文件，零耦合
- ✅ **易于维护**: 修改某个渠道不影响其他渠道
- ✅ **动态加载**: 按需加载，减少内存占用
- ✅ **类型安全**: 每个配置都有验证机制
- ✅ **扩展性强**: 新增渠道只需新增配置文件

## ⚠️ 劣势
- ❌ **文件数量多**: 需要管理多个配置文件
- ❌ **初期开发量大**: 需要拆分现有配置
- ❌ **调试复杂**: 分散的配置文件增加调试难度

## 🚀 实施步骤
1. 创建核心管理器框架
2. 迁移飞猪配置（完全隔离）
3. 逐步迁移其他渠道配置
4. 更新现有代码调用新的配置系统
5. 清理旧的分散配置
