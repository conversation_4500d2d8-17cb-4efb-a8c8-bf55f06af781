/**
 * OTA系统集成配置
 * 
 * 将调和架构集成到现有系统中
 * 这个文件负责将所有组件注册到依赖注入容器和应用启动流程中
 */

(function() {
    'use strict';

    /**
     * OTA系统集成器
     */
    class OTASystemIntegrator {
        constructor() {
            this.initialized = false;
            this.components = new Map();
            
            this.log('OTASystemIntegrator created');
        }

        /**
         * 集成OTA系统到现有架构
         */
        async integrate() {
            try {
                this.log('Starting OTA system integration...');

                // 1. 验证现有架构组件
                this.validateExistingArchitecture();

                // 2. 注册OTA组件到依赖注入容器
                this.registerComponents();

                // 3. 集成到应用启动流程
                this.integrateWithBootstrap();

                // 4. 设置事件监听
                this.setupEventListeners();

                // 5. 注册到全局注册中心
                this.registerWithGlobalRegistry();

                this.initialized = true;
                this.log('OTA system integration completed successfully');

                return true;

            } catch (error) {
                this.error('Failed to integrate OTA system:', error);
                throw error;
            }
        }

        /**
         * 验证现有架构组件
         */
        validateExistingArchitecture() {
            const required = [
                'DependencyContainer',
                'ApplicationBootstrap'
            ];

            const optional = [
                'GlobalEventCoordinator',
                'OTA.Registry'
            ];

            // 检查必需组件
            for (const component of required) {
                if (!this.getGlobalComponent(component)) {
                    throw new Error(`Required component ${component} not found`);
                }
            }

            // 检查可选组件
            for (const component of optional) {
                if (!this.getGlobalComponent(component)) {
                    this.warn(`Optional component ${component} not found, some features may be limited`);
                }
            }

            this.log('Architecture validation completed');
        }

        /**
         * 注册OTA组件到依赖注入容器
         */
        registerComponents() {
            const container = this.getGlobalComponent('DependencyContainer');
            if (!container) {
                throw new Error('DependencyContainer not available');
            }

            this.log('Registering OTA components...');

            // 注册配置管理器
            container.register('OTAConfigurationManager', () => {
                if (!this.components.has('configManager')) {
                    this.components.set('configManager', new OTAConfigurationManager());
                }
                return this.components.get('configManager');
            }, {
                singleton: true,
                tags: ['ota', 'configuration', 'manager']
            });

            // 注册OTA管理器
            container.register('OTAManager', () => {
                if (!this.components.has('otaManager')) {
                    this.components.set('otaManager', new OTAManager());
                }
                return this.components.get('otaManager');
            }, {
                singleton: true,
                dependencies: ['OTAConfigurationManager'],
                tags: ['ota', 'manager', 'primary']
            });

            // 注册策略工厂
            container.register('OTAStrategyFactory', () => {
                return this.createStrategyFactory();
            }, {
                singleton: true,
                tags: ['ota', 'factory', 'strategy']
            });

            // 注册事件桥接器
            container.register('OTAEventBridge', () => {
                const otaManager = container.get('OTAManager');
                if (!this.components.has('eventBridge')) {
                    this.components.set('eventBridge', new OTAEventBridge(otaManager));
                }
                return this.components.get('eventBridge');
            }, {
                singleton: true,
                dependencies: ['OTAManager'],
                tags: ['ota', 'event', 'bridge']
            });

            this.log('OTA components registered successfully');
        }

        /**
         * 集成到应用启动流程
         */
        integrateWithBootstrap() {
            const bootstrap = this.getGlobalComponent('ApplicationBootstrap');
            if (!bootstrap) {
                throw new Error('ApplicationBootstrap not available');
            }

            this.log('Integrating with application bootstrap...');

            // 在managers阶段添加OTA管理器初始化
            bootstrap.addPhaseTask('managers', {
                name: 'ota-system-initialization',
                priority: 7, // 在其他管理器之后，UI管理器之前
                task: async () => {
                    await this.initializeOTASystem();
                },
                dependencies: ['dependency-injection', 'services'],
                description: 'Initialize OTA management system'
            });

            // 在finalization阶段添加验证
            bootstrap.addPhaseTask('finalization', {
                name: 'ota-system-validation',
                priority: 2,
                task: async () => {
                    await this.validateOTASystemIntegration();
                },
                dependencies: ['ota-system-initialization'],
                description: 'Validate OTA system integration'
            });

            this.log('Bootstrap integration completed');
        }

        /**
         * 初始化OTA系统
         */
        async initializeOTASystem() {
            this.log('Initializing OTA system...');

            const container = this.getGlobalComponent('DependencyContainer');

            // 1. 初始化配置管理器
            const configManager = container.get('OTAConfigurationManager');
            await configManager.initialize();

            // 2. 初始化OTA管理器
            const otaManager = container.get('OTAManager');
            await otaManager.initialize();

            // 3. 初始化事件桥接器
            const eventBridge = container.get('OTAEventBridge');
            eventBridge.setupEventHandlers();

            // 4. 注册默认策略
            await this.registerDefaultStrategies(otaManager);

            this.log('OTA system initialized successfully');
        }

        /**
         * 注册默认策略
         */
        async registerDefaultStrategies(otaManager) {
            this.log('Registering default OTA strategies...');

            // 注册Fliggy策略
            const fliggyStrategy = new FliggyOTAStrategy();
            otaManager.registerStrategy('fliggy', fliggyStrategy);

            // 注册其他策略（如果可用）
            if (typeof BookingOTAStrategy !== 'undefined') {
                otaManager.registerStrategy('booking', new BookingOTAStrategy());
            }

            if (typeof ExpediaOTAStrategy !== 'undefined') {
                otaManager.registerStrategy('expedia', new ExpediaOTAStrategy());
            }

            // 注册默认策略
            otaManager.registerStrategy('default', new DefaultOTAStrategy());

            this.log('Default strategies registered');
        }

        /**
         * 创建策略工厂
         */
        createStrategyFactory() {
            return {
                createStrategy: (channel, config = {}) => {
                    switch (channel) {
                        case 'fliggy':
                            return new FliggyOTAStrategy();
                        case 'booking':
                            return new BookingOTAStrategy();
                        case 'expedia':
                            return new ExpediaOTAStrategy();
                        case 'default':
                        default:
                            return new DefaultOTAStrategy();
                    }
                },

                getAvailableStrategies: () => {
                    return ['fliggy', 'booking', 'expedia', 'default'];
                },

                isStrategySupported: (channel) => {
                    return ['fliggy', 'booking', 'expedia', 'default'].includes(channel);
                }
            };
        }

        /**
         * 设置事件监听
         */
        setupEventListeners() {
            this.log('Setting up global event listeners...');

            const globalEventCoordinator = this.getGlobalComponent('GlobalEventCoordinator');
            if (globalEventCoordinator) {
                // 监听应用关闭事件
                globalEventCoordinator.on('application-shutdown', () => {
                    this.cleanup();
                });

                // 监听配置变更事件
                globalEventCoordinator.on('global-configuration-changed', (data) => {
                    this.handleGlobalConfigurationChange(data);
                });
            }

            this.log('Event listeners set up');
        }

        /**
         * 注册到全局注册中心
         */
        registerWithGlobalRegistry() {
            const registry = this.getGlobalComponent('OTA.Registry');
            if (registry) {
                this.log('Registering with OTA global registry...');

                // 注册管理器
                registry.registerService('OTAManager', {
                    type: 'manager',
                    instance: this.components.get('otaManager'),
                    description: 'Main OTA channel management system',
                    version: '1.0.0'
                });

                // 注册配置管理器
                registry.registerService('OTAConfigurationManager', {
                    type: 'service',
                    instance: this.components.get('configManager'),
                    description: 'OTA configuration management service',
                    version: '1.0.0'
                });

                this.log('Registry registration completed');
            }
        }

        /**
         * 验证OTA系统集成
         */
        async validateOTASystemIntegration() {
            this.log('Validating OTA system integration...');

            const container = this.getGlobalComponent('DependencyContainer');
            
            // 验证所有组件都已正确注册和初始化
            const requiredComponents = [
                'OTAManager',
                'OTAConfigurationManager',
                'OTAEventBridge',
                'OTAStrategyFactory'
            ];

            for (const component of requiredComponents) {
                try {
                    const instance = container.get(component);
                    if (!instance) {
                        throw new Error(`Component ${component} not properly initialized`);
                    }
                    
                    // 检查初始化状态
                    if (instance.initialized === false) {
                        throw new Error(`Component ${component} not initialized`);
                    }
                    
                } catch (error) {
                    this.error(`Validation failed for ${component}:`, error);
                    throw error;
                }
            }

            // 验证策略注册
            const otaManager = container.get('OTAManager');
            const status = otaManager.getStatus();
            
            if (status.strategyCount < 2) { // 至少应该有default和fliggy
                throw new Error('Insufficient strategies registered');
            }

            this.log('OTA system integration validation passed');
        }

        /**
         * 处理全局配置变更
         */
        handleGlobalConfigurationChange(data) {
            this.log('Handling global configuration change:', data);

            if (data.affects && data.affects.includes('ota')) {
                const configManager = this.components.get('configManager');
                if (configManager) {
                    configManager.updateDefaultConfiguration(data.otaConfig || {});
                }
            }
        }

        /**
         * 获取全局组件
         */
        getGlobalComponent(name) {
            if (typeof window === 'undefined') return null;

            // 尝试多种路径
            const paths = [
                `window.${name}`,
                `window.APP.${name}`,
                `window.OTA.${name}`
            ];

            for (const path of paths) {
                try {
                    const components = path.split('.');
                    let current = window;
                    
                    for (let i = 1; i < components.length; i++) {
                        current = current[components[i]];
                        if (!current) break;
                    }
                    
                    if (current) return current;
                } catch (error) {
                    // 继续尝试下一个路径
                }
            }

            return null;
        }

        /**
         * 清理资源
         */
        cleanup() {
            this.log('Cleaning up OTA system integrator...');

            // 清理所有组件
            for (const [name, component] of this.components) {
                if (component && typeof component.cleanup === 'function') {
                    try {
                        component.cleanup();
                    } catch (error) {
                        this.error(`Error cleaning up ${name}:`, error);
                    }
                }
            }

            this.components.clear();
            this.initialized = false;

            this.log('OTA system integrator cleaned up');
        }

        /**
         * 记录日志
         */
        log(...args) {
            console.log('[OTASystemIntegrator]', ...args);
        }

        /**
         * 记录警告
         */
        warn(...args) {
            console.warn('[OTASystemIntegrator]', ...args);
        }

        /**
         * 记录错误
         */
        error(...args) {
            console.error('[OTASystemIntegrator]', ...args);
        }
    }

    // 自动执行集成
    function autoIntegrateOTASystem() {
        // 等待依赖组件就绪
        if (typeof window !== 'undefined') {
            const checkDependencies = () => {
                if (window.DependencyContainer && window.ApplicationBootstrap) {
                    const integrator = new OTASystemIntegrator();
                    integrator.integrate().catch(error => {
                        console.error('Failed to auto-integrate OTA system:', error);
                    });
                } else {
                    // 重试
                    setTimeout(checkDependencies, 100);
                }
            };

            // 立即检查或延迟检查
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', checkDependencies);
            } else {
                setTimeout(checkDependencies, 0);
            }
        }
    }

    // 导出集成器
    if (typeof module !== 'undefined' && module.exports) {
        module.exports = OTASystemIntegrator;
    } else if (typeof window !== 'undefined') {
        window.OTASystemIntegrator = OTASystemIntegrator;
        
        // 自动集成（如果启用）
        if (!window.OTA_DISABLE_AUTO_INTEGRATION) {
            autoIntegrateOTASystem();
        }
    }

})();
