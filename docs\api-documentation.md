# 智能学习型格式预处理引擎 API 文档

## 概述

智能学习型格式预处理引擎是一个基于机器学习的订单数据处理系统，能够自动学习用户的操作模式，预测和校正数据格式错误，提高数据处理的准确性和效率。

## 版本信息

- **版本**: 1.0.0
- **发布日期**: 2025-01-16
- **兼容性**: 现代浏览器 (Chrome 80+, Firefox 75+, Safari 13+)

## 核心模块

### 1. 学习配置 (LearningConfig)

管理系统的配置参数和设置。

#### 方法

##### `get(key: string): any`
获取配置值。

**参数:**
- `key`: 配置键，支持点分隔的嵌套键

**返回值:**
- 配置值，如果不存在则返回 `undefined`

**示例:**
```javascript
const config = window.OTA.learningConfig;
const enabled = config.get('learningSystem.enabled');
const storageKeys = config.get('storage.keys');
```

##### `set(key: string, value: any): void`
设置配置值。

**参数:**
- `key`: 配置键
- `value`: 配置值

**示例:**
```javascript
config.set('learningSystem.enabled', true);
config.set('storage.retentionDays', 30);
```

### 2. 存储管理器 (LearningStorageManager)

管理本地存储和数据持久化。

#### 方法

##### `setData(key: string, data: any): boolean`
存储数据到本地存储。

**参数:**
- `key`: 存储键
- `data`: 要存储的数据

**返回值:**
- `boolean`: 存储是否成功

##### `getData(key: string): any`
从本地存储获取数据。

**参数:**
- `key`: 存储键

**返回值:**
- 存储的数据，如果不存在则返回 `null`

##### `removeData(key: string): boolean`
删除存储的数据。

##### `getStorageUsage(): object`
获取存储使用情况。

**返回值:**
```javascript
{
  used: number,        // 已使用空间（字节）
  available: number,   // 可用空间（字节）
  percentage: number   // 使用百分比
}
```

### 3. 用户操作学习器 (UserOperationLearner)

记录和学习用户的操作模式。

#### 方法

##### `recordOperation(operation: object): boolean`
记录用户操作。

**参数:**
- `operation`: 操作对象
```javascript
{
  type: string,           // 操作类型 ('correction', 'validation', 'feedback')
  field: string,          // 字段名
  originalValue: string,  // 原始值
  correctedValue: string, // 校正值
  context: object,        // 上下文信息
  confidence: number      // 置信度 (0-1)
}
```

##### `queryOperations(criteria: object): array`
查询操作记录。

**参数:**
- `criteria`: 查询条件
```javascript
{
  field?: string,      // 字段名过滤
  type?: string,       // 操作类型过滤
  dateFrom?: string,   // 开始日期
  dateTo?: string,     // 结束日期
  limit?: number       // 结果数量限制
}
```

##### `getOperationStats(): object`
获取操作统计信息。

### 4. 错误分类系统 (ErrorClassificationSystem)

自动分类和识别数据错误类型。

#### 方法

##### `classifyError(errorData: object): object`
分类错误类型。

**参数:**
- `errorData`: 错误数据
```javascript
{
  field: string,
  originalValue: string,
  correctedValue: string,
  context: object
}
```

**返回值:**
```javascript
{
  category: string,      // 错误类别
  subCategory: string,   // 子类别
  confidence: number,    // 分类置信度
  suggestions: array     // 修正建议
}
```

##### `getErrorTypes(): array`
获取所有支持的错误类型。

### 5. 模式匹配引擎 (PatternMatchingEngine)

提供字符串相似度计算和模式匹配功能。

#### 方法

##### `calculateSimilarity(str1: string, str2: string, algorithm?: string): number`
计算两个字符串的相似度。

**参数:**
- `str1`: 第一个字符串
- `str2`: 第二个字符串
- `algorithm`: 算法类型 ('levenshtein', 'jaccard', 'cosine')

**返回值:**
- `number`: 相似度分数 (0-1)

##### `findSimilarPatterns(target: string, patterns: array, threshold?: number): array`
查找相似模式。

### 6. 规则生成引擎 (RuleGenerationEngine)

基于用户操作自动生成学习规则。

#### 方法

##### `generateRule(operationData: object): object`
生成学习规则。

**返回值:**
```javascript
{
  id: string,
  type: string,
  field: string,
  pattern: object,
  action: object,
  confidence: number,
  created: string
}
```

##### `getAllRules(): array`
获取所有学习规则。

##### `getRulesByField(field: string): array`
获取特定字段的规则。

##### `deleteRule(ruleId: string): boolean`
删除指定规则。

### 7. 预测性校正器 (PredictiveCorrector)

基于历史数据和学习规则进行预测性校正。

#### 方法

##### `predictCorrection(field: string, value: string, context?: object): object`
预测字段校正。

**返回值:**
```javascript
{
  field: string,
  originalValue: string,
  hasPrediction: boolean,
  topPrediction: object,
  allPredictions: array,
  autoCorrect: boolean,
  confidence: number
}
```

##### `performAutoCorrection(predictionResult: object): object`
执行自动校正。

### 8. 智能缓存管理器 (IntelligentCacheManager)

提供多级缓存和预测性预加载功能。

#### 方法

##### `get(key: string, options?: object): any`
获取缓存项。

##### `set(key: string, value: any, options?: object): boolean`
设置缓存项。

**选项:**
```javascript
{
  ttl?: number,        // 生存时间（毫秒）
  cacheType?: string,  // 缓存类型 ('memory', 'session', 'persistent')
  persistent?: boolean // 是否持久化
}
```

##### `delete(key: string): boolean`
删除缓存项。

##### `getStats(): object`
获取缓存统计信息。

### 9. 性能监控器 (PerformanceMonitor)

监控系统性能和异常检测。

#### 方法

##### `recordOperation(operation: string, startTime: number, metadata?: object): void`
记录操作性能。

##### `recordError(operation: string, error: Error, context?: object): void`
记录错误信息。

##### `getRealTimeMetrics(): object`
获取实时性能指标。

##### `getPerformanceReport(options?: object): object`
获取性能报告。

### 10. 性能优化器 (PerformanceOptimizer)

自动优化系统性能。

#### 方法

##### `performOptimization(options?: object): object`
执行性能优化。

##### `getOptimizationStats(): object`
获取优化统计信息。

## 事件系统

### 事件类型

#### `learningRuleGenerated`
当生成新的学习规则时触发。

**事件数据:**
```javascript
{
  rule: object,
  field: string,
  confidence: number
}
```

#### `predictionMade`
当进行预测校正时触发。

#### `performanceAlert`
当检测到性能问题时触发。

### 事件监听

```javascript
// 监听学习规则生成事件
document.addEventListener('learningRuleGenerated', (event) => {
  console.log('新规则生成:', event.detail.rule);
});

// 监听性能报警
document.addEventListener('performanceAlert', (event) => {
  console.log('性能报警:', event.detail);
});
```

## 配置选项

### 默认配置

```javascript
{
  learningSystem: {
    enabled: true,
    autoLearning: true,
    confidenceThreshold: 0.7
  },
  storage: {
    retentionDays: 30,
    compressionEnabled: true,
    backupEnabled: true
  },
  performance: {
    monitoringEnabled: true,
    alertThresholds: {
      responseTime: 5000,
      memoryUsage: 100 * 1024 * 1024,
      errorRate: 0.1
    }
  },
  cache: {
    maxSize: 1000,
    defaultTTL: 30 * 60 * 1000,
    preloadEnabled: true
  }
}
```

## 错误处理

### 错误类型

#### `LearningSystemError`
学习系统相关错误。

#### `StorageError`
存储操作错误。

#### `ValidationError`
数据验证错误。

### 错误处理示例

```javascript
try {
  const result = operationLearner.recordOperation(operation);
} catch (error) {
  if (error instanceof LearningSystemError) {
    console.error('学习系统错误:', error.message);
  } else {
    console.error('未知错误:', error);
  }
}
```

## 最佳实践

### 1. 初始化

```javascript
// 等待所有模块加载完成
window.addEventListener('DOMContentLoaded', () => {
  const config = window.OTA.learningConfig;
  
  // 配置系统
  config.set('learningSystem.enabled', true);
  config.set('performance.monitoringEnabled', true);
  
  console.log('学习系统已初始化');
});
```

### 2. 记录用户操作

```javascript
function recordUserCorrection(field, originalValue, correctedValue) {
  const operationLearner = window.OTA.userOperationLearner;
  
  const operation = {
    type: 'correction',
    field: field,
    originalValue: originalValue,
    correctedValue: correctedValue,
    context: {
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent
    },
    confidence: 1.0
  };
  
  return operationLearner.recordOperation(operation);
}
```

### 3. 使用预测校正

```javascript
function getPredictiveCorrection(field, value) {
  const predictor = window.OTA.predictiveCorrector;
  
  const prediction = predictor.predictCorrection(field, value, {
    priority: 'accuracy'
  });
  
  if (prediction.hasPrediction && prediction.confidence > 0.8) {
    return prediction.topPrediction.correctedValue;
  }
  
  return value;
}
```

### 4. 性能监控

```javascript
function monitorOperation(operationName, operation) {
  const monitor = window.OTA.performanceMonitor;
  const startTime = performance.now();
  
  try {
    const result = operation();
    monitor.recordOperation(operationName, startTime, { success: true });
    return result;
  } catch (error) {
    monitor.recordError(operationName, error);
    throw error;
  }
}
```

## 版本更新

### v1.0.0 (2025-01-16)
- 初始版本发布
- 完整的学习系统架构
- 15个核心模块
- 预测性校正功能
- 智能缓存系统
- 性能监控和优化

## 支持和反馈

如有问题或建议，请联系开发团队或查看项目文档。

## 相关文档

- [用户操作指南](User-Guide.md)
- [系统架构文档](Architecture.md)
- [部署指南](Deployment-Guide.md)
