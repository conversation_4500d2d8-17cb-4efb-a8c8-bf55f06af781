/**
 * @file 地址翻译服务
 * @description 为Fliggy OTA提供基于hotel-name-database的中文地址英文翻译服务
 * 集成到现有OTA系统中，避免重复开发
 * <AUTHOR> Team
 * @version 1.0.0
 */

// 防止重复加载
if (window.OTA && window.OTA.AddressTranslationService) {
    console.log('地址翻译服务已存在，跳过重复加载');
} else {

/**
 * 地址翻译服务类
 * 基于hotel-name-database提供中文地址到英文的翻译功能
 */
class AddressTranslationService {
    constructor() {
        this.logger = this.getLogger();
        this.logger?.log('地址翻译服务初始化', 'info');
    }

    /**
     * 获取日志记录器
     * @returns {Object} 日志记录器实例
     */
    getLogger() {
        return window.getLogger ? window.getLogger() : console;
    }

    /**
     * 检测字符串是否包含中文字符
     * @param {string} text - 要检测的文本
     * @returns {boolean} 是否包含中文
     */
    containsChinese(text) {
        if (!text || typeof text !== 'string') return false;
        const chineseRegex = /[\u4e00-\u9fff\u3400-\u4dbf\uf900-\ufaff]/;
        return chineseRegex.test(text);
    }

    /**
     * 基于hotel-name-database翻译中文地址
     * @param {string} chineseAddress - 中文地址
     * @returns {Object} 翻译结果
     */
    translateAddress(chineseAddress) {
        if (!chineseAddress || !this.containsChinese(chineseAddress)) {
            return {
                english: null,
                source: 'no-translation-needed',
                confidence: 1.0,
                method: 'direct'
            };
        }

        // 获取酒店名称映射数据库
        const hotelMappings = window.hotelNameMappings || {};
        
        if (Object.keys(hotelMappings).length === 0) {
            this.logger?.log('hotel-name-database未加载', 'warn');
            return {
                english: null,
                source: 'database-unavailable',
                confidence: 0,
                method: 'failed'
            };
        }

        // 尝试直接匹配
        if (hotelMappings[chineseAddress]) {
            return {
                english: hotelMappings[chineseAddress],
                source: 'hotel-database',
                confidence: 1.0,
                method: 'exact-match',
                original: chineseAddress
            };
        }

        // 尝试部分匹配（中文地址包含酒店名）
        for (const [chineseName, englishName] of Object.entries(hotelMappings)) {
            if (chineseAddress.includes(chineseName)) {
                return {
                    english: englishName,
                    source: 'hotel-database',
                    confidence: 0.8,
                    method: 'partial-match',
                    original: chineseAddress,
                    matched: chineseName
                };
            }
        }

        // 尝试反向部分匹配（酒店名包含地址）
        for (const [chineseName, englishName] of Object.entries(hotelMappings)) {
            if (chineseName.includes(chineseAddress)) {
                return {
                    english: englishName,
                    source: 'hotel-database',
                    confidence: 0.6,
                    method: 'reverse-match',
                    original: chineseAddress,
                    matched: chineseName
                };
            }
        }

        // 未找到匹配项
        this.logger?.log('未找到酒店地址翻译', 'info', {
            原地址: chineseAddress,
            建议: '需要在hotel-name-database中添加此地址'
        });

        return {
            english: null,
            source: 'not-found',
            confidence: 0,
            method: 'no-match',
            original: chineseAddress
        };
    }

    /**
     * 批量翻译地址
     * @param {Array<string>} addresses - 地址列表
     * @returns {Array<Object>} 翻译结果列表
     */
    translateBatch(addresses) {
        if (!Array.isArray(addresses)) {
            return [];
        }

        return addresses.map(address => this.translateAddress(address));
    }

    /**
     * 获取翻译统计信息
     * @returns {Object} 统计信息
     */
    getStatistics() {
        const hotelMappings = window.hotelNameMappings || {};
        return {
            databaseSize: Object.keys(hotelMappings).length,
            databaseLoaded: Object.keys(hotelMappings).length > 0,
            supportedCities: this.getSupportedCities(),
            version: '1.0.0'
        };
    }

    /**
     * 获取支持的城市列表
     * @returns {Array<string>} 城市列表
     */
    getSupportedCities() {
        const hotelMappings = window.hotelNameMappings || {};
        const cities = new Set();
        
        Object.keys(hotelMappings).forEach(chineseName => {
            // 简单的城市提取逻辑
            if (chineseName.includes('新山') || chineseName.includes('Johor')) cities.add('新山');
            if (chineseName.includes('吉隆坡') || chineseName.includes('Kuala Lumpur')) cities.add('吉隆坡');
            if (chineseName.includes('新加坡') || chineseName.includes('Singapore')) cities.add('新加坡');
            if (chineseName.includes('槟城') || chineseName.includes('Penang')) cities.add('槟城');
            if (chineseName.includes('马六甲') || chineseName.includes('Malacca')) cities.add('马六甲');
        });

        return Array.from(cities);
    }
}

// 创建服务实例并注册到OTA命名空间
const addressTranslationService = new AddressTranslationService();

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

// 注册服务
window.OTA.AddressTranslationService = AddressTranslationService;
window.OTA.addressTranslationService = addressTranslationService;

// 注册到OTA注册中心（如果存在）
if (window.OTA && window.OTA.Registry) {
    window.OTA.Registry.registerService('addressTranslationService', addressTranslationService, '@ADDRESS_TRANSLATION_SERVICE');
    window.OTA.Registry.registerFactory('getAddressTranslationService', () => addressTranslationService, '@ADDRESS_TRANSLATION_SERVICE_FACTORY');
}

console.log('地址翻译服务已注册到OTA系统');

}
