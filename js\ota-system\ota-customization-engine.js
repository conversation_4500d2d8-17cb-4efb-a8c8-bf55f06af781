/**
 * @OTA_CUSTOMIZATION_ENGINE OTA渠道定制化处理引擎
 * 🏷️ 标签: @OTA_CHANNEL_CUSTOMIZATION
 * 📝 说明: 为不同OTA渠道提供定制化的处理逻辑
 * <AUTHOR>
 * @version 1.0.0
 */

(function() {
    'use strict';

    // 确保OTA命名空间存在
    window.OTA = window.OTA || {};

    /**
     * OTA渠道定制化处理引擎
     */
    class OTACustomizationEngine {
        constructor() {
            // 当前处理上下文 - 用于严格隔离不同渠道的处理
            this._processingContext = null;
            
            // 渠道定制化配置
            this.channelConfigs = {
                'Chong Dealer': {
                    // 专属Gemini提示词
                    geminiPromptTemplate: `
**Chong Dealer渠道专属处理规则：**
- 参考号格式：CD开头，通常8-12位字符
- 客户沟通风格：简洁直接，注重效率
- 价格显示：优先显示MYR货币
- 车型偏好：倾向于经济型车辆(5 Seater)
- 特殊要求：经常包含"团号"信息`,
                    
                    // 价格计算函数
                    priceCalculator: function(basePrice, orderData) {
                        // Chong Dealer 特殊价格逻辑
                        let finalPrice = basePrice;
                        
                        // 批量订单折扣
                        if (orderData.passenger_count >= 5) {
                            finalPrice *= 0.95; // 5%折扣
                        }
                        
                        // 周末加价
                        const pickupDate = new Date(orderData.pickup_date);
                        if (pickupDate.getDay() === 0 || pickupDate.getDay() === 6) {
                            finalPrice *= 1.1; // 10%加价
                        }
                        
                        return {
                            originalPrice: basePrice,
                            finalPrice: Math.round(finalPrice * 100) / 100,
                            adjustments: [
                                { type: 'volume_discount', applied: orderData.passenger_count >= 5 },
                                { type: 'weekend_surcharge', applied: pickupDate.getDay() === 0 || pickupDate.getDay() === 6 }
                            ]
                        };
                    },
                    
                    // 车型名称映射
                    vehicleMapping: {
                        '5 Seater': '经济型轿车',
                        '7 Seater MPV': '7座商务车',
                        'Extended 5': '加长轿车',
                        'Velfire/Alphard': '豪华MPV'
                    },
                    
                    // 数据字段处理规则
                    fieldProcessing: {
                        customer_name: (value) => value ? value.trim() : null,
                        pickup_time: (value) => value,
                        ota_reference_number: (value) => value ? value.toUpperCase() : null
                    }
                },
                
                'Ctrip': {
                    geminiPromptTemplate: `
**携程渠道专属处理规则：**
- 参考号格式：多样化，通常包含数字和字母组合
- 客户沟通风格：详细规范，需要完整信息
- 价格显示：支持CNY/MYR双币种显示
- 车型偏好：注重舒适性，偏好7座以上车型
- 特殊要求：经常需要中文服务，关注行李容量`,
                    
                    priceCalculator: function(basePrice, orderData) {
                        let finalPrice = basePrice;
                        
                        // 携程渠道特殊定价
                        if (orderData.languages_id_array && orderData.languages_id_array.includes(4)) {
                            finalPrice *= 1.05; // 中文服务5%加价
                        }
                        
                        // 机场接送优惠
                        if (orderData.sub_category_id === 2 || orderData.sub_category_id === 3) {
                            finalPrice *= 0.98; // 2%优惠
                        }
                        
                        return {
                            originalPrice: basePrice,
                            finalPrice: Math.round(finalPrice * 100) / 100,
                            currency: 'MYR',
                            cnyPrice: Math.round(finalPrice * 1.65 * 100) / 100, // 假设汇率1:1.65
                            adjustments: [
                                { type: 'chinese_service', applied: orderData.languages_id_array && orderData.languages_id_array.includes(4) },
                                { type: 'airport_transfer_discount', applied: [2, 3].includes(orderData.sub_category_id) }
                            ]
                        };
                    },
                    
                    vehicleMapping: {
                        '5 Seater': '5座经济车型',
                        '7 Seater MPV': '7座舒适商务车',
                        'Extended 5': '5座豪华轿车',
                        'Velfire/Alphard': '顶级商务车'
                    },
                    
                    fieldProcessing: {
                        customer_name: (value) => value ? value.trim() : null,
                        pickup_time: (value) => value,
                        languages_id_array: (value) => value && value.length > 0 ? value : [4] // 默认中文
                    }
                },
                
                'Klook West Malaysia': {
                    geminiPromptTemplate: `
**Klook渠道专属处理规则：**
- 参考号格式：KL开头或Klook特有格式
- 客户沟通风格：国际化标准，英文为主
- 价格显示：支持多币种，MYR为主
- 车型偏好：平衡经济与舒适
- 特殊要求：注重準时性和服务质量`,
                    
                    priceCalculator: function(basePrice, orderData) {
                        let finalPrice = basePrice;
                        
                        // Klook渠道特殊定价
                        if (orderData.passenger_count >= 4) {
                            finalPrice *= 0.97; // 团体3%优惠
                        }
                        
                        // 早鸟/晚鸟时段调整
                        if (orderData.pickup_time) {
                            const hour = parseInt(orderData.pickup_time.split(':')[0]);
                            if (hour < 6 || hour > 22) {
                                finalPrice *= 1.15; // 15%夜间加价
                            }
                        }
                        
                        return {
                            originalPrice: basePrice,
                            finalPrice: Math.round(finalPrice * 100) / 100,
                            adjustments: [
                                { type: 'group_discount', applied: orderData.passenger_count >= 4 },
                                { type: 'night_surcharge', applied: this.isNightTime(orderData.pickup_time) }
                            ]
                        };
                    },
                    
                    vehicleMapping: {
                        '5 Seater': 'Standard Car',
                        '7 Seater MPV': 'Premium MPV',
                        'Extended 5': 'Luxury Sedan',
                        'Velfire/Alphard': 'Executive Van'
                    },
                    
                    fieldProcessing: {
                        customer_name: (value) => value ? value.trim() : null,
                        languages_id_array: (value) => value && value.length > 0 ? value : [2] // 默认英文
                    }
                },
                
                'Fliggy': {
                    // 🔒 FLIGGY专属配置 - 此配置仅适用于Fliggy渠道，严格隔离
                    geminiPromptTemplate: `
**Fliggy渠道专属处理规则（特供配置）：**
- 参考号格式：订单编号 + 19位数字（严格识别特征）
- 客户沟通风格：中文为主，注重服务体验
- 价格显示：支持CNY/MYR，根据地区自动转换
- 车型偏好：注重性价比和舒适性
- 特殊要求：马来西亚订单价格调整(×0.84×0.615)，新加坡订单价格调整(×0.84×0.2)
- ⚠️ 注意：此配置仅限Fliggy渠道使用，包含专属的中文地址翻译功能`,
                    
                    priceCalculator: function(basePrice, orderData) {
                        let finalPrice = basePrice;
                        let adjustments = [];
                        
                        // 🔒 Fliggy渠道特殊价格计算（特供功能）
                        // 根据订单内容识别地区并应用相应价格调整
                        const orderContent = orderData.orderContent || orderData.notes || '';
                        
                        // 马来西亚订单价格调整（Fliggy特供）
                        if (orderContent.toLowerCase().includes('malaysia') || 
                            orderContent.includes('马来西亚') || 
                            orderContent.toLowerCase().includes('kuala lumpur') ||
                            orderContent.includes('吉隆坡')) {
                            finalPrice = basePrice * 0.84 * 0.615;
                            adjustments.push({ 
                                type: 'fliggy_malaysia_pricing', 
                                applied: true, 
                                factor: 0.84 * 0.615,
                                description: 'Fliggy马来西亚订单特供价格调整'
                            });
                        }
                        // 新加坡订单价格调整（Fliggy特供）
                        else if (orderContent.toLowerCase().includes('singapore') || 
                                 orderContent.includes('新加坡')) {
                            finalPrice = basePrice * 0.84 * 0.2;
                            adjustments.push({ 
                                type: 'fliggy_singapore_pricing', 
                                applied: true, 
                                factor: 0.84 * 0.2,
                                description: 'Fliggy新加坡订单特供价格调整'
                            });
                        }
                        
                        // 团体订单优惠（Fliggy特供）
                        if (orderData.passenger_count >= 5) {
                            finalPrice *= 0.97; // 3%团体优惠
                            adjustments.push({ 
                                type: 'fliggy_group_discount', 
                                applied: true,
                                factor: 0.97,
                                description: 'Fliggy团体订单特供优惠'
                            });
                        }
                        
                        return {
                            originalPrice: basePrice,
                            finalPrice: Math.round(finalPrice * 100) / 100,
                            currency: 'MYR',
                            adjustments: adjustments,
                            channelSpecific: true, // 标记为渠道专属
                            processingNote: 'Fliggy特供价格计算'
                        };
                    },
                    
                    vehicleMapping: {
                        '5 Seater': '5座舒适轿车',
                        '7 Seater MPV': '7座商务车',
                        'Extended 5': '5座豪华轿车',
                        'Velfire/Alphard': '豪华商务车'
                    },
                    
                    // 🔒 Fliggy专属字段处理（特供功能）
                    fieldProcessing: {
                        customer_name: (value, orderData) => {
                            // 🔒 Fliggy客户名字识别特供逻辑：优先联系人，其次买家
                            if (orderData.contact_name && orderData.contact_name.trim()) {
                                return orderData.contact_name.trim();
                            }
                            if (orderData.buyer_name && orderData.buyer_name.trim()) {
                                return orderData.buyer_name.trim();
                            }
                            return value ? value.trim() : null;
                        },
                        pickup_time: (value) => value,
                        ota_reference_number: (value) => value ? value.trim() : null,
                        languages_id_array: (value) => value && value.length > 0 ? value : [4], // 默认中文
                        pickup_location: async (value, orderData) => {
                            // 🔒 Fliggy专属：处理中文地址翻译（特供功能）
                            if (value && this.containsChinese(value)) {
                                const translation = await this.translateChineseAddress(value);
                                if (translation) {
                                    orderData.pickup_location_en = translation;
                                    orderData.pickup_translation_source = 'fliggy-hotel-database';
                                }
                            }
                            return value;
                        },
                        dropoff_location: async (value, orderData) => {
                            // 🔒 Fliggy专属：处理中文地址翻译（特供功能）
                            if (value && this.containsChinese(value)) {
                                const translation = await this.translateChineseAddress(value);
                                if (translation) {
                                    orderData.dropoff_location_en = translation;
                                    orderData.dropoff_translation_source = 'fliggy-hotel-database';
                                }
                            }
                            return value;
                        }
                    },
                    
                    // 标记为Fliggy专属配置
                    isChannelSpecific: true,
                    channelNote: '此配置专为Fliggy渠道设计，包含特供的客户名字识别和中文地址翻译功能'
                },
                
                // 通用/默认配置
                'default': {
                    geminiPromptTemplate: `
**通用OTA渠道处理规则：**
- 使用标准处理流程
- 支持多语言服务
- 标准定价策略
- 常规车型选择`,
                    
                    priceCalculator: function(basePrice, orderData) {
                        return {
                            originalPrice: basePrice,
                            finalPrice: basePrice,
                            adjustments: []
                        };
                    },
                    
                    vehicleMapping: {
                        '5 Seater': '5座轿车',
                        '7 Seater MPV': '7座商务车',
                        'Extended 5': '豪华轿车',
                        'Velfire/Alphard': '高端商务车'
                    },
                    
                    fieldProcessing: {}
                }
            };

            this.logger = this.getLogger();
        }

        /**
         * 设置当前处理上下文 (内部方法)
         * @param {string} channel - 渠道名称
         * @param {object} orderData - 订单数据
         */
        _setProcessingContext(channel, orderData) {
            this._processingContext = {
                channel: channel,
                timestamp: Date.now(),
                orderData: orderData
            };
        }

        /**
         * 获取当前处理上下文 (内部方法)
         * @returns {object|null} 当前处理上下文
         */
        _getCurrentProcessingContext() {
            return this._processingContext;
        }

        /**
         * 清除处理上下文 (内部方法)
         */
        _clearProcessingContext() {
            this._processingContext = null;
        }

        /**
         * 验证渠道访问权限 (内部方法)
         * @param {string} requiredChannel - 需要的渠道
         * @param {string} functionality - 功能名称
         * @returns {boolean} 是否有访问权限
         */
        _validateChannelAccess(requiredChannel, functionality) {
            const currentContext = this._getCurrentProcessingContext();
            if (!currentContext || currentContext.channel !== requiredChannel) {
                this.logger?.log(`${functionality}功能仅限${requiredChannel}渠道使用`, 'warn', {
                    当前渠道: currentContext?.channel || '未知',
                    需要渠道: requiredChannel,
                    功能: functionality
                });
                return false;
            }
            return true;
        }

        /**
         * 检测字符串是否包含中文字符 (仅限Fliggy渠道使用)
         * @param {string} text - 要检测的文本
         * @returns {boolean} 是否包含中文
         */
        containsChinese(text) {
            if (!text || typeof text !== 'string') return false;
            const chineseRegex = /[\u4e00-\u9fff\u3400-\u4dbf\uf900-\ufaff]/;
            return chineseRegex.test(text);
        }

        /**
         * 基于hotel-name-database翻译中文地址 (仅限Fliggy渠道使用)
         * @param {string} chineseAddress - 中文地址
         * @returns {Promise<string|null>} 英文翻译结果
         */
        async translateChineseAddress(chineseAddress) {
            // 严格限制：仅Fliggy渠道可使用此功能
            if (!this._validateChannelAccess('Fliggy', '中文地址翻译')) {
                return null;
            }

            if (!chineseAddress || !this.containsChinese(chineseAddress)) {
                return null;
            }

            // 使用专门的地址翻译服务
            const translationService = window.OTA?.addressTranslationService;
            if (translationService) {
                const result = translationService.translateAddress(chineseAddress);
                if (result.english) {
                    this.logger?.log('Fliggy地址翻译成功', 'info', {
                        原地址: chineseAddress,
                        英文地址: result.english,
                        来源: result.source,
                        匹配方式: result.method,
                        置信度: result.confidence
                    });
                    return result.english;
                }
            }

            // 回退到原有的简单方法
            const hotelMappings = window.hotelNameMappings || {};
            
            // 尝试直接匹配
            if (hotelMappings[chineseAddress]) {
                return hotelMappings[chineseAddress];
            }

            // 尝试部分匹配
            for (const [chineseName, englishName] of Object.entries(hotelMappings)) {
                if (chineseAddress.includes(chineseName) || chineseName.includes(chineseAddress)) {
                    return englishName;
                }
            }

            // 未找到匹配项
            this.logger?.log('未找到酒店地址翻译', 'info', {
                原地址: chineseAddress,
                建议: '需要在hotel-name-database中添加此地址'
            });

            return null;
        }

        /**
         * 获取渠道的定制化配置
         * @param {string} channelName - 渠道名称
         * @returns {object} 渠道配置
         */
        getChannelConfig(channelName) {
            return this.channelConfigs[channelName] || this.channelConfigs['default'];
        }

        /**
         * 生成渠道专属的Gemini提示词
         * @param {string} channelName - 渠道名称
         * @param {object} basePrompt - 基础提示词
         * @returns {string} 定制化的提示词
         */
        generateCustomPrompt(channelName, basePrompt = '') {
            const config = this.getChannelConfig(channelName);
            
            // 组合基础提示词和渠道专属内容
            const customPrompt = `${basePrompt}

${config.geminiPromptTemplate}

**渠道特殊处理要求：**
- 针对 ${channelName} 渠道进行优化处理
- 遵循该渠道的业务规范和客户期望
- 使用该渠道偏好的数据格式和术语`;

            this.logger.log('生成渠道专属提示词', 'info', {
                渠道: channelName,
                提示词长度: customPrompt.length
            });

            return customPrompt;
        }

        /**
         * 执行渠道专属的价格计算
         * @param {string} channelName - 渠道名称
         * @param {number} basePrice - 基础价格
         * @param {object} orderData - 订单数据
         * @returns {object} 价格计算结果
         */
        calculateChannelPrice(channelName, basePrice, orderData) {
            const config = this.getChannelConfig(channelName);
            
            try {
                const result = config.priceCalculator(basePrice, orderData);
                
                this.logger.log('渠道价格计算完成', 'info', {
                    渠道: channelName,
                    原始价格: basePrice,
                    最终价格: result.finalPrice,
                    调整项: result.adjustments
                });

                return {
                    success: true,
                    ...result,
                    channel: channelName
                };
                
            } catch (error) {
                this.logger.log('渠道价格计算失败', 'error', {
                    渠道: channelName,
                    错误: error.message
                });
                
                return {
                    success: false,
                    originalPrice: basePrice,
                    finalPrice: basePrice,
                    error: error.message,
                    channel: channelName
                };
            }
        }

        /**
         * 应用渠道专属的车型名称映射
         * @param {string} channelName - 渠道名称
         * @param {string} vehicleType - 原始车型名称
         * @returns {string} 映射后的车型名称
         */
        mapVehicleName(channelName, vehicleType) {
            const config = this.getChannelConfig(channelName);
            const mapping = config.vehicleMapping || {};
            
            return mapping[vehicleType] || vehicleType;
        }

        /**
         * 处理渠道专属的字段数据
         * @param {string} channelName - 渠道名称
         * @param {object} orderData - 订单数据
         * @returns {Promise<object>} 处理后的订单数据
         */
        async processChannelFields(channelName, orderData) {
            // 设置处理上下文，确保渠道隔离
            this._setProcessingContext(channelName, orderData);
            
            try {
                const config = this.getChannelConfig(channelName);
                const processing = config.fieldProcessing || {};
                
                const processedData = { ...orderData };
                
                // 应用字段处理规则
                for (const [fieldName, processor] of Object.entries(processing)) {
                    if (typeof processor === 'function' && processedData.hasOwnProperty(fieldName)) {
                        try {
                            const result = processor(processedData[fieldName], processedData);
                            // 处理异步结果
                            if (result && typeof result.then === 'function') {
                                processedData[fieldName] = await result;
                            } else {
                                processedData[fieldName] = result;
                            }
                        } catch (error) {
                            this.logger.log('字段处理失败', 'error', {
                                渠道: channelName,
                                字段: fieldName,
                                错误: error.message
                            });
                        }
                    }
                }

                return processedData;
                
            } finally {
                // 确保处理完成后清除上下文
                this._clearProcessingContext();
            }
        }

        /**
         * 获取渠道的推荐车型
         * @param {string} channelName - 渠道名称
         * @param {number} passengerCount - 乘客数量
         * @param {number} luggageCount - 行李数量
         * @returns {object} 推荐车型信息
         */
        getRecommendedVehicle(channelName, passengerCount, luggageCount = 0) {
            // 基础车型推荐逻辑
            let recommendedType = '5 Seater';
            let carTypeId = 5;

            if (passengerCount > 3 || luggageCount > 3) {
                recommendedType = '7 Seater MPV';
                carTypeId = 15;
            }
            if (passengerCount > 6) {
                recommendedType = 'Velfire/Alphard';
                carTypeId = 32;
            }

            // 渠道专属调整
            const config = this.getChannelConfig(channelName);
            
            // 对于Ctrip，倾向于推荐更大的车型
            if (channelName === 'Ctrip' && passengerCount >= 3) {
                recommendedType = '7 Seater MPV';
                carTypeId = 15;
            }
            
            // 对于Chong Dealer，倾向于经济型
            if (channelName === 'Chong Dealer' && passengerCount <= 4) {
                recommendedType = '5 Seater';
                carTypeId = 5;
            }

            return {
                originalType: recommendedType,
                mappedType: this.mapVehicleName(channelName, recommendedType),
                carTypeId: carTypeId,
                reasoning: `基于${passengerCount}人${luggageCount}件行李，为${channelName}渠道推荐`
            };
        }

        /**
         * 综合处理订单数据
         * @param {string} channelName - 渠道名称
         * @param {object} orderData - 原始订单数据
         * @param {number} basePrice - 基础价格（可选）
         * @returns {Promise<object>} 完整处理结果
         */
        async processOrder(channelName, orderData, basePrice = null) {
            try {
                // 1. 处理字段数据（异步）
                const processedData = await this.processChannelFields(channelName, orderData);
                
                // 2. 车型推荐
                const vehicleRecommendation = this.getRecommendedVehicle(
                    channelName, 
                    processedData.passenger_count || 1,
                    processedData.luggage_count || 0
                );
                
                // 3. 价格计算（如果提供基础价格）
                let priceResult = null;
                if (basePrice && typeof basePrice === 'number') {
                    priceResult = this.calculateChannelPrice(channelName, basePrice, processedData);
                }

                const result = {
                    success: true,
                    channel: channelName,
                    processedData: processedData,
                    vehicleRecommendation: vehicleRecommendation,
                    priceResult: priceResult,
                    metadata: {
                        processedAt: new Date().toISOString(),
                        processingVersion: '1.0.0'
                    }
                };

                this.logger.log('订单综合处理完成', 'info', {
                    渠道: channelName,
                    客户: processedData.customer_name,
                    推荐车型: vehicleRecommendation.mappedType,
                    最终价格: priceResult ? priceResult.finalPrice : '未计算'
                });

                return result;

            } catch (error) {
                this.logger.log('订单处理失败', 'error', {
                    渠道: channelName,
                    错误: error.message
                });

                return {
                    success: false,
                    error: error.message,
                    channel: channelName,
                    originalData: orderData
                };
            }
        }

        /**
         * 辅助方法：判断是否夜间时段
         * @param {string} timeString - 时间字符串 HH:MM
         * @returns {boolean} 是否夜间
         */
        isNightTime(timeString) {
            if (!timeString) return false;
            const hour = parseInt(timeString.split(':')[0]);
            return hour < 6 || hour > 22;
        }

        /**
         * 获取Logger实例
         * @returns {object} Logger实例
         */
        getLogger() {
            if (typeof getLogger === 'function') {
                return getLogger();
            }
            return {
                log: (message, level, data) => {
                    console.log(`[${level.toUpperCase()}] ${message}`, data || '');
                }
            };
        }
    }

    // 创建全局实例
    const otaCustomizationEngine = new OTACustomizationEngine();

    // 暴露到OTA命名空间
    window.OTA.CustomizationEngine = OTACustomizationEngine;
    window.OTA.customizationEngine = otaCustomizationEngine;

    // 注册到OTA注册中心
    if (window.OTA && window.OTA.Registry) {
        window.OTA.Registry.registerService('customizationEngine', otaCustomizationEngine, '@OTA_CUSTOMIZATION_ENGINE');
        window.OTA.Registry.registerFactory('getCustomizationEngine', () => otaCustomizationEngine, '@OTA_CUSTOMIZATION_ENGINE_FACTORY');
    }

})();