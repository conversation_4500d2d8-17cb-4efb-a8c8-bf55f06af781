/**
 * @OTA_MANAGER 多订单处理管理器 - 模块化重构版 v5.0
 * 🏷️ 标签: @OTA_MULTI_ORDER_MANAGER_V2
 * 📝 说明: 完全模块化的多订单管理器，委托所有功能到专门的模块
 * ⚠️ 警告: 这是重构后的精简版本，保持向后兼容
 * <AUTHOR>
 * @version 5.0.0-modularized
 */

// 防止重复加载
if (window.OTA && window.OTA.MultiOrderManagerV2) {
    console.log('模块化多订单管理器v2已存在，跳过重复加载');
} else {

/**
 * 多订单管理器 - 精简协调器版本
 * 主要功能委托给专门的模块处理
 */
class MultiOrderManagerV2 {
    constructor(dependencies = {}) {
        // 基础配置
        this.config = {
            minInputLength: 50,
            debounceDelay: 1200,
            maxOrdersPerBatch: 5,
            batchDelay: 800,
            confidenceThreshold: 0.7,
            ...dependencies.config
        };

        // 核心模块引用
        this.coordinator = null;
        this.stateManager = null;
        
        // 兼容性属性（保持原有API）
        this.state = {
            isMultiOrderMode: false,
            currentSegments: [],
            selectedOrders: new Set(),
            processedOrders: new Map(),
            parsedOrders: [],
            multiOrderResult: null
        };

        // 事件处理
        this.debounceTimer = null;
        this.boundEventHandlers = new Map();

        this.logger = this.getLogger();
        console.log('🔧 MultiOrderManagerV2 开始初始化...');
        this.initialize();
    }

    /**
     * 初始化管理器
     */
    async initialize() {
        try {
            // 等待模块加载完成
            await this.waitForModules();
            
            // 获取核心模块
            this.coordinator = window.OTA.multiOrderCoordinator;
            this.stateManager = window.OTA.multiOrderStateManager;

            // 设置状态同步
            this.setupStateSync();
            
            // 初始化UI事件
            this.initPanelEvents();
            this.setupInputListener();

            // 初始化批量设置功能
            this.initBatchSettings();

            // 初始化批量提交功能
            this.initBatchSubmission();

            // 初始化航班信息查询功能
            this.initFlightInfoFeature();

            this.logger.log('模块化多订单管理器初始化完成', 'info');

        } catch (error) {
            this.logger.log('管理器初始化失败，使用降级模式', 'error', { error: error.message });
            this.initializeFallbackMode();
        }
    }

    /**
     * 初始化降级模式
     */
    initializeFallbackMode() {
        try {
            this.logger.log('正在初始化降级模式...', 'warn');
            
            // 设置基本状态
            this.state = {
                isMultiOrderMode: false,
                currentSegments: [],
                selectedOrders: new Set(),
                processedOrders: new Map(),
                parsedOrders: [],
                multiOrderResult: null,
                batchSettings: {
                    languageId: '',
                    otaChannel: ''
                }
            };

            // 尝试设置基本的事件监听
            this.initPanelEvents();
            
            // 隐藏多订单面板
            this.hideMultiOrderPanel();
            
            this.logger.log('降级模式初始化完成', 'warn');
        } catch (fallbackError) {
            this.logger.log('降级模式初始化也失败了', 'error', { error: fallbackError.message });
            // 完全降级，只保留最基本的功能
            this.state = { 
                isMultiOrderMode: false,
                currentSegments: [],
                selectedOrders: new Set(),
                processedOrders: new Map(),
                parsedOrders: [],
                multiOrderResult: null
            };
        }
    }

    /**
     * 等待必需模块加载
     */
    async waitForModules() {
        const maxWaitTime = 10000; // 10秒超时
        const startTime = Date.now();
        const requiredModules = [
            'multiOrderCoordinator',
            'multiOrderStateManager',
            'batchProcessor',
            'multiOrderDetector',
            'multiOrderProcessor',
            'multiOrderRenderer'
        ];

        while (Date.now() - startTime < maxWaitTime) {
            const allLoaded = requiredModules.every(moduleName => {
                const moduleExists = window.OTA?.[moduleName] !== undefined;
                if (!moduleExists) {
                    this.logger.log(`等待模块加载: ${moduleName}`, 'debug');
                }
                return moduleExists;
            });

            if (allLoaded) {
                this.logger.log('所有必需模块已加载完成', 'info');
                return;
            }
            await this.sleep(100);
        }

        // 列出未加载的模块
        const missingModules = requiredModules.filter(name => !window.OTA?.[name]);
        this.logger.log(`模块加载超时，缺少: ${missingModules.join(', ')}`, 'error');
        throw new Error(`必需模块加载超时: ${missingModules.join(', ')}`);
    }

    /**
     * 设置状态同步
     */
    setupStateSync() {
        // 监听状态管理器的变化
        this.stateManager.addListener('stateUpdated', (data) => {
            this.syncState(data.newState);
        });

        // 监听批量设置变更事件
        this.stateManager.addListener('batchSettingsChanged', (data) => {
            this.syncBatchSettingsToUI(data.batchSettings);
            this.logger.log('批量设置已同步', 'info', data.changes);
        });

        // 监听协调器事件
        this.coordinator.addEventListener('multiOrderModeEnabled', () => {
            this.state.isMultiOrderMode = true;
        });

        this.coordinator.addEventListener('multiOrderModeDisabled', () => {
            this.state.isMultiOrderMode = false;
            this.state.parsedOrders = [];
            this.state.selectedOrders.clear();
        });

        // 监听全局多订单检测事件（统一事件处理）
        console.log('🔗 注册 multiOrderDetected 事件监听器...');
        document.addEventListener('multiOrderDetected', (event) => {
            console.log('🎯 MultiOrderManagerV2 收到 multiOrderDetected 事件!', event);
            this.handleMultiOrderDetectedEvent(event);
        });
        console.log('✅ multiOrderDetected 事件监听器已注册');
    }

    /**
     * 同步状态
     * @param {object} newState - 新状态
     */
    syncState(newState) {
        // 同步兼容性状态
        this.state.isMultiOrderMode = newState.isMultiOrderMode || false;
        this.state.parsedOrders = newState.parsedOrders || [];
        this.state.selectedOrders = newState.selectedOrders || new Set();
        this.state.multiOrderResult = newState.detectionResult || null;

        // 同步批量设置到UI
        if (newState.batchSettings) {
            this.syncBatchSettingsToUI(newState.batchSettings);
        }

        // 如果订单数据有变化，刷新显示
        if (newState.parsedOrders && this.state.isMultiOrderMode) {
            this.refreshOrderDisplay();
        }
    }

    /**
     * 同步批量设置到UI
     * @param {object} batchSettings - 批量设置
     */
    syncBatchSettingsToUI(batchSettings) {
        try {
            // 同步语言选择
            if (batchSettings.languageId) {
                const languageSelect = document.getElementById('batchLanguageSelect');
                if (languageSelect) {
                    languageSelect.value = batchSettings.languageId;
                }
            }

            // 同步OTA选择
            if (batchSettings.otaChannel) {
                const otaSelect = document.getElementById('batchOtaSelect');
                if (otaSelect) {
                    otaSelect.value = batchSettings.otaChannel;
                }
            }

            this.logger.log('批量设置已同步到UI', 'info');
        } catch (error) {
            this.logger.log('同步批量设置到UI失败', 'error', { error: error.message });
        }
    }

    /**
     * 主要分析方法 - 已简化，避免重复分析
     * 现在只处理已经通过Gemini检测到的多订单结果
     * @param {string} text - 输入文本
     * @param {object} options - 选项
     * @returns {Promise<object>} 分析结果
     */
    async analyzeInputForMultiOrder(text, options = {}) {
        // 🔧 优化：不再主动分析，避免重复的检测逻辑
        // 现在统一通过realtime-analysis-manager的Gemini分析结果触发
        console.log('📋 多订单管理器：跳过主动分析，等待Gemini分析结果');

        // 如果options中已经包含检测结果，处理它
        if (options.detectionResult && options.detectionResult.isMultiOrder) {
            // 更新兼容性状态
            this.state.isMultiOrderMode = true;
            this.state.parsedOrders = options.detectionResult.orders || [];
            this.state.multiOrderResult = options.detectionResult;

            return {
                success: true,
                isMultiOrder: true,
                orderCount: options.detectionResult.orders?.length || 0,
                orders: options.detectionResult.orders || [],
                detectionResult: options.detectionResult
            };
        }

        // 否则返回单订单结果
        return {
            success: true,
            isMultiOrder: false,
            orderCount: 1,
            orders: [],
            detectionResult: null
        };
    }

    /**
     * 显示多订单面板
     * @param {array} orders - 订单列表
     */
    showMultiOrderPanel(orders, options = {}) {
        // 确保orders是数组格式
        if (!Array.isArray(orders)) {
            if (orders && typeof orders === 'object' && orders.orders) {
                orders = orders.orders;
            } else {
                orders = [];
            }
        }

        // 更新状态
        this.state.parsedOrders = orders;
        this.state.isMultiOrderMode = orders.length > 1;

        // 直接调用刷新显示
        this.refreshOrderDisplay();
    }

    /**
     * 隐藏多订单面板
     */
    hideMultiOrderPanel() {
        const multiOrderPanel = document.getElementById('multiOrderPanel');
        if (multiOrderPanel) {
            multiOrderPanel.classList.add('hidden');
            multiOrderPanel.style.display = 'none';
        }
        this.state.isMultiOrderMode = false;
    }

    /**
     * 切换订单选择
     * @param {number} index - 订单索引
     */
    toggleOrderSelection(index) {
        if (this.coordinator) {
            this.coordinator.toggleOrderSelection(index);
        } else {
            // 直接操作本地状态
            if (this.state.selectedOrders.has(index)) {
                this.state.selectedOrders.delete(index);
            } else {
                this.state.selectedOrders.add(index);
            }
            this.updateSelectedCount();
        }
    }

    /**
     * 创建选中的订单 - 委托给批量处理器
     * @returns {Promise<object>} 处理结果
     */
    async createSelectedOrders() {
        if (this.coordinator) {
            return await this.coordinator.processBatchOrders();
        } else {
            return this.fallbackCreateOrders();
        }
    }

    /**
     * 创建单个订单
     * @param {number} index - 订单索引
     * @returns {Promise<object>} 创建结果
     */
    async createSingleOrder(index) {
        if (!this.state.parsedOrders || index >= this.state.parsedOrders.length) {
            throw new Error('订单索引无效');
        }

        const order = this.state.parsedOrders[index];
        
        // 使用API服务创建订单
        const apiService = this.getApiService();
        if (!apiService) {
            throw new Error('API服务不可用');
        }

        try {
            const response = await apiService.createOrder(order);
            this.logger.log(`订单 ${index + 1} 创建成功`, 'info');
            return response;
        } catch (error) {
            this.logger.log(`订单 ${index + 1} 创建失败`, 'error', { error: error.message });
            throw error;
        }
    }

    /**
     * 获取选中的订单
     * @returns {array} 选中的订单列表
     */
    getSelectedOrders() {
        const selectedIndexes = Array.from(this.state.selectedOrders);
        return selectedIndexes.map(index => this.state.parsedOrders[index]).filter(Boolean);
    }

    /**
     * 切换多订单模式
     */
    toggleMultiOrderMode() {
        if (this.coordinator) {
            if (this.state.isMultiOrderMode) {
                this.coordinator.disableMultiOrderMode();
            } else {
                this.coordinator.enableMultiOrderMode();
            }
        } else {
            this.state.isMultiOrderMode = !this.state.isMultiOrderMode;
            if (!this.state.isMultiOrderMode) {
                this.hideMultiOrderPanel();
            }
        }
    }

    /**
     * 初始化面板事件
     */
    initPanelEvents() {
        // 多订单模式切换按钮
        const toggleBtn = document.getElementById('multiOrderToggle');
        if (toggleBtn) {
            toggleBtn.addEventListener('click', () => {
                this.toggleMultiOrderMode();
            });
        }

        // 批量创建按钮
        const batchBtn = document.getElementById('createSelectedBtn');
        if (batchBtn) {
            batchBtn.addEventListener('click', () => {
                this.createSelectedOrders();
            });
        }

        // 关闭多订单面板按钮
        const closeBtn = document.getElementById('closeMultiOrderBtn');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                console.log('🔴 关闭多订单面板');
                this.hideMultiOrderPanel();
            });
        }

        // 返回主页按钮
        const backBtn = document.getElementById('backToMainBtn');
        if (backBtn) {
            backBtn.addEventListener('click', () => {
                console.log('🏠 返回主页');
                this.hideMultiOrderPanel();
                // 如果需要额外的主页逻辑，可以在这里添加
                window.location.reload(); // 刷新页面回到主页状态
            });
        }
    }

    // 🧹 已清理：移除了已禁用的输入监听器设置方法
    // 现在统一通过realtime-analysis-manager的Gemini分析结果触发

    /**
     * 更新选中计数
     */
    updateSelectedCount() {
        const countElement = document.getElementById('selectedCount');
        if (countElement) {
            countElement.textContent = this.state.selectedOrders.size;
        }
    }

    // 🧹 已清理：移除了复杂的降级模式方法
    // 包括降级初始化、降级分析、降级面板显示等
    // 这些方法增加了复杂性但很少使用，现在由核心功能统一处理

    /**
     * 初始化批量设置功能
     */
    initBatchSettings() {
        try {
            // 移除语言选择功能，只保留OTA选择
            
            // 首先填充OTA选项
            this.populateOtaOptions();
            
            // 绑定批量OTA选择事件 - 实时应用
            const batchOtaSelect = document.getElementById('batchOtaSelect');
            if (batchOtaSelect) {
                batchOtaSelect.addEventListener('change', (e) => {
                    const otaChannel = e.target.value;
                    console.log('🔄 OTA选择变更，实时应用:', otaChannel);
                    this.handleBatchOtaChange(otaChannel);
                    // 立即应用到所有选中的订单
                    this.applyBatchOtaToSelected(otaChannel);
                });
            }

            // 移除应用批量设置按钮的事件监听，因为现在是实时应用

            this.logger.log('批量设置功能初始化完成（仅OTA实时应用）', 'info');
        } catch (error) {
            this.logger.log('批量设置功能初始化失败', 'error', { error: error.message });
        }
    }

    /**
     * 填充OTA下拉选择框选项 - 与主界面保持一致
     */
    populateOtaOptions() {
        try {
            const batchOtaSelect = document.getElementById('batchOtaSelect');
            if (!batchOtaSelect) {
                console.log('❌ batchOtaSelect元素不存在');
                return;
            }

            // 获取OTA渠道映射
            const otaChannelMapping = window.OTA?.otaChannelMapping;
            if (!otaChannelMapping) {
                console.log('❌ OTA渠道映射不可用，使用默认选项');
                this.populateDefaultOtaOptions(batchOtaSelect);
                return;
            }

            // 获取当前用户信息 - 与主界面逻辑一致（修复大小写问题）
            const appState = window.OTA?.appState || window.OTA?.AppState;
            const user = appState ? appState.getCurrentUser() : null;
            let otaConfig = null;

            console.log('🔍 多订单OTA选项填充 - 用户信息检查:', {
                appStateExists: !!appState,
                user: user,
                userId: user?.id,
                userEmail: user?.email
            });

            // 优先使用用户ID匹配，然后使用邮箱匹配 - 完全复制主界面逻辑
            if (user) {
                if (user.id) {
                    otaConfig = otaChannelMapping.getConfig(user.id);
                    console.log('🔍 通过用户ID匹配OTA配置:', { 
                        userId: user.id, 
                        hasConfig: !!otaConfig,
                        config: otaConfig 
                    });
                }
                if (!otaConfig && user.email) {
                    otaConfig = otaChannelMapping.getConfig(user.email);
                    console.log('🔍 通过邮箱匹配OTA配置:', { 
                        email: user.email, 
                        hasConfig: !!otaConfig,
                        config: otaConfig 
                    });
                }
            } else {
                console.log('⚠️ 未找到用户信息，将使用通用OTA配置');
            }

            // 确定要使用的渠道列表
            let channelsToUse;
            let configType;
            
            if (otaConfig && otaConfig.options) {
                // 使用用户专属配置
                channelsToUse = otaConfig.options;
                configType = '用户专属';
                console.log('✅ 使用用户专属OTA配置:', {
                    userId: user?.id,
                    email: user?.email,
                    defaultChannel: otaConfig.default,
                    optionCount: channelsToUse.length
                });
            } else {
                // 使用通用渠道列表作为后备
                channelsToUse = otaChannelMapping.commonChannels || [];
                configType = '通用';
                console.log('ℹ️ 未找到用户专属配置，使用通用OTA配置:', {
                    userId: user?.id || null,
                    email: user?.email || null,
                    optionCount: channelsToUse.length
                });
            }

            // 清空所有现有选项
            batchOtaSelect.innerHTML = '';

            // 根据是否有用户专属配置决定是否添加占位符
            const hasUserConfig = otaConfig && otaConfig.default;
            if (!hasUserConfig) {
                // 没有专属配置时，添加占位符选项
                const placeholderOption = document.createElement('option');
                placeholderOption.value = '';
                placeholderOption.textContent = '选择OTA渠道';
                placeholderOption.disabled = true;
                placeholderOption.selected = true;
                batchOtaSelect.appendChild(placeholderOption);
            }

            // 添加渠道选项
            channelsToUse.forEach(channel => {
                const option = document.createElement('option');
                option.value = channel.value;
                option.textContent = channel.text;
                batchOtaSelect.appendChild(option);
            });

            // 如果有用户专属默认值，设置为选中状态
            if (hasUserConfig && otaConfig.default) {
                batchOtaSelect.value = otaConfig.default;
                console.log(`🎯 设置默认OTA渠道: ${otaConfig.default}`);
            }

            console.log(`✅ 已填充 ${channelsToUse.length} 个${configType}OTA渠道选项`);
            console.log(`📋 填充的渠道列表:`, channelsToUse.map(c => c.text));
            this.logger.log(`${configType}OTA选项已填充: ${channelsToUse.length}个渠道`, 'success');

        } catch (error) {
            console.error('❌ 填充OTA选项失败:', error);
            this.logger.log('填充OTA选项失败', 'error', { error: error.message });
            // 降级方案
            this.populateDefaultOtaOptions(document.getElementById('batchOtaSelect'));
        }
    }

    /**
     * 填充默认OTA选项（降级方案）
     * @param {HTMLSelectElement} selectElement - 选择框元素
     */
    populateDefaultOtaOptions(selectElement) {
        if (!selectElement) return;

        const defaultOptions = [
            { value: 'Klook West Malaysia', text: 'Klook West Malaysia' },
            { value: 'Ctrip West Malaysia', text: 'Ctrip West Malaysia' },
            { value: 'GMH Sabah', text: 'GMH Sabah' },
            { value: 'SMW Eric', text: 'SMW Eric' },
            { value: 'Traveloka', text: 'Traveloka' },
            { value: 'Fliggy', text: 'Fliggy' }
        ];

        // 清空现有选项（保留第一个默认选项）
        while (selectElement.children.length > 1) {
            selectElement.removeChild(selectElement.lastChild);
        }

        // 添加默认选项
        defaultOptions.forEach(channel => {
            const option = document.createElement('option');
            option.value = channel.value;
            option.textContent = channel.text;
            selectElement.appendChild(option);
        });

        console.log('✅ 已填充默认OTA选项');
    }

    /**
     * 处理批量OTA变更
     * @param {string} otaChannel - OTA渠道
     */
    handleBatchOtaChange(otaChannel) {
        if (!otaChannel) return;

        // 使用状态管理器的专门方法更新批量设置
        if (this.stateManager && this.stateManager.updateBatchSettings) {
            this.stateManager.updateBatchSettings({
                otaChannel: otaChannel
            });
        }

        this.logger.log(`批量OTA设置已更新: ${otaChannel}`, 'info');
    }

    /**
     * 实时应用OTA设置到所有选中的订单
     * @param {string} otaChannel - OTA渠道
     */
    applyBatchOtaToSelected(otaChannel) {
        try {
            if (!otaChannel) {
                console.log('🔄 OTA渠道为空，跳过应用');
                return;
            }

            const selectedOrders = Array.from(this.state.selectedOrders);
            console.log('🔄 应用OTA到选中订单:', { otaChannel, selectedCount: selectedOrders.length });

            if (selectedOrders.length === 0) {
                // 如果没有选中的订单，应用到所有订单
                this.state.parsedOrders.forEach((order, index) => {
                    if (order) {
                        // 设置API字段名 - 与主界面保持一致
                        order.ota = otaChannel;
                        // 保留内部标识字段用于追踪
                        order._otaChannel = otaChannel;
                        console.log(`✅ 订单 ${index + 1} OTA更新为: ${otaChannel}`);
                    }
                });
                console.log('✅ 已应用OTA到所有订单');
            } else {
                // 应用到选中的订单
                selectedOrders.forEach(index => {
                    if (this.state.parsedOrders[index]) {
                        // 设置API字段名 - 与主界面保持一致
                        this.state.parsedOrders[index].ota = otaChannel;
                        // 保留内部标识字段用于追踪
                        this.state.parsedOrders[index]._otaChannel = otaChannel;
                        console.log(`✅ 选中订单 ${index + 1} OTA更新为: ${otaChannel}`);
                    }
                });
                console.log(`✅ 已应用OTA到 ${selectedOrders.length} 个选中订单`);
            }

            // 通知状态管理器更新订单数据
            if (this.stateManager) {
                this.stateManager.updateOrders(this.state.parsedOrders);
            }

            // 直接更新DOM显示，避免完整重新渲染
            this.updateOtaChannelDisplayInDOM(otaChannel, selectedOrders.length === 0);

            this.logger.log(`OTA设置已实时应用: ${otaChannel}`, 'success');

        } catch (error) {
            console.error('❌ 实时应用OTA设置失败:', error);
            this.logger.log('实时应用OTA设置失败', 'error', { error: error.message });
        }
    }

    /**
     * 应用批量设置到所有选中的订单
     * @deprecated 已改为实时应用，此方法保留用于兼容性
     */
    applyBatchSettings() {
        console.log('⚠️ applyBatchSettings已弃用，现在使用实时应用');
        // 保留方法以避免错误，但不执行任何操作
    }

    /**
     * 直接更新DOM中OTA渠道的显示值，避免完整重新渲染
     * @param {string} otaChannel - OTA渠道名称
     * @param {boolean} applyToAll - 是否应用到所有订单
     */
    updateOtaChannelDisplayInDOM(otaChannel, applyToAll) {
        try {
            console.log('🔄 直接更新DOM中的OTA渠道显示:', { otaChannel, applyToAll });
            
            if (applyToAll) {
                // 更新所有订单卡片
                const otaElements = document.querySelectorAll('[data-field="otaChannel"] .grid-value');
                otaElements.forEach(element => {
                    element.textContent = otaChannel;
                    console.log(`✅ 已更新OTA显示: ${otaChannel}`);
                });
                console.log(`✅ 已更新所有 ${otaElements.length} 个OTA显示`);
            } else {
                // 只更新选中的订单
                const selectedOrders = Array.from(this.state.selectedOrders);
                selectedOrders.forEach(index => {
                    const orderCard = document.querySelector(`.order-card[data-index="${index}"]`);
                    if (orderCard) {
                        const otaElement = orderCard.querySelector('[data-field="otaChannel"] .grid-value');
                        if (otaElement) {
                            otaElement.textContent = otaChannel;
                            console.log(`✅ 已更新订单 ${index + 1} 的OTA显示: ${otaChannel}`);
                        }
                    }
                });
                console.log(`✅ 已更新 ${selectedOrders.length} 个选中订单的OTA显示`);
            }

        } catch (error) {
            console.error('❌ 直接更新DOM中OTA显示失败:', error);
            // 如果直接更新失败，回退到完整刷新
            this.refreshOrderDisplay();
        }
    }

    /**
     * 刷新订单显示
     */
    refreshOrderDisplay() {
        try {
            console.log('🔄 refreshOrderDisplay: 开始执行', {
                订单数量: this.state.parsedOrders?.length,
                订单数据: this.state.parsedOrders
            });
            
            // 直接显示面板，移除所有复杂的渲染器查找
            const multiOrderPanel = document.getElementById('multiOrderPanel');
            if (!multiOrderPanel) {
                console.log('❌ multiOrderPanel 元素不存在');
                this.logger.log('多订单面板元素不存在', 'error');
                return;
            }
            console.log('✅ multiOrderPanel 元素找到');
            
            // 显示面板
            multiOrderPanel.classList.remove('hidden');
            multiOrderPanel.style.display = 'flex';
            console.log('✅ 面板已设置显示');
            
            // 重新填充OTA选项（确保同步当前用户的OTA配置）
            this.populateOtaOptions();
            
            // 更新内容
            const orderList = document.getElementById('multiOrderList');
            console.log('🔍 orderList 检查:', {
                元素存在: !!orderList,
                订单数据存在: !!this.state.parsedOrders,
                订单数量: this.state.parsedOrders?.length
            });
            
            if (orderList && this.state.parsedOrders?.length > 0) {
                const html = this.generateOrderListHTML(this.state.parsedOrders);
                console.log('🔧 生成的HTML:', html.substring(0, 200) + '...');
                orderList.innerHTML = html;
                console.log('✅ 订单列表内容已更新');
                
                // 调整地址文字大小
                this.adjustAddressTextSize();
            } else {
                console.log('⚠️ 无法更新订单列表内容');
            }
            
            console.log('✅ refreshOrderDisplay 执行完成');
            this.logger.log('多订单面板已显示', 'success');
        } catch (error) {
            console.error('❌ refreshOrderDisplay 执行失败:', error);
            this.logger.log('显示多订单面板失败', 'error', { error: error.message });
        }
    }

    /**
     * 处理全局多订单检测事件
     * @param {CustomEvent} event - 多订单检测事件
     */
    handleMultiOrderDetectedEvent(event) {
        // 🚨 强制DEBUG：立即输出执行状态
        console.log('🔧 handleMultiOrderDetectedEvent 开始执行...');
        console.log('🔍 this对象检查:', { 
            hasLogger: !!this.logger,
            hasState: !!this.state,
            loggerType: typeof this.logger,
            stateType: typeof this.state
        });
        
        try {
            // 🚨 强制DEBUG：添加更详细的错误捕获
            console.log('📋 第1步：验证事件数据');
            console.log('📋 事件对象类型:', typeof event);
            console.log('📋 事件对象:', event);
            console.log('📋 事件detail:', event?.detail);
            
            if (!event) {
                console.error('❌ event 对象不存在');
                return;
            }
            
            if (!event.detail) {
                console.error('❌ event.detail 不存在');
                if (this.logger?.log) {
                    this.logger.log('事件detail数据缺失', 'error');
                }
                return;
            }
            
            console.log('📋 开始解构数据...');
            const multiOrderResult = event.detail.multiOrderResult;
            const orderText = event.detail.orderText;
            console.log('📋 解构完成:', { 
                multiOrderResult: !!multiOrderResult, 
                orderText: orderText ? orderText.substring(0, 100) + '...' : 'null'
            });

            // 第2步：验证多订单结果
            console.log('📋 第2步：验证多订单结果');
            if (!multiOrderResult) {
                console.error('❌ multiOrderResult 不存在');
                if (this.logger?.log) {
                    this.logger.log('multiOrderResult数据缺失', 'error');
                }
                return;
            }

            console.log('📋 检查orders数组...');
            if (!multiOrderResult.orders || !Array.isArray(multiOrderResult.orders)) {
                console.error('❌ multiOrderResult.orders 无效:', multiOrderResult.orders);
                if (this.logger?.log) {
                    this.logger.log('订单数据无效', 'warning', { multiOrderResult });
                }
                return;
            }

            console.log('✅ 数据验证通过:', {
                订单数量: multiOrderResult.orderCount,
                订单数组长度: multiOrderResult.orders.length,
                置信度: multiOrderResult.confidence
            });

            // 第3步：更新状态
            console.log('📋 第3步：更新状态...');
            console.log('🔍 state检查前:', this.state);
            
            try {
                this.state.isMultiOrderMode = true;
                this.state.parsedOrders = multiOrderResult.orders;
                
                // 安全检查：确保selectedOrders是Set对象
                if (!(this.state.selectedOrders instanceof Set)) {
                    console.log('🔧 修复selectedOrders类型为Set');
                    this.state.selectedOrders = new Set();
                } else {
                    this.state.selectedOrders.clear();
                }
                
                console.log('✅ 状态更新成功:', {
                    isMultiOrderMode: this.state.isMultiOrderMode,
                    parsedOrders长度: this.state.parsedOrders.length
                });
            } catch (stateError) {
                console.error('❌ 状态更新失败:', stateError);
                throw stateError;
            }

            // 第4步：通知状态管理器（可选，不影响核心功能）
            console.log('📋 第4步：通知状态管理器...');
            console.log('🔍 stateManager检查:', !!this.stateManager);
            
            try {
                if (this.stateManager) {
                    this.stateManager.updateState({
                        isMultiOrderMode: true,
                        parsedOrders: multiOrderResult.orders,
                        detectionResult: multiOrderResult
                    });
                    console.log('✅ 状态管理器已更新');
                } else {
                    console.log('ℹ️ 状态管理器不可用，跳过');
                }
            } catch (stateManagerError) {
                console.error('❌ 状态管理器更新失败:', stateManagerError);
                // 不抛出错误，继续执行
            }

            // 第5步：刷新UI显示
            console.log('📋 第5步：刷新UI显示...');
            this.refreshOrderDisplay();

            console.log('✅ handleMultiOrderDetectedEvent 执行完成');
            this.logger.log('多订单事件处理完成', 'success');

        } catch (error) {
            console.error('❌ handleMultiOrderDetectedEvent 执行失败:', error);
            console.error('错误堆栈:', error.stack);
            this.logger.log('处理多订单事件失败', 'error', { 
                error: error.message, 
                stack: error.stack 
            });
        }
    }

    /**
     * 更新订单卡片显示（降级方案）
     */
    updateOrderCardsDisplay() {
        const orderCards = document.querySelectorAll('.order-card');
        orderCards.forEach((card, index) => {
            if (this.state.parsedOrders[index]) {
                const order = this.state.parsedOrders[index];

                // 更新语言显示
                const languageElement = card.querySelector('.order-language');
                if (languageElement && order.languages_id_array) {
                    const languageNames = order.languages_id_array.map(id => {
                        const langMap = { 2: '英文', 3: '马来文', 4: '中文', 5: '举牌' };
                        return langMap[id] || `语言${id}`;
                    });
                    languageElement.textContent = languageNames.join(', ');
                }

                // 更新OTA渠道显示
                const otaElement = card.querySelector('.order-ota');
                if (otaElement && order._otaChannel) {
                    otaElement.textContent = order._otaChannel;
                }
            }
        });
    }

    /**
     * 防抖函数
     * @param {function} func - 要防抖的函数
     * @param {number} wait - 等待时间
     * @returns {function} 防抖后的函数
     */
    debounce(func, wait) {
        return (...args) => {
            clearTimeout(this.debounceTimer);
            this.debounceTimer = setTimeout(() => func.apply(this, args), wait);
        };
    }


    /**
     * 生成订单列表HTML
     * @param {Array} orders - 订单数组
     * @returns {string} HTML字符串
     */
    generateOrderListHTML(orders) {
        return orders.map((order, index) => `
            <div class="order-card" data-index="${index}">
                <div class="order-card-header">
                    <div class="order-selector">
                        <input type="checkbox" class="order-checkbox" data-index="${index}">
                        <span class="order-number">订单 ${index + 1}</span>
                    </div>
                    <div class="order-status">
                        <span class="status-badge status-ready">就绪</span>
                    </div>
                </div>
                <div class="order-card-body">
                    <div class="order-details compact-inline-layout">
                        <div class="grid-item editable-field" data-field="customerName" onclick="window.OTA.multiOrderManager.editField(${index}, 'customerName')">
                            <span class="grid-value">${order.customer_name || '未知客户'}</span>
                        </div>
                        <div class="grid-item editable-field" data-field="customerContact" onclick="window.OTA.multiOrderManager.editField(${index}, 'customerContact')">
                            <span class="grid-value">${order.customer_contact || '未提供'}</span>
                        </div>
                        <div class="grid-item grid-item-route editable-field" data-field="pickup" onclick="window.OTA.multiOrderManager.editField(${index}, 'pickup')">
                            <span class="grid-value address-text">${order.pickup || '未指定上车地点'}</span>
                        </div>
                        <div class="grid-item grid-item-route editable-field" data-field="dropoff" onclick="window.OTA.multiOrderManager.editField(${index}, 'dropoff')">
                            <span class="grid-value address-text">${order.dropoff || '未指定下车地点'}</span>
                        </div>
                        <div class="grid-item editable-field" data-field="pickupDate" onclick="window.OTA.multiOrderManager.editField(${index}, 'pickupDate')">
                            <span class="grid-value">${order.pickup_date || '未指定'}</span>
                        </div>
                        <div class="grid-item editable-field" data-field="pickupTime" onclick="window.OTA.multiOrderManager.editField(${index}, 'pickupTime')">
                            <span class="grid-value">${order.pickup_time || '未指定'}</span>
                        </div>
                        <div class="grid-item editable-field" data-field="price" onclick="window.OTA.multiOrderManager.editField(${index}, 'price')">
                            <span class="grid-value">${order.ota_price || 0}</span>
                        </div>
                        <div class="grid-item editable-field" data-field="otaChannel" onclick="window.OTA.multiOrderManager.editField(${index}, 'otaChannel')">
                            <span class="grid-value">${this.getOtaChannelDisplay(order)}</span>
                        </div>
                        <div class="grid-item editable-field" data-field="vehicleType" onclick="window.OTA.multiOrderManager.editField(${index}, 'vehicleType')">
                            <span class="grid-value">${this.getVehicleTypeDisplay(order)}</span>
                        </div>
                        <div class="grid-item editable-field" data-field="drivingRegion" onclick="window.OTA.multiOrderManager.editField(${index}, 'drivingRegion')">
                            <span class="grid-value">${this.getDrivingRegionDisplay(order)}</span>
                        </div>
                        <div class="grid-item" data-field="language">
                            <span class="grid-value">${this.getLanguageDisplay(order)}</span>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    }

    /**
     * 调整地址文字大小以适应容器
     */
    adjustAddressTextSize() {
        setTimeout(() => {
            const addressContainers = document.querySelectorAll('.grid-item-route');
            addressContainers.forEach(container => {
                const element = container.querySelector('.grid-value.address-text');
                if (!element) return;
                
                // 重置状态
                container.removeAttribute('data-text-long');
                container.removeAttribute('data-text-overflow');
                
                // 创建临时元素测量文字宽度
                const temp = document.createElement('span');
                temp.style.visibility = 'hidden';
                temp.style.position = 'absolute';
                temp.style.fontSize = '11px';
                temp.textContent = element.textContent;
                document.body.appendChild(temp);
                
                const textWidth = temp.offsetWidth;
                document.body.removeChild(temp);
                
                // 如果文字宽度超过80px，标记为长文本
                if (textWidth > 80) {
                    container.setAttribute('data-text-long', 'true');
                    
                    // 检查是否需要增加容器高度（超过两行）
                    setTimeout(() => {
                        if (element.scrollHeight > element.offsetHeight) {
                            container.setAttribute('data-text-overflow', 'true');
                        }
                    }, 10);
                }
            });
        }, 50);
    }

    /**
     * 获取OTA渠道显示文本
     * @param {object} order - 订单对象
     * @returns {string} OTA渠道显示文本
     */
    getOtaChannelDisplay(order) {
        // 修复：直接使用订单数据中的OTA渠道值，确保批量设置后能正确显示
        const ota = order._otaChannel || order.ota || order.ota_channel || order.ota_name || '';
        if (!ota) return '未指定';

        // 直接返回OTA渠道值，不进行复杂的映射查找
        // 这样确保批量设置后的值能够立即正确显示
        return ota;
    }

    /**
     * 获取车型显示文本
     * @param {object} order - 订单对象
     * @returns {string} 车型显示文本
     */
    getVehicleTypeDisplay(order) {
        // 修复：使用正确的字段名 car_type_id
        const carTypeId = order.car_type_id || order.carTypeId || order.vehicle_type || order.car_type || order.vehicle || '';
        if (!carTypeId) return '标准车型';

        // 修复：使用与主界面相同的车型数据源
        const systemData = getAppState()?.get('systemData') || getApiService()?.staticData;
        if (systemData?.carTypes) {
            // 首先尝试按ID匹配（当值为数字时）
            const numericId = parseInt(carTypeId);
            if (!isNaN(numericId)) {
                const matchedCarType = systemData.carTypes.find(carType => carType.id === numericId);
                if (matchedCarType) {
                    return matchedCarType.name;
                }
            }
            
            // 然后尝试按名称匹配
            const matchedCarType = systemData.carTypes.find(carType =>
                carType.name === carTypeId ||
                carType.name.toLowerCase() === carTypeId.toLowerCase() ||
                carType.name.includes(carTypeId) ||
                carTypeId.includes(carType.name)
            );

            if (matchedCarType) {
                return matchedCarType.name;
            }
        }

        // 降级方案：使用基本映射
        const basicMapping = {
            '4座': '4 Seater Hatchback',
            '5座': '5 Seater',
            '豪华': 'Premium 5 Seater',
            '7座': '7 Seater MPV',
            '10座': '10 Seater MPV',
            '14座': '14 Seater Van',
            '18座': '18 Seater Van',
            '30座': '30 Seat Mini Bus',
            '44座': '44 Seater Bus',
            'sedan': '轿车',
            'suv': 'SUV',
            'mpv': 'MPV',
            'van': '面包车',
            'luxury': '豪华车'
        };

        const lowerCarType = carTypeId.toLowerCase();
        for (const [key, value] of Object.entries(basicMapping)) {
            if (lowerCarType.includes(key.toLowerCase())) {
                return value;
            }
        }

        return carTypeId;
    }

    /**
     * 获取驾驶区域显示文本
     * @param {object} order - 订单对象
     * @returns {string} 驾驶区域显示文本
     */
    getDrivingRegionDisplay(order) {
        // 修复：使用正确的字段名 driving_region_id
        const regionId = order.driving_region_id || order.drivingRegionId || order.driving_region || order.region || order.area || '';
        if (!regionId) return '市区';

        // 修复：使用与主界面相同的区域数据源
        const systemData = getAppState()?.get('systemData') || getApiService()?.staticData;
        if (systemData?.drivingRegions) {
            // 首先尝试按ID匹配（当值为数字时）
            const numericId = parseInt(regionId);
            if (!isNaN(numericId)) {
                const matchedRegion = systemData.drivingRegions.find(drivingRegion => drivingRegion.id === numericId);
                if (matchedRegion) {
                    return matchedRegion.name;
                }
            }
            
            // 然后尝试按名称匹配
            const matchedRegion = systemData.drivingRegions.find(drivingRegion =>
                drivingRegion.name === regionId ||
                drivingRegion.name.toLowerCase() === regionId.toLowerCase() ||
                drivingRegion.name.includes(regionId) ||
                regionId.includes(drivingRegion.name)
            );

            if (matchedRegion) {
                return matchedRegion.name;
            }
        }

        // 降级方案：使用基本映射
        const basicMapping = {
            'city': '市区',
            'suburban': '郊区',
            'airport': '机场',
            'downtown': '市中心',
            'uptown': '上城区',
            'outskirts': '市郊',
            'rural': '乡村',
            'highway': '高速路段',
            '市区': '市区',
            '郊区': '郊区',
            '机场': '机场'
        };

        const lowerRegion = regionId.toLowerCase();
        for (const [key, value] of Object.entries(basicMapping)) {
            if (lowerRegion.includes(key.toLowerCase())) {
                return value;
            }
        }

        return regionId;
    }

    /**
     * 获取需求语言显示文本
     * @param {object} order - 订单对象
     * @returns {string} 需求语言显示文本
     */
    getLanguageDisplay(order) {
        const language = order.preferred_language || order.language || order.customer_language || '';
        if (!language) return '中文';
        
        // 语言映射
        const languageMapping = {
            'zh': '中文',
            'zh-cn': '中文',
            'zh-tw': '繁体',
            'en': '英文',
            'english': '英文',
            'ms': '马来文',
            'malay': '马来文',
            'ta': '泰米尔文',
            'tamil': '泰米尔文',
            'hi': '印地文',
            'hindi': '印地文',
            'th': '泰文',
            'thai': '泰文',
            'ko': '韩文',
            'korean': '韩文',
            'ja': '日文',
            'japanese': '日文'
        };
        
        const lowerLang = language.toLowerCase();
        return languageMapping[lowerLang] || language;
    }

    /**
     * 睡眠函数
     * @param {number} ms - 毫秒数
     * @returns {Promise<void>}
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 获取API服务
     * @returns {object|null} API服务实例
     */
    getApiService() {
        if (window.OTA && window.OTA.apiService) {
            return window.OTA.apiService;
        }
        if (typeof getApiService === 'function') {
            return getApiService();
        }
        return null;
    }

    /**
     * 获取Logger实例
     * @returns {object} Logger实例
     */
    getLogger() {
        if (typeof getLogger === 'function') {
            return getLogger();
        }
        return {
            log: (message, level, data) => {
                console.log(`[MULTI-MGR-V2][${level?.toUpperCase() || 'INFO'}] ${message}`, data || '');
            }
        };
    }

    /**
     * 获取当前状态（兼容方法）
     * @returns {object} 当前状态
     */
    getState() {
        if (this.stateManager) {
            return this.stateManager.getState();
        }
        return this.state;
    }

    /**
     * 重置管理器
     */
    reset() {
        if (this.coordinator) {
            this.coordinator.reset();
        }
        if (this.stateManager) {
            this.stateManager.resetState();
        }
        
        this.state = {
            isMultiOrderMode: false,
            currentSegments: [],
            selectedOrders: new Set(),
            processedOrders: new Map(),
            parsedOrders: [],
            multiOrderResult: null
        };
    }

    /**
     * 销毁管理器
     */
    destroy() {
        if (this.debounceTimer) {
            clearTimeout(this.debounceTimer);
        }
        
        if (this.stateManager) {
            this.stateManager.destroy();
        }
        
        this.boundEventHandlers.clear();
        this.logger.log('多订单管理器已销毁', 'info');
    }

    /**
     * 初始化航班信息查询功能
     */
    initFlightInfoFeature() {
        // 查找所有可能的航班号输入框
        const flightInputSelectors = [
            'input[name*="flight"]',
            'input[id*="flight"]',
            'input[placeholder*="航班"]',
            'input[placeholder*="Flight"]',
            '#flightInfo',
            '.flight-input'
        ];

        flightInputSelectors.forEach(selector => {
            const inputs = document.querySelectorAll(selector);
            inputs.forEach(input => this.setupFlightInfoListener(input));
        });

        this.logger.log('✅ 航班信息查询功能已初始化', 'info');
    }

    /**
     * 为航班号输入框设置监听器
     * @param {HTMLElement} input - 输入框元素
     */
    setupFlightInfoListener(input) {
        if (!input || input.dataset.flightInfoEnabled) return;

        // 标记已启用，避免重复绑定
        input.dataset.flightInfoEnabled = 'true';

        // 创建航班信息显示区域
        const flightInfoContainer = this.createFlightInfoContainer(input);

        // 添加输入监听器
        input.addEventListener('input', async (event) => {
            const flightNumber = event.target.value.trim();

            if (flightNumber.length >= 2) {
                // 使用现有的航班号提取逻辑
                const extractedFlight = this.extractFlightNumber(flightNumber);
                if (extractedFlight) {
                    await this.queryAndDisplayFlightInfo(extractedFlight, flightInfoContainer, input.id);
                }
            } else {
                // 清空显示区域
                this.clearFlightInfoDisplay(flightInfoContainer);
            }
        });

        this.logger.log(`🛫 航班信息监听器已设置: ${input.id || input.name}`, 'info');
    }

    /**
     * 创建航班信息显示容器
     * @param {HTMLElement} input - 输入框元素
     * @returns {HTMLElement} 显示容器
     */
    createFlightInfoContainer(input) {
        // 检查是否已存在容器
        let container = input.parentNode.querySelector('.flight-info-container');
        if (container) return container;

        // 创建新容器
        container = document.createElement('div');
        container.className = 'flight-info-container';
        container.style.cssText = `
            margin-top: 8px;
            padding: 12px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background-color: #f9f9f9;
            font-size: 13px;
            display: none;
        `;

        // 插入到输入框后面
        input.parentNode.insertBefore(container, input.nextSibling);
        return container;
    }

    /**
     * 查询并显示航班信息
     * @param {string} flightNumber - 航班号
     * @param {HTMLElement} container - 显示容器
     * @param {string} inputId - 输入框ID
     */
    async queryAndDisplayFlightInfo(flightNumber, container, inputId) {
        // 显示加载状态
        this.showFlightInfoLoading(container);

        try {
            const flightService = this.getFlightInfoService();
            if (!flightService) {
                throw new Error('航班信息服务不可用');
            }

            // 使用防抖查询
            const flightInfo = await flightService.queryFlightInfoWithDebounce(flightNumber, inputId);
            this.displayFlightInfo(flightInfo, container);

        } catch (error) {
            this.displayFlightInfoError(error, container, flightNumber);
        }
    }

    /**
     * 显示加载状态
     * @param {HTMLElement} container - 显示容器
     */
    showFlightInfoLoading(container) {
        container.style.display = 'block';
        container.innerHTML = `
            <div style="display: flex; align-items: center; color: #666;">
                <div style="width: 16px; height: 16px; border: 2px solid #f3f3f3; border-top: 2px solid #3498db; border-radius: 50%; animation: spin 1s linear infinite; margin-right: 8px;"></div>
                正在查询航班信息...
            </div>
            <style>
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            </style>
        `;
    }

    /**
     * 显示航班信息
     * @param {Object} flightInfo - 航班信息
     * @param {HTMLElement} container - 显示容器
     */
    displayFlightInfo(flightInfo, container) {
        container.style.display = 'block';

        if (flightInfo.success) {
            container.innerHTML = `
                <div style="border-left: 4px solid #4CAF50; padding-left: 12px;">
                    <div style="font-weight: bold; color: #2E7D32; margin-bottom: 8px;">
                        ✈️ ${flightInfo.flightNumber} - ${flightInfo.status}
                    </div>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px; margin-bottom: 8px;">
                        <div>
                            <div style="font-weight: bold; color: #1976D2;">🛫 起飞</div>
                            <div>${flightInfo.departure.airport} - ${flightInfo.departure.airportName}</div>
                            <div>计划: ${flightInfo.departure.scheduledTime}</div>
                            ${flightInfo.departure.actualTime !== '未知' ? `<div>实际: ${flightInfo.departure.actualTime}</div>` : ''}
                        </div>
                        <div>
                            <div style="font-weight: bold; color: #1976D2;">🛬 到达</div>
                            <div>${flightInfo.arrival.airport} - ${flightInfo.arrival.airportName}</div>
                            <div>计划: ${flightInfo.arrival.scheduledTime}</div>
                            ${flightInfo.arrival.actualTime !== '未知' ? `<div>实际: ${flightInfo.arrival.actualTime}</div>` : ''}
                        </div>
                    </div>
                    ${flightInfo.delay !== '无延误信息' ? `<div style="color: ${flightInfo.delay === '准时' ? '#4CAF50' : '#FF9800'};">⏰ ${flightInfo.delay}</div>` : ''}
                    <div style="font-size: 11px; color: #666; margin-top: 8px;">
                        机型: ${flightInfo.aircraft} | 更新: ${flightInfo.lastUpdated}
                    </div>
                </div>
            `;
        } else {
            container.innerHTML = `
                <div style="border-left: 4px solid #FF9800; padding-left: 12px;">
                    <div style="font-weight: bold; color: #F57C00; margin-bottom: 8px;">
                        ⚠️ ${flightInfo.message}
                    </div>
                    <div style="color: #666; margin-bottom: 8px;">
                        查询航班号: ${flightInfo.flightNumber}
                    </div>
                    <div style="color: #666; font-size: 12px;">
                        💡 ${flightInfo.suggestion}
                    </div>
                    <div style="font-size: 11px; color: #999; margin-top: 8px;">
                        ${flightInfo.lastUpdated}
                    </div>
                </div>
            `;
        }
    }

    /**
     * 显示航班信息错误
     * @param {Error} error - 错误对象
     * @param {HTMLElement} container - 显示容器
     * @param {string} flightNumber - 航班号
     */
    displayFlightInfoError(error, container, flightNumber) {
        // 获取国际化错误消息
        const errorMessage = this.getI18nMessage('flight.error.unknown');

        container.style.display = 'block';
        container.innerHTML = `
            <div style="border-left: 4px solid #f44336; padding-left: 12px;">
                <div style="font-weight: bold; color: #d32f2f; margin-bottom: 8px;">
                    ❌ ${errorMessage}
                </div>
                <div style="color: #666; margin-bottom: 8px;">
                    ${this.getI18nMessage('common.flightNumber', { flightNumber })}
                </div>
            </div>
        `;
    }

    /**
     * 获取国际化消息
     * @param {string} key - 翻译键
     * @param {Object} params - 参数对象
     * @returns {string} 翻译后的消息
     */
    getI18nMessage(key, params = {}) {
        // 尝试获取国际化管理器
        if (window.OTA && window.OTA.t) {
            return window.OTA.t(key, params);
        }

        // 回退到全局t函数
        if (typeof window.t === 'function') {
            return window.t(key, params);
        }

        // 最终回退到硬编码消息
        const fallbackMessages = {
            'flight.error.unknown': '查询失败',
            'common.flightNumber': '查询航班号: {flightNumber}'
        };

        let message = fallbackMessages[key] || key;

        // 替换参数
        Object.keys(params).forEach(paramKey => {
            message = message.replace(`{${paramKey}}`, params[paramKey]);
        });

        return message;
    }

    /**
     * 清空航班信息显示
     * @param {HTMLElement} container - 显示容器
     */
    clearFlightInfoDisplay(container) {
        container.style.display = 'none';
        container.innerHTML = '';
    }

    /**
     * 提取航班号（使用现有逻辑）
     * @param {string} text - 输入文本
     * @returns {string|null} 提取的航班号
     */
    extractFlightNumber(text) {
        if (!text) return null;

        // 使用现有的航班号识别模式
        const patterns = [
            /\b([A-Z]{2,3}\d{1,4}[A-Z]?)\b/gi,
            /\b([A-Z]{2,3}\d{1,4}\/\d{1,3})\b/gi,
            /\b([0-9][A-Z]\d{3,4})\b/gi
        ];

        for (const pattern of patterns) {
            const matches = text.match(pattern);
            if (matches && matches.length > 0) {
                const candidate = matches[0].toUpperCase();
                if (this.validateFlightNumber(candidate)) {
                    return candidate;
                }
            }
        }

        return null;
    }

    /**
     * 验证航班号格式
     * @param {string} flightNumber - 航班号
     * @returns {boolean} 是否有效
     */
    validateFlightNumber(flightNumber) {
        const flightService = this.getFlightInfoService();
        if (flightService) {
            return flightService.validateFlightNumber(flightNumber);
        }

        // 备用验证逻辑
        const patterns = [
            /^[A-Z]{2,3}\d{1,4}[A-Z]?$/i,
            /^[A-Z]{2,3}\d{1,4}\/\d{1,3}$/i,
            /^[0-9][A-Z]\d{3,4}$/i
        ];

        return patterns.some(pattern => pattern.test(flightNumber.trim()));
    }

    /**
     * 获取航班信息服务
     * @returns {Object|null} 航班信息服务实例
     */
    getFlightInfoService() {
        return window.OTA?.flightInfoService || null;
    }

    /**
     * 初始化批量提交功能
     */
    initBatchSubmission() {
        try {
            // 绑定创建选中订单按钮事件
            const createSelectedBtn = document.getElementById('createSelectedOrdersBtn');
            if (createSelectedBtn) {
                createSelectedBtn.addEventListener('click', () => {
                    this.handleBatchSubmission();
                });
                console.log('✅ 批量提交按钮事件已绑定');
            } else {
                console.log('❌ 未找到创建选中订单按钮');
            }

            this.logger.log('批量提交功能初始化完成', 'success');
        } catch (error) {
            this.logger.log('批量提交功能初始化失败', 'error', { error: error.message });
        }
    }

    /**
     * 处理批量提交
     */
    async handleBatchSubmission() {
        try {
            console.log('🚀 开始批量提交订单...');
            
            const selectedOrders = Array.from(this.state.selectedOrders);
            if (selectedOrders.length === 0) {
                this.showMessage('请先选择要创建的订单', 'warning');
                return;
            }

            // 获取选中的订单数据
            const ordersToSubmit = selectedOrders
                .map(index => this.state.parsedOrders[index])
                .filter(order => order); // 过滤掉空值

            console.log(`📦 准备提交 ${ordersToSubmit.length} 个订单:`, ordersToSubmit);

            // 验证订单数据（只提醒）
            const validationResult = this.validateOrdersForSubmission(ordersToSubmit);
            if (validationResult.warnings && validationResult.warnings.length > 0) {
                this.showMessage(`⚠️ ${validationResult.warnings.join(', ')}`, 'warning');
            }

            // 显示提交进度
            this.showBatchSubmissionProgress(true);

            // 执行批量提交
            const results = await this.submitOrdersBatch(ordersToSubmit);
            
            // 处理提交结果
            this.handleBatchSubmissionResults(results);

        } catch (error) {
            console.error('❌ 批量提交失败:', error);
            this.logger.log('批量提交失败', 'error', { error: error.message });
            this.showMessage('批量提交过程中发生错误，请重试', 'error');
        } finally {
            // 隐藏进度显示
            this.showBatchSubmissionProgress(false);
        }
    }

    /**
     * 确保订单包含所有必需的API字段
     * @param {Object} order - 订单对象
     * @param {Object} apiService - API服务实例
     */
    ensureRequiredApiFields(order, apiService) {
        // 确保incharge_by_backend_user_id字段存在
        if (!order.incharge_by_backend_user_id) {
            const defaultBackendUserId = apiService.getDefaultBackendUserId();
            if (defaultBackendUserId) {
                order.incharge_by_backend_user_id = defaultBackendUserId;
                console.log(`✅ 为订单设置负责人ID: ${defaultBackendUserId}`);
            } else {
                // 使用紧急默认值
                order.incharge_by_backend_user_id = 1;
                console.warn('⚠️ 使用紧急默认负责人ID: 1');
            }
        }

        // 确保sub_category_id字段存在（默认接机服务）
        if (!order.sub_category_id) {
            order.sub_category_id = 2; // Pickup
            console.log('✅ 设置默认服务类型: 接机 (ID=2)');
        }

        // 确保车型ID字段存在
        if (!order.car_type_id && !order.carTypeId) {
            order.car_type_id = 5; // 默认5座车
            console.log('✅ 设置默认车型: 5座车 (ID=5)');
        }

        // 确保区域ID字段存在
        if (!order.driving_region_id && !order.drivingRegionId) {
            order.driving_region_id = 1; // 默认KL/Selangor
            console.log('✅ 设置默认区域: KL/Selangor (ID=1)');
        }

        // 确保OTA字段存在，优先使用内部标识字段
        if (!order.ota) {
            if (order._otaChannel) {
                order.ota = order._otaChannel;
                console.log(`✅ 从内部标识复制OTA渠道: ${order._otaChannel}`);
            } else {
                order.ota = 'GoMyHire Direct';
                console.log('✅ 设置默认OTA渠道: GoMyHire Direct');
            }
        }
    }

    /**
     * 使用全局字段标准化处理订单数据
     * @param {Object} order - 订单对象
     * @returns {Object} 标准化后的订单对象
     */
    standardizeOrderFields(order) {
        try {
            // 使用全局字段标准化层（如果可用）
            if (window.standardizeFieldsToApi && typeof window.standardizeFieldsToApi === 'function') {
                const standardized = window.standardizeFieldsToApi(order, 'multi-order-batch');
                console.log('✅ 使用全局字段标准化处理', { original: Object.keys(order).length, standardized: Object.keys(standardized).length });
                return standardized;
            } else {
                console.log('⚠️ 全局字段标准化层不可用，使用原始订单数据');
                return order;
            }
        } catch (error) {
            console.error('❌ 字段标准化失败，使用原始订单数据:', error);
            return order;
        }
    }

    /**
     * 验证订单数据是否可以提交
     * @param {Array} orders - 订单数组
     * @returns {Object} 验证结果
     */
    validateOrdersForSubmission(orders) {
        const warnings = [];

        if (!orders || orders.length === 0) {
            warnings.push('没有选中的订单');
        }

        orders.forEach((order, index) => {
            const orderPrefix = `订单 ${index + 1}`;

            // 基本字段检查（只提醒，不阻止）
            if (!order.ota) {
                warnings.push(`${orderPrefix}: 缺少OTA渠道`);
            }
            if (!order.pickup || !order.dropoff) {
                warnings.push(`${orderPrefix}: 缺少接送地点信息`);
            }
            if (!order.date || !order.time) {
                warnings.push(`${orderPrefix}: 缺少日期时间信息`);
            }
        });

        return {
            isValid: true, // 始终允许提交
            warnings: warnings
        };
    }

    /**
     * 批量提交订单到API
     * @param {Array} orders - 订单数组
     * @returns {Array} 提交结果数组
     */
    async submitOrdersBatch(orders) {
        const results = [];
        const apiService = getApiService();

        if (!apiService) {
            throw new Error('API服务未初始化');
        }

        // 逐个提交订单（确保与主界面逻辑一致）
        for (let i = 0; i < orders.length; i++) {
            const order = orders[i];
            const orderIndex = i + 1;

            try {
                console.log(`📤 正在提交订单 ${orderIndex}/${orders.length}:`, order);
                
                // 确保关键字段存在（与单订单模式保持一致）
                this.ensureRequiredApiFields(order, apiService);
                
                // 使用全局字段标准化处理（如果可用）
                const standardizedOrder = this.standardizeOrderFields(order);
                
                // 使用与主界面相同的API调用方法
                const result = await apiService.createOrder(standardizedOrder);
                
                results.push({
                    orderIndex: orderIndex,
                    success: true,
                    result: result,
                    order: order
                });

                console.log(`✅ 订单 ${orderIndex} 提交成功:`, result);

            } catch (error) {
                console.error(`❌ 订单 ${orderIndex} 提交失败:`, error);
                
                results.push({
                    orderIndex: orderIndex,
                    success: false,
                    error: error.message || '提交失败',
                    order: order
                });
            }

            // 更新进度
            this.updateBatchSubmissionProgress(orderIndex, orders.length);
        }

        return results;
    }

    /**
     * 处理批量提交结果
     * @param {Array} results - 提交结果数组
     */
    handleBatchSubmissionResults(results) {
        const successCount = results.filter(r => r.success).length;
        const totalCount = results.length;
        
        console.log(`📊 批量提交结果统计: 成功 ${successCount}/${totalCount}`);

        // 显示结果摘要
        if (successCount === totalCount) {
            this.showMessage(`🎉 所有订单提交成功！(${successCount}/${totalCount})`, 'success');
        } else if (successCount > 0) {
            this.showMessage(`⚠️ 部分订单提交成功: ${successCount}/${totalCount}`, 'warning');
        } else {
            this.showMessage(`❌ 所有订单提交失败 (0/${totalCount})`, 'error');
        }

        // 详细结果日志
        results.forEach(result => {
            const status = result.success ? '✅ 成功' : '❌ 失败';
            const message = result.success 
                ? `订单 ${result.orderIndex} 创建成功`
                : `订单 ${result.orderIndex} 创建失败: ${result.error}`;
            
            this.logger.log(message, result.success ? 'success' : 'error');
        });

        // 清空选中状态（成功的订单）
        const successfulIndices = results
            .filter(r => r.success)
            .map(r => r.orderIndex - 1);
        
        successfulIndices.forEach(index => {
            this.state.selectedOrders.delete(index);
        });

        // 刷新UI显示
        this.refreshOrderDisplay();
        this.updateSelectedOrderCount();
    }

    /**
     * 显示/隐藏批量提交进度
     * @param {boolean} show - 是否显示
     */
    showBatchSubmissionProgress(show) {
        const statusElement = document.querySelector('.batch-create-status');
        if (!statusElement) return;

        if (show) {
            statusElement.innerHTML = `
                <div class="batch-progress">
                    <div class="progress-indicator">
                        <span class="progress-text">正在批量创建订单...</span>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 0%"></div>
                        </div>
                        <span class="progress-count">0/0</span>
                    </div>
                </div>
            `;
            statusElement.style.display = 'block';
        } else {
            statusElement.style.display = 'none';
            statusElement.innerHTML = '';
        }
    }

    /**
     * 更新批量提交进度
     * @param {number} current - 当前进度
     * @param {number} total - 总数
     */
    updateBatchSubmissionProgress(current, total) {
        const progressFill = document.querySelector('.progress-fill');
        const progressCount = document.querySelector('.progress-count');
        
        if (progressFill && progressCount) {
            const percentage = Math.round((current / total) * 100);
            progressFill.style.width = `${percentage}%`;
            progressCount.textContent = `${current}/${total}`;
        }
    }

    /**
     * 显示消息提示
     * @param {string} message - 消息内容
     * @param {string} type - 消息类型 ('success', 'warning', 'error', 'info')
     */
    showMessage(message, type = 'info') {
        // 使用现有的UI管理器显示消息
        if (window.OTA && window.OTA.uiManager) {
            window.OTA.uiManager.showAlert(message, type);
        } else {
            // 后备方案：使用浏览器alert
            alert(message);
        }
    }

    /**
     * 更新选中订单计数显示
     */
    updateSelectedOrderCount() {
        const countElement = document.getElementById('selectedOrderCount');
        if (countElement) {
            const count = this.state.selectedOrders.size;
            countElement.textContent = `已选择 ${count} 个订单`;
        }
    }

    /**
     * 刷新OTA选项（公开方法，供外部调用）
     */
    refreshOtaOptions() {
        console.log('🔄 外部请求刷新OTA选项');
        this.populateOtaOptions();
    }

    /**
     * 编辑字段功能 - 修复缺失的方法
     * @param {number} orderIndex - 订单索引
     * @param {string} fieldName - 字段名称
     */
    editField(orderIndex, fieldName) {
        try {
            console.log(`🔧 编辑字段: 订单${orderIndex}, 字段${fieldName}`);

            // 检查是否为不可编辑的字段
            if (fieldName === 'otaChannel') {
                console.log('⚠️ OTA渠道字段不支持单独编辑，请使用批量设置功能');
                // 可以在这里添加用户提示
                if (window.showToast) {
                    window.showToast('OTA渠道需要通过批量操作设置，不支持单独编辑', 'warning');
                }
                return;
            }

            // 验证参数
            if (!this.state.parsedOrders || orderIndex >= this.state.parsedOrders.length) {
                console.error('❌ 无效的订单索引:', orderIndex);
                return;
            }

            const order = this.state.parsedOrders[orderIndex];
            if (!order) {
                console.error('❌ 订单数据不存在:', orderIndex);
                return;
            }

            // 找到对应的字段元素
            const orderCard = document.querySelector(`.order-card[data-index="${orderIndex}"]`);
            if (!orderCard) {
                console.error('❌ 找不到订单卡片:', orderIndex);
                return;
            }

            const fieldElement = orderCard.querySelector(`[data-field="${fieldName}"]`);
            if (!fieldElement) {
                console.error('❌ 找不到字段元素:', fieldName);
                return;
            }

            // 检查是否已经在编辑模式
            if (fieldElement.classList.contains('editing')) {
                // 保存编辑
                this.saveFieldEdit(orderIndex, fieldName, fieldElement);
            } else {
                // 开始编辑
                this.startFieldEdit(orderIndex, fieldName, fieldElement, order);
            }

        } catch (error) {
            console.error('❌ 编辑字段失败:', error);
            this.logger?.log('编辑字段失败', 'error', { orderIndex, fieldName, error: error.message });
        }
    }

    /**
     * 开始字段编辑
     * @param {number} orderIndex - 订单索引
     * @param {string} fieldName - 字段名称
     * @param {HTMLElement} fieldElement - 字段元素
     * @param {object} order - 订单数据
     */
    startFieldEdit(orderIndex, fieldName, fieldElement, order) {
        const valueElement = fieldElement.querySelector('.grid-value');
        if (!valueElement) return;

        // 获取当前值
        const currentValue = this.getFieldValue(order, fieldName);

        // 标记为编辑状态
        fieldElement.classList.add('editing');

        // 根据字段类型创建编辑器
        let editElement;

        if (['vehicleType', 'drivingRegion'].includes(fieldName)) {
            // 下拉选择字段
            editElement = this.createSelectEditor(fieldName, currentValue);
        } else if (fieldName === 'pickupDate') {
            // 日期字段
            editElement = document.createElement('input');
            editElement.type = 'date';
            editElement.value = currentValue || '';
        } else if (fieldName === 'pickupTime') {
            // 时间字段
            editElement = document.createElement('input');
            editElement.type = 'time';
            editElement.value = currentValue || '';
        } else if (fieldName === 'price') {
            // 价格字段
            editElement = document.createElement('input');
            editElement.type = 'number';
            editElement.step = '0.01';
            editElement.min = '0';
            editElement.value = currentValue || '0';
        } else {
            // 文本字段
            editElement = document.createElement('input');
            editElement.type = 'text';
            editElement.value = currentValue || '';
        }

        // 设置样式和事件
        editElement.className = 'field-editor';
        editElement.style.width = '100%';
        editElement.style.border = '1px solid #007bff';
        editElement.style.borderRadius = '4px';
        editElement.style.padding = '4px 8px';

        // 修复：简化事件绑定，移除复杂的延迟和事件阻止逻辑
        if (['vehicleType', 'drivingRegion'].includes(fieldName)) {
            // 下拉选择字段：只使用 change 事件，避免blur事件冲突
            editElement.addEventListener('change', () => {
                this.saveFieldEdit(orderIndex, fieldName, fieldElement);
            });

            // 键盘事件处理 - 简化逻辑
            editElement.addEventListener('keydown', (e) => {
                e.stopPropagation(); // 阻止事件传播
                if (e.key === 'Escape') {
                    this.cancelFieldEdit(fieldElement, valueElement.textContent);
                }
            });
        } else {
            // 非下拉字段：使用 blur 事件
            editElement.addEventListener('blur', () => {
                this.saveFieldEdit(orderIndex, fieldName, fieldElement);
            });

            editElement.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    this.saveFieldEdit(orderIndex, fieldName, fieldElement);
                } else if (e.key === 'Escape') {
                    this.cancelFieldEdit(fieldElement, valueElement.textContent);
                }
            });
        }

        // 替换显示元素
        valueElement.style.display = 'none';
        fieldElement.appendChild(editElement);

        // 标记字段为编辑状态，防止全局事件干扰
        fieldElement.setAttribute('data-editing', 'true');
        fieldElement.setAttribute('data-field-type', fieldName);
        
        // 阻止编辑元素的点击事件向上传播，避免全局事件干扰
        editElement.addEventListener('click', (e) => {
            e.stopPropagation();
        });

        // 延迟聚焦，避免与DOM操作冲突
        setTimeout(() => {
            editElement.focus();
            
            if (editElement.type === 'text') {
                editElement.select();
            } else if (editElement.tagName === 'SELECT') {
                // 对于下拉菜单，确保正确显示并触发下拉
                editElement.style.display = 'block';
                editElement.style.visibility = 'visible';
            }
        }, 0);

        console.log(`✅ 开始编辑字段: ${fieldName}`);
    }

    /**
     * 创建下拉选择编辑器
     * @param {string} fieldName - 字段名称
     * @param {string} currentValue - 当前值
     * @returns {HTMLSelectElement} 下拉选择元素
     */
    createSelectEditor(fieldName, currentValue) {
        const select = document.createElement('select');
        select.className = 'field-editor';

        // 简化样式设置，避免复杂的CSS规则冲突
        select.style.width = '100%';
        select.style.zIndex = '1000';
        select.style.position = 'relative';
        select.style.background = 'white';
        select.style.border = '1px solid #007bff';
        select.style.padding = '4px';

        // 添加空选项
        const emptyOption = document.createElement('option');
        emptyOption.value = '';
        emptyOption.textContent = '请选择...';
        select.appendChild(emptyOption);

        // 根据字段类型获取选项
        let options = [];

        if (fieldName === 'vehicleType') {
            // 从系统数据获取车型选项
            const systemData = getAppState()?.get('systemData') || getApiService()?.staticData;
            options = (systemData?.carTypes || []).map(item => ({
                value: item.name,
                text: item.name
            }));

            // 降级方案：如果没有获取到选项，提供基本选项
            if (!options || options.length === 0) {
                options = [
                    { value: '4 Seater Hatchback', text: '4 Seater Hatchback' },
                    { value: '5 Seater', text: '5 Seater' },
                    { value: 'Premium 5 Seater', text: 'Premium 5 Seater' },
                    { value: '7 Seater MPV', text: '7 Seater MPV' },
                    { value: '10 Seater MPV', text: '10 Seater MPV' }
                ];
            }
        } else if (fieldName === 'drivingRegion') {
            // 从系统数据获取区域选项
            const systemData = getAppState()?.get('systemData') || getApiService()?.staticData;
            options = (systemData?.drivingRegions || []).map(item => ({
                value: item.name,
                text: item.name
            }));

            // 降级方案：如果没有获取到选项，提供基本选项
            if (!options || options.length === 0) {
                options = [
                    { value: '市区', text: '市区' },
                    { value: '郊区', text: '郊区' },
                    { value: '机场', text: '机场' },
                    { value: '高速路段', text: '高速路段' }
                ];
            }
        }

        // 添加选项
        options.forEach(option => {
            const optionElement = document.createElement('option');
            optionElement.value = option.value || option;
            optionElement.textContent = option.text || option;
            if ((option.value || option) === currentValue) {
                optionElement.selected = true;
            }
            select.appendChild(optionElement);
        });

        console.log(`✅ 创建${fieldName}下拉编辑器，共${options.length}个选项`);
        return select;
    }

    /**
     * 保存字段编辑
     * @param {number} orderIndex - 订单索引
     * @param {string} fieldName - 字段名称
     * @param {HTMLElement} fieldElement - 字段元素
     */
    saveFieldEdit(orderIndex, fieldName, fieldElement) {
        try {
            const editElement = fieldElement.querySelector('.field-editor');
            const valueElement = fieldElement.querySelector('.grid-value');

            if (!editElement || !valueElement) return;

            const newValue = editElement.value.trim();

            // 更新订单数据
            this.updateOrderFieldValue(orderIndex, fieldName, newValue);

            // 更新显示
            const displayValue = this.getFieldDisplayValue(fieldName, newValue);
            valueElement.textContent = displayValue;

            // 清理编辑状态
            this.cleanupFieldEdit(fieldElement, editElement, valueElement);

            // 通知状态管理器
            if (this.stateManager) {
                this.stateManager.updateOrders(this.state.parsedOrders);
            }

            console.log(`✅ 字段保存成功: ${fieldName} = ${newValue}`);

        } catch (error) {
            console.error('❌ 保存字段失败:', error);
            this.cancelFieldEdit(fieldElement, '保存失败');
        }
    }

    /**
     * 取消字段编辑
     * @param {HTMLElement} fieldElement - 字段元素
     * @param {string} originalValue - 原始值
     */
    cancelFieldEdit(fieldElement, originalValue) {
        const editElement = fieldElement.querySelector('.field-editor');
        const valueElement = fieldElement.querySelector('.grid-value');

        if (editElement && valueElement) {
            this.cleanupFieldEdit(fieldElement, editElement, valueElement);
            valueElement.textContent = originalValue;
        }

        console.log('🔄 取消字段编辑');
    }

    /**
     * 清理字段编辑状态
     * @param {HTMLElement} fieldElement - 字段元素
     * @param {HTMLElement} editElement - 编辑元素
     * @param {HTMLElement} valueElement - 值显示元素
     */
    cleanupFieldEdit(fieldElement, editElement, valueElement) {
        fieldElement.classList.remove('editing');
        fieldElement.removeAttribute('data-editing');
        fieldElement.removeAttribute('data-field-type');
        editElement.remove();
        valueElement.style.display = '';
    }

    /**
     * 获取字段值
     * @param {object} order - 订单对象
     * @param {string} fieldName - 字段名称
     * @returns {string} 字段值
     */
    getFieldValue(order, fieldName) {
        const fieldMapping = {
            customerName: order.customer_name || order.customerName || '',
            customerContact: order.customer_contact || order.customerContact || '',
            pickup: order.pickup || '',
            dropoff: order.destination || order.dropoff || '',
            pickupDate: order.pickup_date || order.pickupDate || '',
            pickupTime: order.pickup_time || order.pickupTime || '',
            price: order.ota_price || order.price || '0',
            otaChannel: order._otaChannel || order.ota_channel || order.ota || '',
            vehicleType: order.vehicle_type || order.car_type || order.vehicleType || '',
            drivingRegion: order.driving_region || order.region || order.drivingRegion || ''
        };

        return fieldMapping[fieldName] || '';
    }

    /**
     * 更新订单字段值
     * @param {number} orderIndex - 订单索引
     * @param {string} fieldName - 字段名称
     * @param {string} newValue - 新值
     */
    updateOrderFieldValue(orderIndex, fieldName, newValue) {
        if (!this.state.parsedOrders[orderIndex]) return;

        const order = this.state.parsedOrders[orderIndex];

        // 更新对应的字段
        switch (fieldName) {
            case 'customerName':
                order.customer_name = newValue;
                order.customerName = newValue;
                break;
            case 'customerContact':
                order.customer_contact = newValue;
                order.customerContact = newValue;
                break;
            case 'pickup':
                order.pickup = newValue;
                break;
            case 'dropoff':
                order.destination = newValue;
                order.dropoff = newValue;
                break;
            case 'pickupDate':
                order.pickup_date = newValue;
                order.pickupDate = newValue;
                break;
            case 'pickupTime':
                order.pickup_time = newValue;
                order.pickupTime = newValue;
                break;
            case 'price':
                order.ota_price = parseFloat(newValue) || 0;
                order.price = parseFloat(newValue) || 0;
                break;
            case 'otaChannel':
                order._otaChannel = newValue;
                order.ota_channel = newValue;
                order.ota = newValue;
                break;
            case 'vehicleType':
                order.vehicle_type = newValue;
                order.car_type = newValue;
                order.vehicleType = newValue;
                break;
            case 'drivingRegion':
                order.driving_region = newValue;
                order.region = newValue;
                order.drivingRegion = newValue;
                break;
        }
    }

    /**
     * 获取字段显示值
     * @param {string} fieldName - 字段名称
     * @param {string} value - 原始值
     * @returns {string} 显示值
     */
    getFieldDisplayValue(fieldName, value) {
        if (!value) {
            const defaultValues = {
                customerName: '未知客户',
                customerContact: '未提供',
                pickup: '未指定上车地点',
                dropoff: '未指定下车地点',
                pickupDate: '未指定',
                pickupTime: '未指定',
                price: '0',
                otaChannel: '未指定',
                vehicleType: '标准车型',
                drivingRegion: '市区'
            };
            return defaultValues[fieldName] || '未指定';
        }

        return value;
    }

}

// 创建全局实例
console.log('🚀 创建 MultiOrderManagerV2 实例...');
const multiOrderManagerV2 = new MultiOrderManagerV2();
console.log('✅ MultiOrderManagerV2 实例已创建:', multiOrderManagerV2);

// 暴露到OTA命名空间
window.OTA = window.OTA || {};
window.OTA.MultiOrderManagerV2 = MultiOrderManagerV2;
window.OTA.multiOrderManagerV2 = multiOrderManagerV2;

// 注册到OTA注册中心
if (window.OTA && window.OTA.Registry) {
    window.OTA.Registry.registerService('multiOrderManagerV2', multiOrderManagerV2, '@OTA_MULTI_ORDER_MANAGER_V2');
}

// 向后兼容：智能切换管理器
if (window.OTA.multiOrderManager) {
    // 如果原管理器存在，保存为备份
    window.OTA.multiOrderManagerLegacy = window.OTA.multiOrderManager;
    console.log('已备份旧版多订单管理器为 window.OTA.multiOrderManagerLegacy');
}

// 设置v2为主要管理器
window.OTA.multiOrderManager = multiOrderManagerV2;
console.log('多订单管理器v2已设置为主要管理器');

// 系统状态报告
setTimeout(() => {
    const healthReport = {
        system: 'Multi-Order Manager V2',
        status: 'ACTIVE',
        modules: {
            coordinator: !!window.OTA?.multiOrderCoordinator,
            stateManager: !!window.OTA?.multiOrderStateManager,
            batchProcessor: !!window.OTA?.batchProcessor,
            detector: !!window.OTA?.multiOrderDetector,
            processor: !!window.OTA?.multiOrderProcessor,
            renderer: !!window.OTA?.multiOrderRenderer
        },
        compatibility: {
            legacyManager: !!window.OTA?.multiOrderManagerLegacy,
            globalAccess: !!window.OTA?.multiOrderManager,
            backwardMethods: !!(window.OTA?.multiOrderManager?.analyzeInputForMultiOrder && 
                              window.OTA?.multiOrderManager?.showMultiOrderPanel)
        }
    };
    
    console.log('🚀 多订单系统状态报告:', healthReport);
    
    const allModulesLoaded = Object.values(healthReport.modules).every(Boolean);
    const compatibilityOk = Object.values(healthReport.compatibility).every(Boolean);
    
    if (allModulesLoaded && compatibilityOk) {
        console.log('✅ 多订单系统完整性验证通过 - 所有模块正常加载');
    } else {
        console.warn('⚠️ 多订单系统存在问题，请检查完整性报告');
    }
}, 1000);

}