# GoMyHire API 字段需求文档

## 📋 API 概览

**API 基础信息**
- **基础 URL**: `https://gomyhire.com.my/api`
- **认证方式**: Bearer <PERSON> (JWT)
- **内容类型**: `application/json`
- **超时时间**: 30秒
- **默认编码**: UTF-8

## 🔐 认证端点

### 登录接口
```http
POST /login
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "Gomyhire@123456"
}
```

**响应格式**:
```json
{
    "status": true,
    "token": "2409|F0hye4nKqL58qhcdW8knwLwLMbQltjl35uggBwGA"
}
```

**Token 处理规则**:
- 提取 `|` 后的实际 token 部分
- 存储在 AppState 中用于后续请求
- 支持"记住我"功能

## 📦 订单创建接口

### 创建订单端点
```http
POST /create_order
Content-Type: application/json
Authorization: 不需要认证
```

## 📋 字段需求详细说明

### 必填字段 (Required Fields)

#### 1. sub_category_id
- **类型**: `number`
- **必填**: ✅ 是
- **描述**: 服务类型ID
- **可选值**:
  - `2` - 接机服务 (Pickup)
  - `3` - 送机服务 (Dropoff)  
  - `4` - 包车服务 (Charter)
- **验证规则**: 必须为2、3、4之一
- **默认值**: 根据订单内容智能检测

#### 2. ota_reference_number
- **类型**: `string`
- **必填**: ✅ 是
- **描述**: OTA平台参考编号
- **格式要求**: 
  - 长度: 6-20个字符
  - 允许字符: 字母、数字、连字符
  - 不能为空或纯空格
- **验证规则**: 
  - 必须唯一，不能重复
  - 自动识别多种OTA格式
- **智能提取规则**:
  - Chong Dealer: `CD[A-Z0-9]{6,12}`, `CHONG[A-Z0-9]{4,8}`
  - Klook: `KL[A-Z0-9]{8,12}`, `KLOOK[A-Z0-9]{4,8}`
  - KKday: `KK[A-Z0-9]{8,12}`, `KKDAY[A-Z0-9]{4,8}`
  - 通用格式: `[A-Z]{2,4}[0-9]{6,10}`, `[A-Z0-9]{8,15}`

#### 3. car_type_id
- **类型**: `number`
- **必填**: ✅ 是
- **描述**: 车型ID
- **可选值**:
  - `5` - 5座车 (3乘客, 3个大行李)
  - `15` - 7座MPV (5乘客, 4个大行李)
  - `20` - 10座MPV/Van (7乘客, 6个大行李)
  - `32` - Velfire/Alphard (6乘客, 4个大行李)
- **智能推荐规则**:
  - 乘客数 ≤ 3 → 推荐 5座车 (ID: 5)
  - 乘客数 4-5 → 推荐 7座MPV (ID: 15)
  - 乘客数 6-7 → 推荐 10座MPV (ID: 20)
- **验证规则**: 必须为系统定义的有效车型ID

#### 4. incharge_by_backend_user_id
- **类型**: `number`
- **必填**: ✅ 是
- **描述**: 后台负责人ID
- **可选值**:
  - `1` - Super Admin
  - `37` - smw (<EMAIL>)
  - `310` - Jcy (<EMAIL>)
- **智能匹配规则**:
  - 根据登录用户邮箱自动匹配
  - 如果没有匹配，默认为 `1`
- **验证规则**: 必须为有效的后台用户ID

### 可选字段 (Optional Fields)

#### 5. ota
- **类型**: `string`
- **必填**: ❌ 否
- **描述**: OTA平台名称
- **示例**: "Chong Dealer", "Klook", "KKday"
- **默认值**: 根据参考编号自动检测
- **长度限制**: 最大50个字符

#### 6. ota_price
- **类型**: `number`
- **必填**: ❌ 否
- **描述**: OTA平台价格
- **格式**: 数字，支持小数点
- **验证规则**: 必须为正数
- **币种**: 默认为MYR (马来西亚林吉特)
- **转换支持**: 支持MYR、USD、SGD、CNY之间转换

#### 7. customer_name
- **类型**: `string`
- **必填**: ❌ 否
- **描述**: 客户姓名
- **长度限制**: 最大100个字符
- **验证规则**: 
  - 允许中文、英文、数字、空格
  - 不能包含特殊符号
- **智能语言检测**: 根据姓名自动检测语言偏好

#### 8. customer_contact
- **类型**: `string`
- **必填**: ❌ 否
- **描述**: 客户联系电话
- **格式要求**: 
  - 正则表达式: `/^[\+]?[0-9\s\-\(\)]{8,}$/`
  - 最少8位数字
  - 可包含国家代码 (+60)
  - 可包含空格、连字符、括号
- **验证规则**: 必须为有效的电话号码格式
- **示例**: "+60123456789", "012-345-6789", "(012) 345 6789"

#### 9. customer_email
- **类型**: `string`
- **必填**: ❌ 否
- **描述**: 客户邮箱地址
- **格式要求**: 
  - 正则表达式: `/^[^\s@]+@[^\s@]+\.[^\s@]+$/`
  - 必须包含@符号
  - 必须包含有效的域名
- **验证规则**: 必须为有效的邮箱格式
- **示例**: "<EMAIL>"

#### 10. flight_info
- **类型**: `string`
- **必填**: ❌ 否
- **描述**: 航班信息
- **格式**: 自由文本，通常包含航班号、时间等
- **长度限制**: 最大200个字符
- **示例**: "MH123 15:30", "CX712 到达 18:45"

#### 11. pickup
- **类型**: `string`
- **必填**: ❌ 否
- **描述**: 接客地点
- **长度限制**: 最大200个字符
- **示例**: "KLIA2 Terminal", "Kuala Lumpur Sentral"

#### 12. pickup_lat
- **类型**: `number`
- **必填**: ❌ 否
- **描述**: 接客地点纬度
- **格式**: 浮点数，精确到小数点后6位
- **范围**: -90.0 到 90.0
- **示例**: 2.745578

#### 13. pickup_long
- **类型**: `number`
- **必填**: ❌ 否
- **描述**: 接客地点经度
- **格式**: 浮点数，精确到小数点后6位
- **范围**: -180.0 到 180.0
- **示例**: 101.709917

#### 14. date
- **类型**: `string`
- **必填**: ❌ 否
- **描述**: 服务日期
- **格式**: `YYYY-MM-DD`
- **验证规则**: 
  - 必须为有效日期格式
  - 不能早于今天
  - 正则表达式: `/^\d{4}-\d{2}-\d{2}$/`
- **示例**: "2025-07-20"

#### 15. time
- **类型**: `string`
- **必填**: ❌ 否
- **描述**: 服务时间
- **格式**: `HH:MM` (24小时制)
- **验证规则**: 
  - 小时: 00-23
  - 分钟: 00-59
  - 正则表达式: `/^([01]\d|2[0-3]):([0-5]\d)$/`
- **示例**: "14:30", "09:15"

#### 16. destination
- **类型**: `string`
- **必填**: ❌ 否
- **描述**: 目的地
- **长度限制**: 最大200个字符
- **示例**: "KLIA Terminal 1", "Petronas Twin Towers"

#### 17. destination_lat
- **类型**: `number`
- **必填**: ❌ 否
- **描述**: 目的地纬度
- **格式**: 浮点数，精确到小数点后6位
- **范围**: -90.0 到 90.0

#### 18. destination_long
- **类型**: `number`
- **必填**: ❌ 否
- **描述**: 目的地经度
- **格式**: 浮点数，精确到小数点后6位
- **范围**: -180.0 到 180.0

#### 19. passenger_number
- **类型**: `number`
- **必填**: ❌ 否
- **描述**: 乘客数量
- **验证规则**: 
  - 必须为正整数
  - 范围: 1-20
  - 不能超过车型乘客限制
- **默认值**: 1

#### 20. luggage_number
- **类型**: `number`
- **必填**: ❌ 否
- **描述**: 行李数量
- **验证规则**: 
  - 必须为非负整数
  - 范围: 0-30
- **默认值**: 0

#### 21. driver_fee
- **类型**: `number`
- **必填**: ❌ 否
- **描述**: 司机费用
- **格式**: 数字，支持小数点
- **验证规则**: 必须为非负数
- **币种**: MYR

#### 22. driver_collect
- **类型**: `number`
- **必填**: ❌ 否
- **描述**: 司机收取金额
- **格式**: 数字，支持小数点
- **验证规则**: 必须为非负数
- **币种**: MYR

#### 23. tour_guide
- **类型**: `boolean`
- **必填**: ❌ 否
- **描述**: 是否需要导游服务
- **可选值**: `true`, `false`
- **默认值**: `false`

#### 24. baby_chair
- **类型**: `boolean`
- **必填**: ❌ 否
- **描述**: 是否需要婴儿座椅
- **可选值**: `true`, `false`
- **默认值**: `false`

#### 25. meet_and_greet
- **类型**: `boolean`
- **必填**: ❌ 否
- **描述**: 是否需要接机服务
- **可选值**: `true`, `false`
- **默认值**: `false`

#### 26. extra_requirement
- **类型**: `string`
- **必填**: ❌ 否
- **描述**: 额外要求
- **长度限制**: 最大500个字符
- **示例**: "需要中文司机", "行李较多请准备大车"

#### 27. driving_region_id
- **类型**: `number`
- **必填**: ❌ 否
- **描述**: 驾驶区域ID
- **可选值**:
  - `1` - 吉隆坡/雪兰莪 (KL)
  - `2` - 槟城 (PNG)
  - `4` - 沙巴 (SBH)
  - `5` - 新加坡 (SG)
- **默认值**: 根据接客地点自动检测

#### 28. languages_id_array
- **类型**: `object`
- **必填**: ❌ 否
- **描述**: 语言ID数组 (转换为对象格式)
- **格式**: `{"0": "2", "1": "3"}` (对象格式)
- **可选值**:
  - `2` - 英语 (EN)
  - `3` - 马来语 (MY)
  - `4` - 中文 (CN)
- **转换规则**: 
  - 输入: `[2, 3]` (数组)
  - 输出: `{"0": "2", "1": "3"}` (对象)
- **智能检测**: 根据客户姓名自动检测语言偏好

## 🔄 数据预处理规则

### 字段名称映射
系统内部字段名到API字段名的映射：
```javascript
{
    'pickup_location': 'pickup',
    'dropoff_location': 'destination',
    'pickup_date': 'date',
    'pickup_time': 'time',
    'luggage_count': 'luggage_number',
    'passenger_count': 'passenger_number',
    'customerName': 'customer_name',
    'customerContact': 'customer_contact',
    'customerEmail': 'customer_email'
}
```

### 智能默认值生成

#### 1. 后台用户ID
```javascript
// 根据登录用户邮箱匹配
if (currentUser.email === '<EMAIL>') {
    incharge_by_backend_user_id = 37;
} else if (currentUser.email === '<EMAIL>') {
    incharge_by_backend_user_id = 310;
} else {
    incharge_by_backend_user_id = 1; // 默认Super Admin
}
```

#### 2. 语言数组
```javascript
// 根据客户姓名检测语言
const chineseRegex = /[\u4e00-\u9fff\u3400-\u4dbf\uf900-\ufaff]/;
if (customer_name && chineseRegex.test(customer_name)) {
    languages_id_array = {"0": "4"}; // 中文
} else {
    languages_id_array = {"0": "2"}; // 英文
}
```

#### 3. 车型推荐
```javascript
// 根据乘客数量推荐车型
if (passenger_number <= 3) {
    car_type_id = 5; // 5座车
} else if (passenger_number <= 5) {
    car_type_id = 15; // 7座MPV
} else if (passenger_number <= 7) {
    car_type_id = 20; // 10座MPV
} else {
    car_type_id = 5; // 默认5座车
}
```

#### 4. 服务类型检测
```javascript
// 根据地点信息检测服务类型
if (pickup.includes('KLIA') || pickup.includes('Airport')) {
    if (destination.includes('KLIA') || destination.includes('Airport')) {
        sub_category_id = 4; // 包车
    } else {
        sub_category_id = 2; // 接机
    }
} else if (destination.includes('KLIA') || destination.includes('Airport')) {
    sub_category_id = 3; // 送机
} else {
    sub_category_id = 4; // 包车
}
```

## 📤 请求示例

### 完整订单请求
```json
{
    "sub_category_id": 2,
    "ota_reference_number": "CD123456789",
    "car_type_id": 5,
    "incharge_by_backend_user_id": 37,
    "ota": "Chong Dealer",
    "ota_price": 150.00,
    "customer_name": "王小明",
    "customer_contact": "+60123456789",
    "customer_email": "<EMAIL>",
    "flight_info": "MH123 15:30",
    "pickup": "KLIA2 Terminal",
    "pickup_lat": 2.745578,
    "pickup_long": 101.709917,
    "date": "2025-07-20",
    "time": "15:30",
    "destination": "Kuala Lumpur Sentral",
    "destination_lat": 3.134623,
    "destination_long": 101.686377,
    "passenger_number": 2,
    "luggage_number": 3,
    "driver_fee": 120.00,
    "driver_collect": 30.00,
    "tour_guide": false,
    "baby_chair": false,
    "meet_and_greet": true,
    "extra_requirement": "需要中文司机",
    "driving_region_id": 1,
    "languages_id_array": {"0": "4", "1": "2"}
}
```

### 最简订单请求
```json
{
    "sub_category_id": 2,
    "ota_reference_number": "CD123456789",
    "car_type_id": 5,
    "incharge_by_backend_user_id": 1
}
```

## 📥 响应格式

### 成功响应
```json
{
    "status": true,
    "data": {
        "id": 12345,
        "order_id": "GMH-123456",
        "message": "Order created successfully"
    }
}
```

### 验证错误响应
```json
{
    "status": false,
    "message": "Data need to be refined",
    "data": {
        "validation_error": {
            "sub_category_id": ["The sub category id field is required."],
            "ota_reference_number": ["The ota reference number field is required."],
            "car_type_id": ["The car type id field is required."],
            "customer_contact": ["The customer contact field must be a valid phone number."]
        }
    }
}
```

### 重复订单错误
```json
{
    "status": false,
    "message": "Order already exists",
    "data": {
        "existing_order_id": "GMH-123456"
    }
}
```

## 🚨 错误处理

### 网络错误
- **错误类型**: 网络连接失败
- **处理**: 显示"网络连接失败，请检查网络连接"
- **重试**: 自动重试2次

### 验证错误
- **错误类型**: 字段验证失败
- **处理**: 显示具体字段错误信息
- **用户行为**: 修正错误字段后重新提交

### 重复订单
- **错误类型**: OTA参考号重复
- **处理**: 显示重复订单警告
- **用户行为**: 修改参考号或确认覆盖

### 超时错误
- **错误类型**: 请求超时 (>30秒)
- **处理**: 显示"请求超时，请稍后重试"
- **用户行为**: 重新提交订单

## 🔍 数据验证流程

### 客户端验证
1. **字段格式验证**: 邮箱、电话、日期格式
2. **必填字段检查**: 确保必填字段不为空
3. **数值范围验证**: 乘客数、行李数范围检查
4. **逻辑验证**: 车型与乘客数匹配检查

### 服务端验证
1. **数据库约束检查**: 唯一性约束、外键约束
2. **业务逻辑验证**: 服务类型与地点匹配
3. **权限验证**: 后台用户权限检查
4. **重复检查**: OTA参考号唯一性检查

## 🎯 最佳实践

### 数据收集
1. **智能表单**: 使用AI自动填充表单字段
2. **实时验证**: 输入时即时验证字段格式
3. **智能推荐**: 根据历史数据推荐车型和服务
4. **错误预防**: 在提交前进行完整性检查

### 性能优化
1. **批量处理**: 支持多订单批量创建
2. **缓存机制**: 缓存系统数据减少API调用
3. **异步处理**: 使用异步请求避免界面阻塞
4. **超时控制**: 设置合理的超时时间

### 用户体验
1. **进度反馈**: 显示订单创建进度
2. **错误提示**: 友好的错误信息显示
3. **成功确认**: 创建成功后显示订单详情
4. **历史记录**: 保存订单历史便于查看

## 📊 API使用统计

### 字段使用频率
- **必填字段**: 100% 使用
- **客户信息**: 95% 使用
- **地点信息**: 90% 使用
- **时间信息**: 85% 使用
- **特殊服务**: 30% 使用

### 常见错误
1. **OTA参考号格式错误**: 25%
2. **电话号码格式错误**: 20%
3. **日期格式错误**: 15%
4. **车型与乘客数不匹配**: 10%
5. **重复订单**: 5%

---

**文档版本**: v1.0  
**更新日期**: 2025-07-18  
**维护人**: Claude Code  
**状态**: 当前版本