/**
 * @OTA_CHANNEL_DETECTOR OTA渠道智能识别引擎
 * 🏷️ 标签: @OTA_CHANNEL_INTELLIGENT_DETECTION
 * 📝 说明: 基于参考码、关键词、文本特征智能识别OTA渠道
 * <AUTHOR>
 * @version 1.0.0
 */

(function() {
    'use strict';

    // 确保OTA命名空间存在
    window.OTA = window.OTA || {};

    /**
     * OTA渠道智能检测器
     */
    class OTAChannelDetector {
        constructor() {
            // 基于参考码模式的识别规则
            this.referencePatterns = {
                // Chong Dealer - CD开头的参考号
                chongDealer: {
                    pattern: /^CD[A-Z0-9]{6,12}$/i,
                    channel: 'Chong Dealer',
                    confidence: 0.95,
                    description: 'CD开头的参考号识别'
                },
                
                // 携程相关模式
                ctrip: {
                    pattern: /^CT[A-Z0-9]{6,10}$/i,
                    channel: 'Ctrip',
                    confidence: 0.9,
                    description: '携程参考号模式'
                },
                
                // Klook相关模式
                klook: {
                    pattern: /^KL[A-Z0-9]{6,10}$/i,
                    channel: 'Klook West Malaysia',
                    confidence: 0.9,
                    description: 'Klook参考号模式'
                },
                
                // KKday相关模式
                kkday: {
                    pattern: /^KK[A-Z0-9]{6,10}$/i,
                    channel: 'Kkday',
                    confidence: 0.9,
                    description: 'KKday参考号模式'
                },
                
                // Fliggy相关模式 - 订单编号 + 19位数字
                fliggy: {
                    pattern: /订单编号\d{19}/,
                    channel: 'Fliggy',
                    confidence: 0.95,
                    description: 'Fliggy订单编号识别模式'
                },
                
                // 通用OTA模式
                generic: {
                    pattern: /^[A-Z]{2,4}[0-9]{6,10}$/,
                    channel: null, // 需要进一步分析
                    confidence: 0.6,
                    description: '通用OTA参考号格式'
                }
            };

            // 基于关键词的识别规则
            this.keywordPatterns = {
                // 团号相关关键词
                groupNumber: {
                    keywords: ['团号', 'group number', 'tour code', '确认号'],
                    weight: 0.8,
                    description: '团号/确认号关键词识别'
                },
                
                // 渠道名称直接匹配
                channelNames: {
                    'chong dealer': { channel: 'Chong Dealer', confidence: 0.95 },
                    '携程': { channel: 'Ctrip', confidence: 0.9 },
                    'ctrip': { channel: 'Ctrip', confidence: 0.9 },
                    'klook': { channel: 'Klook West Malaysia', confidence: 0.9 },
                    'kkday': { channel: 'Kkday', confidence: 0.9 },
                    'fliggy': { channel: 'Fliggy', confidence: 0.85 },
                    'traveloka': { channel: 'Traveloka', confidence: 0.85 }
                },
                
                // 特殊服务关键词
                services: {
                    'meet and greet': { 
                        indicators: ['meet and greet', 'meet & greet', '举牌', '迎接服务'],
                        weight: 0.7 
                    },
                    'paging service': {
                        indicators: ['paging', 'paging service', '举牌服务', '接机牌'],
                        weight: 0.7
                    }
                }
            };

            // 文本特征模式识别
            this.textFeaturePatterns = {
                // 邮件格式特征
                emailFormat: {
                    pattern: /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g,
                    weight: 0.3
                },
                
                // 聊天记录格式特征
                chatFormat: {
                    pattern: /\[\d{4}\/\d{1,2}\/\d{1,2}\s+\d{1,2}:\d{2}\]/g,
                    weight: 0.5,
                    description: '聊天记录时间戳格式'
                },
                
                // 订单编号格式特征
                orderFormat: {
                    pattern: /订单[号编码][:：]\s*([A-Z0-9]+)/gi,
                    weight: 0.6
                },
                
                // 航班信息格式
                flightFormat: {
                    pattern: /[A-Z]{2}\d{2,4}/g,
                    weight: 0.4
                }
            };

            this.logger = this.getLogger();
        }

        /**
         * 智能检测OTA渠道
         * @param {string} text - 输入文本
         * @param {string} referenceNumber - 参考号（可选）
         * @param {object} context - 上下文信息（可选）
         * @returns {object} 检测结果
         */
        detectChannel(text, referenceNumber = null, context = {}) {
            const results = {
                detectedChannel: null,
                confidence: 0,
                method: null,
                details: [],
                allMatches: []
            };

            try {
                // 1. 基于参考号的检测
                if (referenceNumber) {
                    const refResult = this.detectByReference(referenceNumber);
                    if (refResult.confidence > results.confidence) {
                        Object.assign(results, refResult);
                        results.method = 'reference_pattern';
                    }
                    results.allMatches.push(refResult);
                }

                // 2. 专门的Fliggy文本内容检测
                const fliggyResult = this.detectFliggyFromContent(text);
                if (fliggyResult.confidence > results.confidence) {
                    Object.assign(results, fliggyResult);
                    results.method = 'fliggy_content_detection';
                }
                results.allMatches.push(fliggyResult);

                // 3. 基于关键词的检测
                const keywordResult = this.detectByKeywords(text);
                if (keywordResult.confidence > results.confidence) {
                    Object.assign(results, keywordResult);
                    results.method = 'keyword_matching';
                }
                results.allMatches.push(keywordResult);

                // 4. 基于文本特征的检测
                const featureResult = this.detectByTextFeatures(text);
                if (featureResult.confidence > results.confidence) {
                    Object.assign(results, featureResult);
                    results.method = 'text_features';
                }
                results.allMatches.push(featureResult);

                // 5. 综合分析提升置信度
                this.enhanceDetectionWithContext(results, context);

                this.logger.log('OTA渠道检测完成', 'info', {
                    检测到的渠道: results.detectedChannel,
                    置信度: results.confidence,
                    检测方法: results.method,
                    详细信息: results.details
                });

                return results;

            } catch (error) {
                this.logger.log('OTA渠道检测失败', 'error', { error: error.message });
                return results;
            }
        }

        /**
         * 专门的Fliggy内容检测
         * @param {string} text - 输入文本
         * @returns {object} 检测结果
         */
        detectFliggyFromContent(text) {
            const result = {
                detectedChannel: null,
                confidence: 0,
                details: []
            };

            if (!text || typeof text !== 'string') {
                return result;
            }

            // 检测"订单编号" + 19位数字的模式
            const fliggyPattern = /订单编号\d{19}/g;
            const matches = text.match(fliggyPattern);
            
            if (matches && matches.length > 0) {
                result.detectedChannel = 'Fliggy';
                result.confidence = 0.95;
                result.details.push({
                    type: 'fliggy_order_pattern',
                    pattern: '订单编号+19位数字',
                    matches: matches,
                    count: matches.length,
                    description: 'Fliggy订单编号特征识别'
                });
                
                this.logger.log('检测到Fliggy订单特征', 'info', {
                    匹配内容: matches,
                    匹配次数: matches.length
                });
            }

            return result;
        }

        /**
         * 基于参考号模式检测
         * @param {string} referenceNumber - 参考号
         * @returns {object} 检测结果
         */
        detectByReference(referenceNumber) {
            const result = {
                detectedChannel: null,
                confidence: 0,
                details: []
            };

            if (!referenceNumber || typeof referenceNumber !== 'string') {
                return result;
            }

            const cleanRef = referenceNumber.trim().toUpperCase();

            // 检查所有参考号模式
            for (const [patternName, config] of Object.entries(this.referencePatterns)) {
                if (config.pattern.test(cleanRef)) {
                    if (config.channel) {
                        result.detectedChannel = config.channel;
                        result.confidence = config.confidence;
                        result.details.push({
                            type: 'reference_pattern',
                            pattern: patternName,
                            description: config.description,
                            match: cleanRef
                        });
                        break;
                    } else {
                        // 通用格式需要进一步分析
                        result.details.push({
                            type: 'generic_pattern',
                            pattern: patternName,
                            description: config.description,
                            match: cleanRef,
                            needsAnalysis: true
                        });
                    }
                }
            }

            return result;
        }

        /**
         * 基于关键词检测
         * @param {string} text - 输入文本
         * @returns {object} 检测结果
         */
        detectByKeywords(text) {
            const result = {
                detectedChannel: null,
                confidence: 0,
                details: []
            };

            if (!text || typeof text !== 'string') {
                return result;
            }

            const lowerText = text.toLowerCase();

            // 检查渠道名称直接匹配
            for (const [keyword, config] of Object.entries(this.keywordPatterns.channelNames)) {
                if (lowerText.includes(keyword.toLowerCase())) {
                    if (config.confidence > result.confidence) {
                        result.detectedChannel = config.channel;
                        result.confidence = config.confidence;
                        result.details.push({
                            type: 'channel_name',
                            keyword: keyword,
                            confidence: config.confidence
                        });
                    }
                }
            }

            // 检查其他关键词模式
            for (const [patternName, config] of Object.entries(this.keywordPatterns)) {
                if (patternName === 'channelNames') continue;

                if (config.keywords) {
                    for (const keyword of config.keywords) {
                        if (lowerText.includes(keyword.toLowerCase())) {
                            result.details.push({
                                type: 'pattern_keyword',
                                pattern: patternName,
                                keyword: keyword,
                                weight: config.weight
                            });
                            // 增加基础置信度
                            result.confidence = Math.max(result.confidence, config.weight * 0.5);
                        }
                    }
                }

                if (config.indicators) {
                    for (const indicator of config.indicators) {
                        if (lowerText.includes(indicator.toLowerCase())) {
                            result.details.push({
                                type: 'service_indicator',
                                pattern: patternName,
                                indicator: indicator,
                                weight: config.weight
                            });
                        }
                    }
                }
            }

            return result;
        }

        /**
         * 基于文本特征检测
         * @param {string} text - 输入文本
         * @returns {object} 检测结果
         */
        detectByTextFeatures(text) {
            const result = {
                detectedChannel: null,
                confidence: 0,
                details: []
            };

            if (!text || typeof text !== 'string') {
                return result;
            }

            let totalWeight = 0;
            let maxWeight = 0;

            // 检查各种文本特征
            for (const [featureName, config] of Object.entries(this.textFeaturePatterns)) {
                const matches = text.match(config.pattern);
                if (matches && matches.length > 0) {
                    const weight = config.weight * Math.min(matches.length / 3, 1); // 限制最大权重
                    totalWeight += weight;
                    maxWeight = Math.max(maxWeight, weight);
                    
                    result.details.push({
                        type: 'text_feature',
                        feature: featureName,
                        matches: matches.length,
                        weight: weight,
                        description: config.description
                    });
                }
            }

            // 基于文本特征计算置信度
            result.confidence = Math.min(totalWeight * 0.3, 0.7); // 文本特征最高置信度为0.7

            return result;
        }

        /**
         * 使用上下文信息增强检测结果
         * @param {object} results - 检测结果
         * @param {object} context - 上下文信息
         */
        enhanceDetectionWithContext(results, context) {
            // 如果有用户ID或邮箱信息，检查现有映射
            if (context.userId || context.userEmail) {
                const existingMapping = this.getExistingChannelMapping(context.userId, context.userEmail);
                if (existingMapping) {
                    results.details.push({
                        type: 'existing_mapping',
                        channel: existingMapping.channel,
                        source: existingMapping.source,
                        confidence: 0.8
                    });
                    
                    // 如果现有映射置信度高，使用它
                    if (!results.detectedChannel || results.confidence < 0.8) {
                        results.detectedChannel = existingMapping.channel;
                        results.confidence = 0.8;
                        results.method = 'existing_mapping';
                    }
                }
            }

            // 根据组合特征提升置信度
            if (results.allMatches && results.allMatches.length > 1) {
                const hasMultipleIndicators = results.allMatches.filter(match => match.confidence > 0.3).length;
                if (hasMultipleIndicators >= 2) {
                    results.confidence = Math.min(results.confidence * 1.2, 0.95);
                    results.details.push({
                        type: 'multiple_indicators',
                        count: hasMultipleIndicators,
                        boost: 0.2
                    });
                }
            }
        }

        /**
         * 获取现有的渠道映射
         * @param {string|number} userId - 用户ID
         * @param {string} userEmail - 用户邮箱
         * @returns {object|null} 现有映射信息
         */
        getExistingChannelMapping(userId, userEmail) {
            try {
                // 检查现有的OTA渠道映射
                if (window.OTA && window.OTA.otaChannelMapping) {
                    const config = window.OTA.otaChannelMapping.getConfig(userId || userEmail);
                    if (config && config.default) {
                        return {
                            channel: config.default,
                            source: 'static_mapping',
                            confidence: 0.8
                        };
                    }
                }
                return null;
            } catch (error) {
                this.logger.log('获取现有渠道映射失败', 'error', { error: error.message });
                return null;
            }
        }

        /**
         * 获取所有可能的渠道选项
         * @param {string} detectedChannel - 检测到的渠道
         * @returns {array} 渠道选项列表
         */
        getChannelOptions(detectedChannel = null) {
            const options = [];

            // 如果检测到特定渠道，将其放在首位
            if (detectedChannel) {
                options.push({
                    value: detectedChannel,
                    text: detectedChannel,
                    confidence: 'detected',
                    recommended: true
                });
            }

            // 添加常用渠道选项
            if (window.OTA && window.OTA.otaChannelMapping && window.OTA.otaChannelMapping.commonChannels) {
                const commonChannels = window.OTA.otaChannelMapping.commonChannels;
                for (const channel of commonChannels) {
                    if (!detectedChannel || channel.value !== detectedChannel) {
                        options.push({
                            value: channel.value,
                            text: channel.text,
                            confidence: 'common'
                        });
                    }
                }
            }

            return options;
        }

        /**
         * 获取Logger实例
         * @returns {object} Logger实例
         */
        getLogger() {
            if (typeof getLogger === 'function') {
                return getLogger();
            }
            // 降级处理
            return {
                log: (message, level, data) => {
                    console.log(`[${level.toUpperCase()}] ${message}`, data || '');
                }
            };
        }
    }

    // 创建全局实例
    const otaChannelDetector = new OTAChannelDetector();

    // 暴露到OTA命名空间
    window.OTA.ChannelDetector = OTAChannelDetector;
    window.OTA.channelDetector = otaChannelDetector;

    // 注册到OTA注册中心
    if (window.OTA && window.OTA.Registry) {
        window.OTA.Registry.registerService('channelDetector', otaChannelDetector, '@OTA_CHANNEL_DETECTOR');
        window.OTA.Registry.registerFactory('getChannelDetector', () => otaChannelDetector, '@OTA_CHANNEL_DETECTOR_FACTORY');
    }

})();